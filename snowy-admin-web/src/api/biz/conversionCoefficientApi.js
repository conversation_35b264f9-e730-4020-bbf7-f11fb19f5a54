import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/conversionCoefficient/` + url, ...arg)

/**
 * 折合系数Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/17 15:44
 **/
export default {
	// 获取折合系数分页
	conversionCoefficientPage(data) {
		return request('page', data, 'get')
	},
	// 提交折合系数表单 edit为true时为编辑，默认为新增
	conversionCoefficientSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除折合系数
	conversionCoefficientDelete(data) {
		if (data.length>1){
			let data_new = []
			for (let i = 0; i < data.length; i++) {
				data_new.push({"objectRrn":data[i].id})
			}
			return request('delete', data_new)
		}else {
			return request('delete', data)
		}
	},
	// 获取折合系数详情
	conversionCoefficientExport(data) {
		return request('detail', data, 'get')
	},

	// 导出数据
	ExportData(data) {
		return request('conversionCoefficientExport', data, 'post', {
			responseType: 'blob'
		})
	}
}
