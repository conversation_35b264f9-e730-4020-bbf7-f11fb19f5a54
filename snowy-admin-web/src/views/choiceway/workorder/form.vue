<template>
	<a-modal
		v-model:open="visible"
		:title="formData.id ? '编辑工单' : '新增工单'"
		width="600px"
		:confirm-loading="submitLoading"
		@ok="onSubmit"
		@cancel="onClose"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="SN码" name="snCode">
						<a-input v-model:value="formData.snCode" placeholder="请输入SN码" />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="产品名称" name="productName">
						<a-input v-model:value="formData.productName" placeholder="请输入产品名称" />
					</a-form-item>
				</a-col>
			</a-row>
			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="流程" name="processId">
						<a-select 
							v-model:value="formData.processId" 
							placeholder="请选择流程"
							@change="onProcessChange"
						>
							<a-select-option 
								v-for="process in processList" 
								:key="process.id" 
								:value="process.id"
							>
								{{ process.processName }} ({{ process.version }})
							</a-select-option>
						</a-select>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="工单状态" name="status">
						<a-select v-model:value="formData.status" placeholder="请选择工单状态">
							<a-select-option value="CREATED">已创建</a-select-option>
							<a-select-option value="PROCESSING">处理中</a-select-option>
							<a-select-option value="COMPLETED">已完成</a-select-option>
						</a-select>
					</a-form-item>
				</a-col>
			</a-row>
			<a-row>
				<a-col :span="24">
					<a-form-item label="工单备注" name="remark">
						<a-textarea v-model:value="formData.remark" placeholder="请输入工单备注" :rows="3" />
					</a-form-item>
				</a-col>
			</a-row>
			<a-row>
				<a-col :span="24">
					<a-form-item label="排序码" name="sortCode">
						<a-input-number v-model:value="formData.sortCode" placeholder="请输入排序码" style="width: 100%" />
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
	</a-modal>
</template>

<script setup name="mesWorkOrderForm">
	import { message } from 'ant-design-vue'
	import mesWorkOrderApi from '@/api/choiceway/mesWorkOrderApi'
	import mesProcessApi from '@/api/choiceway/mesProcessApi'

	const emit = defineEmits(['successful'])

	const visible = ref(false)
	const submitLoading = ref(false)
	const formRef = ref()
	const processList = ref([])

	const formData = ref({})

	const formRules = {
		snCode: [{ required: true, message: '请输入SN码', trigger: 'blur' }],
		productName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
		processId: [{ required: true, message: '请选择流程', trigger: 'change' }]
	}

	// 获取启用的流程列表
	const getProcessList = () => {
		mesProcessApi.mesProcessEnabledList().then((data) => {
			processList.value = data
		})
	}

	// 流程选择变化
	const onProcessChange = (processId) => {
		const selectedProcess = processList.value.find(p => p.id === processId)
		if (selectedProcess) {
			formData.value.processName = selectedProcess.processName
			formData.value.processVersion = selectedProcess.version
		}
	}

	// 打开弹窗
	const onOpen = (record) => {
		visible.value = true
		getProcessList()
		if (record) {
			formData.value = Object.assign({}, record)
		} else {
			formData.value = {
				status: 'CREATED',
				sortCode: 100
			}
		}
	}

	// 关闭弹窗
	const onClose = () => {
		visible.value = false
		formRef.value.resetFields()
		formData.value = {}
	}

	// 默认成功
	const onSubmit = () => {
		formRef.value.validate().then(() => {
			submitLoading.value = true
			const submitFunction = formData.value.id ? mesWorkOrderApi.mesWorkOrderSubmitForm : mesWorkOrderApi.mesWorkOrderSubmitForm
			submitFunction(formData.value, !!formData.value.id)
				.then(() => {
					message.success('操作成功')
					emit('successful')
					onClose()
				})
				.finally(() => {
					submitLoading.value = false
				})
		})
	}

	// 抛出函数
	defineExpose({
		onOpen
	})
</script>
