<template>
	<xn-form-container
		:title="formData.id ? '编辑更新记录表' : '增加更新记录表'"
		:width="700"
		v-model:open="open"
		:destroy-on-close="true"
		@close="onClose"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
			<a-form-item label="标题：" name="title">
				<a-input v-model:value="formData.title" placeholder="请输入标题" allow-clear />
			</a-form-item>
			<a-form-item label="更新内容：" name="content">
				<a-input v-model:value="formData.content" placeholder="请输入更新内容" allow-clear />
			</a-form-item>
			<a-form-item label="下载链接：" name="url">
				<a-input v-model:value="formData.url" placeholder="请输入下载链接" allow-clear />
			</a-form-item>
			<a-form-item label="版本：" name="edition">
				<a-input v-model:value="formData.edition" placeholder="请输入版本" allow-clear />
			</a-form-item>
			<a-form-item label="备注：" name="remarks">
				<a-input v-model:value="formData.remarks" placeholder="请输入备注" allow-clear />
			</a-form-item>
		</a-form>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">关闭</a-button>
			<a-button type="primary" @click="onSubmit" :loading="submitLoading">保存</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="updatesForm">
	import { cloneDeep } from 'lodash-es'
	import { required } from '@/utils/formRules'
	import updatesApi from '@/api/biz/updatesApi'
	// 抽屉状态
	const open = ref(false)
	const emit = defineEmits({ successful: null })
	const formRef = ref()
	// 表单数据
	const formData = ref({})
	const submitLoading = ref(false)

	// 打开抽屉
	const onOpen = (record) => {
		open.value = true
		if (record) {
			let recordData = cloneDeep(record)
			formData.value = Object.assign({}, recordData)
		}
	}
	// 关闭抽屉
	const onClose = () => {
		formRef.value.resetFields()
		formData.value = {}
		open.value = false
	}
	// 默认要校验的
	const formRules = {
	}
	// 验证并提交数据
	const onSubmit = () => {
		formRef.value
			.validate()
			.then(() => {
				submitLoading.value = true
				const formDataParam = cloneDeep(formData.value)
				updatesApi
					.updatesSubmitForm(formDataParam, formDataParam.id)
					.then(() => {
						onClose()
						emit('successful')
					})
					.finally(() => {
					submitLoading.value = false
					})
			})
			.catch(() => {})
	}
	// 抛出函数
	defineExpose({
		onOpen
	})
</script>
