<template>
	<a-divider>{{ $t('login.signInOther') }}</a-divider>
	<div class="login-oauth layout-center">
		<a-space align="start">
<!--			<a @click="getLoginRenderUrl('gitee')">
				<GiteeIcon />
			</a>-->
			<a @click="getPromptingInformation('windows')">
				<win10 />
			</a>
			<a @click="getPromptingInformation('alipay')">
				<alipay />
			</a>
			<a @click="getPromptingInformation('UiwQq')">
				<BiTencentQq />
			</a>
			<a @click="getPromptingInformation('wechat')">
				<weChat />
			</a>
		</a-space>
	</div>
</template>

<script setup name="threeLogin">
	import thirdApi from '@/api/auth/thirdApi'
	import {message} from "ant-design-vue";

	const getLoginRenderUrl = (platform) => {
		const param = {
			platform: platform
		}
		thirdApi.thirdRender(param).then((data) => {
			window.location.href = data.authorizeUrl
		})
	}

	const getPromptingInformation = (platform) => {
		//消息显示此功能为实现
		//判断是否为微信
		if (platform === 'wechat') {
			message.warning('暂不支持微信登录')
		}else if(platform === 'windows'){
			message.warning('暂不支持win登录')
		}else if(platform === 'UiwQq'){
			message.warning('暂不支持QQ登录')
		}else if(platform === 'alipay'){
			message.warning('暂不支持支付宝登录')
		}else {
			message.warning('暂不支持此登录')
		}
	}

</script>

<style lang="less">
	a {
		/*间距*/
		margin: 0 10px;
	}
</style>
