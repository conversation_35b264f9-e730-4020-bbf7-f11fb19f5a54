
export default {
	common: {
		searchButton: 'search',
		resetButton: 'reset',
		addButton: 'add',
		editButton: 'edit',
		removeButton: 'delete',
		batchRemoveButton: 'batch Remove',
		detailButton: 'detail',
		searchKey: 'Search Key',
		imports: 'Import',
		more: 'More',
		export: 'Export'
	},
	model: {
		user: 'user',
		org: 'org',
		pos: 'pos',
		role: 'role',
		bizUser: 'bizUser'
	},
	login: {
		signInTitle: 'Sign in',
		forgetPassword: 'Forget password',
		signIn: 'Sign in',
		signInOther: 'Sign in with',
		accountPlaceholder: 'Please input a user account',
		accountError: 'Please input a user account',
		PWPlaceholder: 'Please input a password',
		PWError: 'Please input a password',
		validLaceholder: 'Please input a valid',
		validError: 'Please input a valid',
		accountPassword: 'Account Password',
		phoneSms: 'Phone SMS',
		phonePlaceholder: 'Please input a phone',
		smsCodePlaceholder: 'Please input a SMS code',
		getSmsCode: 'SMS code',
		machineValidation: 'Machine Validation',
		sendingSmsMessage: 'Sending SMS Message',
		newPwdPlaceholder: 'Please input a new password',
		backLogin: 'Back Login',
		restPassword: 'Rest Password',
		emailPlaceholder: 'Please input a email',
		emailCodePlaceholder: 'Please input a Email code',
		restPhoneType: 'For phone rest',
		restEmailType: 'For email rest'
	},
	user: {
		userStatus: 'User Status',
		resetPassword: 'Reset Password',
		role: 'Role',
		batchExportButton: 'Batch Export',
		grantRole: 'Grant Role',
		grantResource: 'Grant Resource',
		grantPermission: 'Grant Permission',
		exportUserInfo: 'Export UserInfo',
		placeholderNameAndSearchKey: 'Please enter your name or keyword',
		placeholderUserStatus: 'Please select status',
		popconfirmDeleteUser: 'Are you sure you want to delete it？',
		popconfirmResatUserPwd: 'Are you sure you want to reset？'
	}
}
