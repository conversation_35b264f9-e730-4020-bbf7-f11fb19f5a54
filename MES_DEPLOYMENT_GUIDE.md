# MES管理系统部署指南

## 部署步骤

### 1. 数据库初始化

#### 1.1 创建数据库表
执行以下SQL脚本创建MES系统所需的数据库表：

```bash
# 在MySQL数据库中执行
mysql -u root -p your_database_name < snowy-plugin/snowy-plugin-choiceway/src/main/resources/db/mes_tables.sql
```

#### 1.2 初始化菜单和权限数据
执行以下SQL脚本创建菜单和权限：

```bash
# 在MySQL数据库中执行
mysql -u root -p your_database_name < snowy-plugin/snowy-plugin-choiceway/src/main/resources/db/mes_menu_data.sql
```

**注意**：菜单SQL脚本已根据Snowy框架的实际表结构进行了调整：
- 使用 `sys_resource` 表存储菜单和按钮权限
- 使用 `sys_relation` 表存储角色权限关系
- 使用 `sys_role` 表存储角色信息

### 2. 后端部署

#### 2.1 确认依赖配置
确保以下文件已正确配置MES插件依赖：

- `pom.xml` - 主项目依赖管理
- `snowy-plugin-api/pom.xml` - API模块配置
- `snowy-plugin/pom.xml` - 插件模块配置
- `snowy-web-app/pom.xml` - Web应用依赖

#### 2.2 编译和启动
```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run
```

#### 2.3 验证部署
- 访问 http://localhost:82 确认应用启动成功
- 访问 http://localhost:82/doc.html 查看API文档
- 确认MES相关接口已正确加载

### 3. 前端配置

#### 3.1 前端文件结构
前端文件已完整创建：

**API接口文件**：
- `snowy-admin-web/src/api/choiceway/mesProcessApi.js`
- `snowy-admin-web/src/api/choiceway/mesWorkOrderApi.js`
- `snowy-admin-web/src/api/choiceway/mesBatchApi.js`
- `snowy-admin-web/src/api/choiceway/mesTransactionApi.js`

**页面组件文件**：
- `snowy-admin-web/src/views/choiceway/process/index.vue` - 流程管理页面
- `snowy-admin-web/src/views/choiceway/process/form.vue` - 流程表单组件
- `snowy-admin-web/src/views/choiceway/workorder/index.vue` - 工单管理页面
- `snowy-admin-web/src/views/choiceway/workorder/form.vue` - 工单表单组件
- `snowy-admin-web/src/views/choiceway/batch/index.vue` - 批次管理页面
- `snowy-admin-web/src/views/choiceway/station/index.vue` - 进出站管理页面
- `snowy-admin-web/src/views/choiceway/warehouse/index.vue` - 入库管理页面
- `snowy-admin-web/src/views/choiceway/transaction/index.vue` - 事务历史页面

#### 3.2 路由配置
需要在前端路由配置中添加MES模块路由：

```javascript
// 在 snowy-admin-web/src/router/index.js 中添加
{
  path: '/mes',
  component: Layout,
  children: [
    {
      path: 'process',
      component: () => import('@/views/choiceway/process/index.vue'),
      meta: { title: '流程设计' }
    },
    {
      path: 'workorder',
      component: () => import('@/views/choiceway/workorder/index.vue'),
      meta: { title: '工单管理' }
    },
    {
      path: 'batch',
      component: () => import('@/views/choiceway/batch/index.vue'),
      meta: { title: '批次管理' }
    },
    {
      path: 'station',
      component: () => import('@/views/choiceway/station/index.vue'),
      meta: { title: '进出站管理' }
    },
    {
      path: 'warehouse',
      component: () => import('@/views/choiceway/warehouse/index.vue'),
      meta: { title: '入库管理' }
    },
    {
      path: 'transaction',
      component: () => import('@/views/choiceway/transaction/index.vue'),
      meta: { title: '事务历史' }
    }
  ]
}
```

### 4. 权限配置

#### 4.1 用户角色分配
系统已自动创建以下角色：
- **超级管理员** (SUPER_ADMIN) - 拥有所有MES权限
- **MES操作员** (MES_OPERATOR) - 拥有基础操作权限（不包括删除）

#### 4.2 为用户分配角色
在系统管理 -> 用户管理中，为需要使用MES系统的用户分配相应角色。

### 5. 菜单结构

部署完成后，系统将显示以下菜单结构：

```
MES管理系统
├── 流程设计        # 流程图绘制、版本管理
├── 工单管理        # 工单创建、物料绑定
├── 批次管理        # 批次查询、状态管理
├── 进出站管理      # 批次进出站操作
├── 入库管理        # 批次入库操作
└── 事务历史        # 操作历史查询
```

### 6. 功能验证

#### 6.1 基础功能测试
1. **流程设计**：创建一个测试流程
2. **工单管理**：基于流程创建工单，绑定物料
3. **投料操作**：对工单进行投料，生成批次
4. **进出站**：使用批次ID进行进出站操作
5. **入库**：完成批次入库
6. **历史查询**：查看完整的操作历史

#### 6.2 权限验证
1. 使用不同角色用户登录
2. 验证权限控制是否正确
3. 确认按钮级权限生效

### 7. 常见问题

#### 7.1 菜单不显示
- 确认已执行菜单SQL脚本
- 检查用户是否分配了正确的角色
- 清除浏览器缓存重新登录

#### 7.2 API接口404错误
- 确认后端应用已正确启动
- 检查控制器类是否被Spring扫描到
- 验证权限注解是否正确

#### 7.3 权限验证失败
- 确认Sa-Token配置正确
- 检查权限标识是否与菜单数据一致
- 验证用户角色权限分配

### 8. 生产环境注意事项

#### 8.1 性能优化
- 为数据库表添加适当索引
- 配置数据库连接池参数
- 启用Redis缓存

#### 8.2 安全配置
- 修改默认密码
- 配置HTTPS
- 设置访问控制

#### 8.3 监控和日志
- 配置应用监控
- 设置日志级别
- 定期备份数据

## 技术支持

如遇到部署问题，请检查：
1. 数据库连接配置
2. 依赖版本兼容性
3. 权限配置正确性
4. 日志错误信息

部署完成后，您将拥有一个功能完整的MES管理系统！
