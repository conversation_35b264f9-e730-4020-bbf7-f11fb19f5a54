<template>
    <view class="slide-delete-container">
        <view class="slide-item" 
              @touchstart="touchStart" 
              @touchmove="touchMove" 
              @touchend="touchEnd"
              :style="{ transform: `translateX(${translateX}px)` }">
            <slot></slot>
        </view>
        
        <view class="delete-btn" 
              @tap="handleDelete"
              :style="{ width: deleteWidth + 'px' }">
            <uv-icon name="trash" size="20" color="#fff"></uv-icon>
            <text class="delete-text">删除</text>
        </view>
    </view>
</template>

<script>
export default {
    name: 'SlideDelete',
    props: {
        // 删除按钮宽度
        deleteWidth: {
            type: Number,
            default: 80
        },
        // 传递的数据
        data: {
            type: Object,
            default: () => ({})
        },
        // 索引
        index: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            startX: 0,
            translateX: 0,
            isDragging: false
        }
    },
    methods: {
        touchStart(e) {
            if (e.touches.length === 1) {
                this.startX = e.touches[0].clientX
                this.isDragging = true
            }
        },
        
        touchMove(e) {
            if (!this.isDragging || e.touches.length !== 1) return
            
            const moveX = e.touches[0].clientX
            const deltaX = this.startX - moveX
            
            if (deltaX > 0) {
                // 向左滑动
                this.translateX = Math.min(-deltaX, -this.deleteWidth)
            } else {
                // 向右滑动，回到原位
                this.translateX = 0
            }
        },
        
        touchEnd(e) {
            if (!this.isDragging) return
            
            this.isDragging = false
            const endX = e.changedTouches[0].clientX
            const deltaX = this.startX - endX
            
            // 如果滑动距离超过删除按钮宽度的一半，则显示删除按钮
            if (deltaX > this.deleteWidth / 2) {
                this.translateX = -this.deleteWidth
            } else {
                this.translateX = 0
            }
        },
        
        handleDelete() {
            this.$emit('delete', {
                index: this.index,
                data: this.data
            })
        },
        
        // 重置位置
        reset() {
            this.translateX = 0
        }
    }
}
</script>

<style scoped>
.slide-delete-container {
    position: relative;
    overflow: hidden;
    background: #fff;
    margin-bottom: 15rpx;
}

.slide-item {
    position: relative;
    z-index: 2;
    background: #fff;
    transition: transform 0.3s ease;
    width: 100%;
}

.delete-btn {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    background: #f56c6c;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1;
    border-radius: 0 8rpx 8rpx 0;
}

.delete-text {
    color: #fff;
    font-size: 22rpx;
    margin-top: 4rpx;
}
</style>
