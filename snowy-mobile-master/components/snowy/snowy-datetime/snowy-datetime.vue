<template>
	<view class="snowy-datetime">
		<view class="input" @click="clickInput" :class="{ 'input-disabled': props.disabled }">
			<view class="input-value" :class="{ 'input-value-border': props.border, 'input-value-disabled': props.disabled }">
				<view v-if="!$xeu.isEmpty(curSelData)">
					{{ curSelData }}
				</view>
				<view class="placeholder" v-else>
					{{ placeholder }}
				</view>
			</view>
		</view>
		<uv-datetime-picker ref="calendarRef" :date="curSelData" @confirm="confirm" />
	</view>
</template>
<script setup>
	import { reactive, ref, getCurrentInstance, watch, inject } from "vue"
	const { proxy } = getCurrentInstance()
	const emits = defineEmits(['update:modelValue', 'cancel', 'confirm',])
	const props = defineProps({
		modelValue: [String, Array],
		border: {
			type: Boolean,
			default: true,
			required: false
		},
		placeholder: {
			type: String,
			default: "请选择",
			required: false
		},
		disabled: {
			type: Boolean,
			default: false,
			required: false
		},
	})
	const curSelData = ref('')
	const calendarRef = ref()
	watch(() => props.modelValue, (newValue, oldValue) => {
		curSelData.value = props.modelValue
	}, {
		// deep: false,
		// immediate: true
	})
	console.log("curSelData123",curSelData)
	console.log("props.modelValue",props.modelValue+"")
	console.log("props.placeholder",props.placeholder+"")
	console.log("props.linkType",props.linkType)
	console.log("props",props)
	// curSelData.value = "2024-06-07 14:01:11"
	
	const clickInput = () => {
		calendarRef.value.open()
	}
	const confirm = (e) => {
		// curSelData.value = e.fulldate
		// console.log("curSelData",curSelData)
		// emits('update:modelValue', e.fulldate)
		// console.log("e.fulldate",e.fulldate)
		// console.log("e.value",e.value)
		// // 校验字段
		// uni.$uv.formValidate(e.value, "change")
		// console.log("proxy",proxy.value)
		 // 将时间戳转换为日期时间字符串
		    let date = new Date(e.value);
		    let year = date.getFullYear();
		    let month = String(date.getMonth() + 1).padStart(2, '0');
		    let day = String(date.getDate()).padStart(2, '0');
		    let hours = String(date.getHours()).padStart(2, '0');
		    let minutes = String(date.getMinutes()).padStart(2, '0');
		    let seconds = String(date.getSeconds()).padStart(2, '0');
		    let formattedDateTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		
		    // 将日期时间字符串赋值给 curSelData.value
		    curSelData.value = formattedDateTime;
		
		    console.log("curSelData", curSelData);
		    emits('update:modelValue', formattedDateTime); // 触发事件，传递格式化后的日期时间字符串
		    console.log("e.fulldate", e.fulldate);
		    console.log("e.value", e.value);
		    // 校验字段
		    uni.$uv.formValidate(e.value, "change");
		    console.log("proxy", proxy.value);
	}
</script>
<style lang="scss" scoped>
	.snowy-datetime {
		width: 100%;

		.input-disabled {
			pointer-events: none;
			background-color: rgb(247, 246, 246);

			.input-value-disabled {
				color: rgb(192, 192, 192);
			}
		}

		.input {
			.input-value {
				font-size: 25upx;
				line-height: 30upx;
				padding: 20upx;
				min-height: 30upx;

				.placeholder {
					color: $uni-secondary-color;
				}
			}

			.input-value-border {
				border: 1px solid $uni-border-2;
				border-radius: 5upx;
			}
		}
	}
</style>