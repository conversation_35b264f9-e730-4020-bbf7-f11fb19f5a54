## LoadMore 加载更多

> **组件名：uv-load-more**

此组件一般用于标识页面底部加载数据时的状态，共有三种状态：加载前、加载中、加载后。

### <a href="https://www.uvui.cn/components/loadMore.html" target="_blank">查看文档</a>

### [完整示例项目下载 | 关注更多组件](https://ext.dcloud.net.cn/plugin?name=uv-ui)

#### 如使用过程中有任何问题，或者您对uv-ui有一些好的建议，欢迎加入 uv-ui 交流群：<a href="https://ext.dcloud.net.cn/plugin?id=12287" target="_blank">uv-ui</a>、<a href="https://www.uvui.cn/components/addQQGroup.html" target="_blank">官方QQ群</a>
