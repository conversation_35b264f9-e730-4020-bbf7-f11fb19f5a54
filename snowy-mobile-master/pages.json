{
	"pages": [{
		"path": "pages/login",
		"style": {
			"navigationBarTitleText": "登录",
			"enablePullDownRefresh": false,
			//#ifdef H5
			"navigationStyle": "custom"
			//#endif
		}
	}, {
		"path": "pages/home/<USER>",
		"style": {
			"navigationBarTitleText": "兆维生产制造系统",
			//#ifdef H5
			"navigationStyle": "custom"
			//#endif
		}
	}, {
		"path": "pages/work/index",
		"style": {
			"navigationBarTitleText": "工作台",
			//#ifdef H5
			"navigationStyle": "custom"
			//#endif
		}
	}, {
		"path": "pages/biz/leaderboard/index",
		"style": {
			"navigationBarTitleText": "排行榜",
			"enablePullDownRefresh": true,
			//#ifdef H5
			"navigationStyle": "custom"
			//#endif
		}
	}, {
		"path": "pages/msg/index",
		"style": {
			"navigationBarTitleText": "消息",
			"enablePullDownRefresh": true,
			"onReachBottomDistance": 50,
			//#ifdef H5
			"navigationStyle": "custom"
			//#endif
		}
	},{
		"path": "pages/msg/detail",
		"style": {
			"navigationBarTitleText": "消息详情",
			//#ifdef H5
			"navigationStyle": "custom"
			//#endif
		}
	}, {
		"path": "pages/mine/index",
		"style": {
			"navigationBarTitleText": "我的",
			//#ifdef H5
			"navigationStyle": "custom"
			//#endif
		}
	}, {
		"path": "pages/biz/outputRegistration/index",
		"style": {
			"navigationBarTitleText": "产出登记管理",
			"enablePullDownRefresh": true,
			//#ifdef H5
			"navigationStyle": "custom"
			//#endif
		}
	}, {
		"path": "pages/biz/outputRegistration/form",
		"style": {
			"navigationBarTitleText": "产出登记管理",
			"enablePullDownRefresh": false,
			//#ifdef H5
			"navigationStyle": "custom"
			//#endif
		}
	},{
    "path": "pages/biz/conversionCoefficient/index",
    "style": {
        "navigationBarTitleText": "折合系数管理",
        "enablePullDownRefresh": true,
        //#ifdef H5
        "navigationStyle": "custom"
        //#endif
    }
}, {
    "path": "pages/biz/conversionCoefficient/form",
    "style": {
        "navigationBarTitleText": "折合系数管理",
        "enablePullDownRefresh": false,
        //#ifdef H5
        "navigationStyle": "custom"
        //#endif
    }
},{
    "path": "pages/biz/summaryOfOutputStatistics/index",
    "style": {
        "navigationBarTitleText": "汇总管理",
        "enablePullDownRefresh": true,
        //#ifdef H5
        "navigationStyle": "custom"
        //#endif
    }
}, {
    "path": "pages/biz/summaryOfOutputStatistics/form",
    "style": {
        "navigationBarTitleText": "汇总管理",
        "enablePullDownRefresh": false,
        //#ifdef H5
        "navigationStyle": "custom"
        //#endif
    }
},{
    "path": "pages/biz/updates/index",
    "style": {
        "navigationBarTitleText": "更新记录表管理",
        "enablePullDownRefresh": true,
        //#ifdef H5
        "navigationStyle": "custom"
        //#endif
    }
}, {
    "path": "pages/biz/updates/form",
    "style": {
        "navigationBarTitleText": "更新记录表管理",
        "enablePullDownRefresh": false,
        //#ifdef H5
        "navigationStyle": "custom"
        //#endif
    }
}, {
    "path": "pages/choiceway/batch/index",
    "style": {
        "navigationBarTitleText": "新建批次",
        "enablePullDownRefresh": false,
        //#ifdef H5
        "navigationStyle": "custom"
        //#endif
    }
}, {
    "path": "pages/choiceway/station/index",
    "style": {
        "navigationBarTitleText": "批次进出站",
        "enablePullDownRefresh": false,
        //#ifdef H5
        "navigationStyle": "custom"
        //#endif
    }
}, {
    "path": "pages/choiceway/history/index",
    "style": {
        "navigationBarTitleText": "批次历史",
        "enablePullDownRefresh": false,
        //#ifdef H5
        "navigationStyle": "custom"
        //#endif
    }
}, {
    "path": "pages/choiceway/warehouse/index",
    "style": {
        "navigationBarTitleText": "批次入库",
        "enablePullDownRefresh": false,
        //#ifdef H5
        "navigationStyle": "custom"
        //#endif
    } 
}, {
    "path": "pages/choiceway/regression/index",
    "style": {
        "navigationBarTitleText": "批量退步",
        "enablePullDownRefresh": false,
        //#ifdef H5
        "navigationStyle": "custom"
        //#endif
    }
}],
	"subPackages": [{
		"root": "pages/config",
		"pages": [{
			"path": "index",
			"style": {
				"navigationBarTitleText": "环境设置",
				"enablePullDownRefresh": false,
				//#ifdef H5
				"navigationStyle": "custom"
				//#endif
			}
		}, {
			"path": "form",
			"style": {
				"navigationBarTitleText": "环境管理",
				"enablePullDownRefresh": false,
				//#ifdef H5
				"navigationStyle": "custom"
				//#endif
			}
		}]
	}, {
		"root": "pages/common",
		"pages": [{
			"path": "webview/index",
			"style": {
				"navigationBarTitleText": "浏览网页",
				"enablePullDownRefresh": false,
				//#ifdef H5
				"navigationStyle": "custom"
				//#endif
			}
		}]
	}, {
		"root": "pages/biz/org",
		"pages": [{
			"path": "index",
			"style": {
				"navigationBarTitleText": "机构管理",
				"enablePullDownRefresh": false,
				//#ifdef H5
				"navigationStyle": "custom"
				//#endif
			}
		}, {
			"path": "form",
			"style": {
				"navigationBarTitleText": "机构管理",
				"enablePullDownRefresh": false,
				//#ifdef H5
				"navigationStyle": "custom"
				//#endif
			}
		}]
	}, {
		"root": "pages/biz/position",
		"pages": [{
			"path": "index",
			"style": {
				"navigationBarTitleText": "职位管理",
				"enablePullDownRefresh": true,
				//#ifdef H5
				"navigationStyle": "custom"
				//#endif
			}
		}, {
			"path": "form",
			"style": {
				"navigationBarTitleText": "职位管理",
				"enablePullDownRefresh": false,
				//#ifdef H5
				"navigationStyle": "custom"
				//#endif
			}
		}]
	}, {
		"root": "pages/biz/user",
		"pages": [{
			"path": "index",
			"style": {
				"navigationBarTitleText": "用户管理",
				"enablePullDownRefresh": true,
				"onReachBottomDistance": 50,
				//#ifdef H5
				"navigationStyle": "custom"
				//#endif
			}
		}, {
			"path": "form",
			"style": {
				"navigationBarTitleText": "用户管理",
				"enablePullDownRefresh": false,
				"onReachBottomDistance": 50,
				//#ifdef H5
				"navigationStyle": "custom"
				//#endif
			}
		}]
	}, {
		"root": "pages/mine/info",
		"pages": [{
			"path": "index",
			"style": {
				"navigationBarTitleText": "个人信息",
				"enablePullDownRefresh": false,
				//#ifdef H5
				"navigationStyle": "custom"
				//#endif
			}
		}, {
			"path": "edit",
			"style": {
				"navigationBarTitleText": "编辑资料",
				"enablePullDownRefresh": false,
				//#ifdef H5
				"navigationStyle": "custom"
				//#endif
			}
		}]
	}, {
		"root": "pages/mine/home-config",
		"pages": [{
			"path": "index",
			"style": {
				"navigationBarTitleText": "首页设置",
				"enablePullDownRefresh": false,
				//#ifdef H5
				"navigationStyle": "custom"
				//#endif
			}
		}]
	}, {
		"root": "pages/mine/pwd",
		"pages": [{
			"path": "index",
			"style": {
				"navigationBarTitleText": "修改密码",
				"enablePullDownRefresh": false,
				//#ifdef H5
				"navigationStyle": "custom"
				//#endif
			}
		}]
	}],
	"tabBar": {
		"color": "#000000",
		"selectedColor": "#000000",
		"borderStyle": "white",
		"backgroundColor": "#ffffff",
		"list": [{
			"pagePath": "pages/home/<USER>",
			"iconPath": "static/images/tabbar/home.png",
			"selectedIconPath": "static/images/tabbar/home_.png",
			"text": "首页"
		}, 
		// {
		// 	"pagePath": "pages/biz/leaderboard/index",
		// 	"iconPath": "static/images/tabbar/home.png",
		// 	"selectedIconPath": "static/images/tabbar/home_.png",
		// 	"text": "排行榜"
		// }, 
		{
			"pagePath": "pages/work/index",
			"iconPath": "static/images/tabbar/work.png",
			"selectedIconPath": "static/images/tabbar/work_.png",
			"text": "工作台"
		}, {
			"pagePath": "pages/msg/index",
			"iconPath": "static/images/tabbar/msg.png",
			"selectedIconPath": "static/images/tabbar/msg_.png",
			"text": "消息"
		}, {
			"pagePath": "pages/mine/index",
			"iconPath": "static/images/tabbar/mine.png",
			"selectedIconPath": "static/images/tabbar/mine_.png",
			"text": "我的"
		}]
	},
	"easycom": {
		"autoscan": true,
		"custom": {
			"tui-(.*)": "@/components/thorui/thorui/tui-$1/tui-$1.vue",
			"snowy-(.*)": "@/components/snowy/snowy-$1/snowy-$1.vue"
		}
	},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"uniIdRouter": {}
}