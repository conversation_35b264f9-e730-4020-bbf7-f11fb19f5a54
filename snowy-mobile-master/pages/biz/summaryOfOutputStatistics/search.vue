<template>
    <uv-popup ref="popRef" mode="bottom" bg-color="null" z-index="99">
        <view class="container">
            <view class="close">
                <icon type="clear" :size="20" color="#5677fc" @click="close"></icon>
            </view>
            <uv-form ref="formRef" :model="searchFormState" label-position="top" labelWidth="auto" :labelStyle="{marginBottom: '25rpx', fontSize: '27rpx', color: '#606266'}">
                <uv-form-item label="日期" prop="date">
                    <uv-input v-model="searchFormState.date" placeholder="请输入日期"></uv-input>
                </uv-form-item>
                <uv-form-item label="工号" prop="nameId">
                    <uv-input v-model="searchFormState.nameId" placeholder="请输入工号"></uv-input>
                </uv-form-item>
                <uv-form-item label="姓名" prop="name">
                    <uv-input v-model="searchFormState.name" placeholder="请输入姓名"></uv-input>
                </uv-form-item>
                <uv-form-item label="等级" prop="grade">
                    <uv-input v-model="searchFormState.grade" placeholder="请输入等级"></uv-input>
                </uv-form-item>
                <uv-form-item label="排名" prop="ranking">
                    <uv-input v-model="searchFormState.ranking" placeholder="请输入排名"></uv-input>
                </uv-form-item>
            </uv-form>
            <uv-row :gutter="10">
                <uv-col :span="6">
                    <tui-button height="90rpx" type="gray" @click="reset">重置</tui-button>
                </uv-col>
                <uv-col :span="6">
                    <tui-button height="90rpx" type="primary" @click="confirm">确认</tui-button>
                </uv-col>
            </uv-row>
        </view>
    </uv-popup>
</template>
<script setup name="zwSummaryOfOutputStatisticsSearch">
    import { ref } from "vue"
    const emits = defineEmits(['reset', 'confirm'])
    const props = defineProps({
    	searchFormState: {
            type: Object,
            required: true
    	},
    })
    const popRef = ref()
    const open = () => {
    	popRef.value.open()
    }
    const reset = () =>{
        // 重置数据
        props.searchFormState.date = ''
        props.searchFormState.nameId = ''
        props.searchFormState.name = ''
        props.searchFormState.grade = ''
        props.searchFormState.ranking = ''
    	emits('reset', props.searchFormState)
    }
    const confirm = () => {
    	popRef.value.close()
    	emits('confirm', props.searchFormState)
    }
    const close = () => {
        popRef.value.close()
    }
    defineExpose({
    	open
    })
</script>
<style lang="scss" scoped>
    .container {
        margin: 5rpx;
        border-radius: 10rpx;
        padding: 20rpx;
        background-color: white;
        .close {
            display: flex;
            justify-content: flex-end;
        }
    }
</style>
