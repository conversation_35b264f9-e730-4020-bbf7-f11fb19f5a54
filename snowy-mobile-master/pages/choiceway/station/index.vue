<!--批次进出站管理-->
<template>
    <view class="station-container">
        <!-- 页面标题 -->
        <!-- <view class="page-header">
            <view class="header-left" @tap="goBack">
                <uv-icon name="arrow-left" size="24" color="#333"></uv-icon>
            </view>
            <view class="header-center">
                <uv-icon name="/static/images/choiceway/station.png" size="24" color="#5677fc"></uv-icon>
                <text class="page-title">批次过站</text>
            </view>
            <view class="header-right"></view>
        </view> -->

        <!-- 搜索区域 -->
        <view class="search-section">
            <view class="input-wrapper">
                <uv-icon class="icon" name="scan" size="20" color="#999"></uv-icon>
                <input
                    v-model="searchLotId"
                    class="input"
                    type="text"
                    placeholder="请输入批次ID"
                    maxlength="50"
                    @confirm="searchBatch"
                />
                <view class="scan-btn" @tap="scanCode">
                    <uv-icon name="scan" size="20" color="#5677fc"></uv-icon>
                </view>
            </view>

            <!-- 操作按钮 -->
            <view class="action-buttons">
                <uv-button
                    type="success"
                    @tap="enterStation"
                    :disabled="!batchInfo || batchInfo.status === 'RUN' || batchInfo.status === 'SHIP' || operating"
                    :loading="operating && operationType === 'enter'"
                    customStyle="width: 90px; height: 45px; border-radius: 25rpx; font-size: 28rpx; font-weight: 500; background: linear-gradient(135deg, #28a745, #20c997); border: none;"
                >
                    进站
                </uv-button>

                <uv-button
                    type="primary"
                    @tap="searchBatch"
                    :disabled="!searchLotId.trim() || searching"
                    :loading="searching"
                    customStyle="width: 90px; height: 45px; border-radius: 25rpx; font-size: 28rpx; font-weight: 500;"
                >
                    {{ searching ? '查询中' : '查询' }}
                </uv-button>

                <uv-button
                    type="warning"
                    @tap="exitStation"
                    :disabled="!batchInfo || batchInfo.status === 'WAIT' || batchInfo.status === 'SHIP' || operating"
                    :loading="operating && operationType === 'exit'"
                    customStyle="width: 90px; height: 45px; border-radius: 25rpx; font-size: 28rpx; font-weight: 500; background: linear-gradient(135deg, #ffc107, #fd7e14); border: none;"
                >
                    出站
                </uv-button>

                <uv-button
                    type="default"
                    @tap="clearBatchList"
                    :disabled="operating || batchList.length === 0"
                    customStyle="width: 90px; height: 45px; border-radius: 25rpx; font-size: 28rpx; font-weight: 500; background: linear-gradient(135deg, #6c757d, #495057); color: #fff; border: none;"
                >
                    清空
                </uv-button>
            </view>
        </view>
        
        <!-- 批次基本信息区块 -->
        <view class="batch-info" v-if="batchInfo">
            <view class="info-title">批次基本信息</view>

            <uv-row customStyle="margin-bottom: 15rpx">
                <uv-col :span="4">
                    <view class="info-label">批次ID:</view>
                </uv-col>
                <uv-col :span="8">
                    <view class="info-value">{{ batchInfo.lotId }}</view>
                </uv-col>
            </uv-row>

            <uv-row customStyle="margin-bottom: 15rpx">
                <uv-col :span="4">
                    <view class="info-label">产品名称:</view>
                </uv-col>
                <uv-col :span="8">
                    <view class="info-value">{{ batchInfo.productName }}</view>
                </uv-col>
            </uv-row>

            <uv-row customStyle="margin-bottom: 15rpx">
                <uv-col :span="4">
                    <view class="info-label">流程:</view>
                </uv-col>
                <uv-col :span="8">
                    <view class="info-value">{{ batchInfo.processName || '未设置' }}</view>
                </uv-col>
            </uv-row>

            <uv-row customStyle="margin-bottom: 15rpx">
                <uv-col :span="4">
                    <view class="info-label">站点:</view>
                </uv-col>
                <uv-col :span="8">
                    <view class="info-value">{{ batchInfo.currentStationName || '未设置' }}</view>
                </uv-col>
            </uv-row>

            <uv-row customStyle="margin-bottom: 15rpx">
                <uv-col :span="4">
                    <view class="info-label">数量:</view>
                </uv-col>
                <uv-col :span="8">
                    <view class="info-value">{{ batchInfo.particleCount || 0 }}</view>
                </uv-col>
            </uv-row>

            <uv-row customStyle="margin-bottom: 15rpx">
                <uv-col :span="4">
                    <view class="info-label">状态:</view>
                </uv-col>
                <uv-col :span="8">
                    <uv-tags
                        :text="getStatusText(batchInfo.status)"
                        :type="getStatusType(batchInfo.status)"
                        size="mini"
                    ></uv-tags>
                </uv-col>
            </uv-row>
        </view>

        
        <!-- 空状态 -->
        <uv-empty 
            v-if="!batchInfo && !searching" 
            text="请输入批次ID进行搜索"
            icon="search"
        ></uv-empty>
        
        <!-- 进出站操作弹窗 -->
        <uv-modal
            ref="operationModal"
            :title="operationType === 'enter' ? '批次进站' : '批次出站'"
            @confirm="confirmOperation"
            @cancel="cancelOperation"
            :showCancelButton="true"
            width="100%"
            :customStyle="{ minHeight: '400rpx' }"
        >
            <view class="modal-content">
                <!-- 颗粒数输入 -->
                <view class="form-item particle-count-item" style="display: flex;">
                    <view class="form-label" style="width: 6.125rem;">颗粒数：<text class="required">*</text></view>
                    <view class="particle-count-input">
                        <view class="count-btn decrease-btn" @tap="decreaseCount">
                            <uv-icon name="minus" size="16" color="#666"></uv-icon>
                        </view>
                        <uv-input
                            v-model="operationParticleCount"
                            placeholder="请输入颗粒数"
                            type="number"
                            :maxlength="10"
                            class="count-input"
                            @input="onParticleCountInput"
                        ></uv-input>
                        <view class="count-btn increase-btn" @tap="increaseCount">
                            <uv-icon name="plus" size="16" color="#666"></uv-icon>
                        </view>
                    </view>
                </view>

                <!-- 变化原因输入（当颗粒数发生变化时显示） -->
                <view class="form-item change-reason-item" v-if="batchInfo && parseInt(operationParticleCount) !== (batchInfo.particleCount || 0)">
                    <view class="form-label">变化原因 <text class="required">*</text></view>
                    <uv-textarea
                        v-model="operationScrapReason"
                        placeholder="请说明颗粒数变化原因"
                        :maxlength="200"
                        count
                        :autoHeight="false"
                        height="120rpx"
                    ></uv-textarea>
                    <view class="form-tip">
                        颗粒数从 {{ batchInfo.particleCount || 0 }} 变为 {{ operationParticleCount || 0 }}，请说明变化原因
                    </view>
                </view>

                <!-- 备注输入 -->
                <view class="form-item">
                    <view class="form-label">备注</view>
                    <uv-textarea
                        v-model="operationRemark"
                        placeholder="请输入操作备注（可选）"
                        :maxlength="200"
                        count
                        :autoHeight="true"
                    ></uv-textarea>
                </view>
            </view>
        </uv-modal>
    </view>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, nextTick } from 'vue'
import { searchBatchByLotId, batchEnterByLotId, batchExitByLotId } from '@/api/choiceway/mesBatchApi.js'

const { proxy } = getCurrentInstance()

// 响应式数据
const searchLotId = ref('')
const batchInfo = ref(null)
const searching = ref(false)
const operating = ref(false)
const operationType = ref('') // 'enter' 或 'exit'
const operationRemark = ref('')
const operationParticleCount = ref('')
const operationScrapReason = ref('')

// 弹窗ref
const operationModal = ref(null)

// 页面加载时执行
onMounted(() => {
    console.log('批次进出站页面加载完成')
    // 如果有传入的lotId参数，这里需要通过其他方式获取
    // 在Vue 3中，可以通过useRoute来获取路由参数
})

// 返回上一页
const goBack = () => {
    uni.navigateBack()
}


// 取消操作
const cancelOperation = () => {
    console.log('=== 取消操作 ===')
    console.log('取消前 operationType.value:', operationType.value)

    if (operationModal.value) {
        operationModal.value.close()
        console.log('弹窗已关闭')
    }

    operationType.value = ''
    operationRemark.value = ''
    operationParticleCount.value = ''
    operationScrapReason.value = ''

    console.log('=== 取消操作完成 ===')
}

// 颗粒数减少
const decreaseCount = () => {
    console.log('颗粒数减少按钮点击')
    let currentCount = parseInt(operationParticleCount.value) || 0
    if (currentCount > 0) {
        currentCount -= 1
        operationParticleCount.value = currentCount.toString()
        console.log('颗粒数减少后:', operationParticleCount.value)
    } else {
        console.log('颗粒数已经是0，无法继续减少')
        uni.showToast({
            title: '颗粒数不能小于0',
            icon: 'none',
            duration: 1500
        })
    }
}

// 颗粒数增加
const increaseCount = () => {
    console.log('颗粒数增加按钮点击')
    let currentCount = parseInt(operationParticleCount.value) || 0
    currentCount += 1
    operationParticleCount.value = currentCount.toString()
    console.log('颗粒数增加后:', operationParticleCount.value)
}

// 颗粒数输入处理
const onParticleCountInput = (value) => {
    console.log('颗粒数输入:', value)
    // 确保输入的是有效数字
    const numValue = parseInt(value) || 0
    if (numValue < 0) {
        operationParticleCount.value = '0'
        uni.showToast({
            title: '颗粒数不能小于0',
            icon: 'none',
            duration: 1500
        })
    }
}

// 扫码功能
const scanCode = () => {
    console.log('=== 扫码功能开始 ===')
    console.log('调用uni.scanCode...')

    uni.scanCode({
        success: (res) => {
            console.log('=== 扫码成功 ===')
            console.log('扫码结果:', res)
            console.log('扫码内容:', res.result)
            console.log('扫码类型:', res.scanType)
            console.log('字符集:', res.charSet)
            console.log('路径:', res.path)

            searchLotId.value = res.result
            console.log('批次ID已设置为:', searchLotId.value)

            // 扫码成功后自动查询批次信息
            console.log('扫码成功，开始自动查询批次信息...')
            searchBatch()
            console.log('=== 扫码成功处理完成 ===')
        },
        fail: (err) => {
            console.error('=== 扫码失败 ===')
            console.error('失败原因:', err)
            console.error('错误码:', err.errCode)
            console.error('错误信息:', err.errMsg)
            console.error('完整错误对象:', err)
            console.error('=== 扫码失败结束 ===')

            uni.showToast({
                title: '扫码失败',
                icon: 'none'
            })
        }
    })
    console.log('uni.scanCode调用完成')
}
// 搜索批次
const searchBatch = async () => {
    console.log('=== 搜索批次开始 ===')
    console.log('输入的批次ID:', searchLotId.value)

    if (!searchLotId.value.trim()) {
        console.error('批次ID为空')
        uni.showToast({
            title: '请输入批次ID',
            icon: 'none'
        })
        return
    }

    const trimmedLotId = searchLotId.value.trim()
    console.log('处理后的批次ID:', trimmedLotId)
    console.log('开始搜索批次...')

    searching.value = true
    batchInfo.value = null

    try {
        console.log('调用搜索API: searchBatchByLotId')
        const res = await searchBatchByLotId(trimmedLotId)

        console.log('=== 搜索API响应 ===')
        console.log('完整响应:', res)
        console.log('响应码:', res?.code)
        console.log('响应消息:', res?.message)
        console.log('响应数据:', res?.data)
        console.log('记录数量:', res?.data?.records?.length)
        console.log('=== 搜索API响应结束 ===')

        if (res.code === 200 && res.data && res.data.records && res.data.records.length > 0) {
            // 取第一个匹配的批次（通常lotId是唯一的）
            batchInfo.value = res.data.records[0]
            console.log('=== 批次信息获取成功 ===')
            console.log('批次ID:', batchInfo.value.id)
            console.log('批次LOT ID:', batchInfo.value.lotId)
            console.log('产品名称:', batchInfo.value.productName)
            console.log('流程名称:', batchInfo.value.processName)
            console.log('当前站点:', batchInfo.value.currentStationName)
            console.log('当前状态:', batchInfo.value.status)
            console.log('颗粒数:', batchInfo.value.particleCount)
            console.log('完整批次信息:', batchInfo.value)
            console.log('=== 批次信息获取成功结束 ===')
        } else {
            console.error('未找到批次或响应格式错误')
            console.error('响应码:', res?.code)
            console.error('响应消息:', res?.message)
            console.error('数据结构:', res?.data)

            batchInfo.value = null
            uni.showToast({
                title: '未找到该批次',
                icon: 'none'
            })
        }
    } catch (error) {
        console.error('=== 搜索批次异常 ===')
        console.error('异常类型:', error.constructor.name)
        console.error('异常消息:', error.message)
        console.error('异常堆栈:', error.stack)
        console.error('完整异常对象:', error)
        console.error('=== 搜索批次异常结束 ===')

        batchInfo.value = null
        uni.showToast({
            title: '搜索失败',
            icon: 'none'
        })
    } finally {
        searching.value = false
        console.log('搜索状态重置完成')
        console.log('=== 搜索批次结束 ===')
    }
}

// 清空搜索
const clearSearch = () => {
    console.log('清空搜索')
    searchLotId.value = ''
    batchInfo.value = null
}

// 进站操作
const enterStation = () => {
    console.log('=== 进站操作开始 ===')
    console.log('当前批次信息:', batchInfo.value)

    if (!batchInfo.value) {
        console.error('批次信息为空，无法进行进站操作')
        uni.showToast({
            title: '请先查询批次信息',
            icon: 'none'
        })
        return
    }

    console.log('批次状态检查:', batchInfo.value.status)
    if (batchInfo.value.status !== 'WAIT') {
        console.error('批次状态不是WAIT，当前状态:', batchInfo.value.status)
        uni.showToast({
            title: '批次状态不是等待状态，无法进站',
            icon: 'none'
        })
        return
    }

    operationType.value = 'enter'
    operationRemark.value = ''
    operationParticleCount.value = batchInfo.value?.particleCount?.toString() || ''
    operationScrapReason.value = '作业损失'

    console.log('进站操作参数初始化完成:')
    console.log('- 操作类型:', operationType.value)
    console.log('- 当前颗粒数:', operationParticleCount.value)
    console.log('- 备注:', operationRemark.value)
    console.log('- 报废原因:', operationScrapReason.value)

    console.log('准备打开进站弹窗...')
    console.log('operationModal.value:', operationModal.value)

    if (operationModal.value) {
        operationModal.value.open()
        console.log('进站弹窗已打开')
    } else {
        console.error('operationModal ref 为空')
    }

    console.log('=== 进站操作初始化完成 ===')
}

// 出站操作
const exitStation = () => {
    console.log('=== 出站操作开始 ===')
    console.log('当前批次信息:', batchInfo.value)

    if (!batchInfo.value) {
        console.error('批次信息为空，无法进行出站操作')
        uni.showToast({
            title: '请先查询批次信息',
            icon: 'none'
        })
        return
    }

    console.log('批次状态检查:', batchInfo.value.status)
    if (batchInfo.value.status !== 'RUN') {
        console.error('批次状态不是RUN，当前状态:', batchInfo.value.status)
        uni.showToast({
            title: '批次状态不是运行状态，无法出站',
            icon: 'none'
        })
        return
    }

    operationType.value = 'exit'
    operationRemark.value = ''
    operationParticleCount.value = batchInfo.value?.particleCount?.toString() || ''
    operationScrapReason.value = '作业损失'

    console.log('出站操作参数初始化完成:')
    console.log('- 操作类型:', operationType.value)
    console.log('- 当前颗粒数:', operationParticleCount.value)
    console.log('- 备注:', operationRemark.value)
    console.log('- 报废原因:', operationScrapReason.value)

    console.log('准备打开出站弹窗...')
    console.log('operationModal.value:', operationModal.value)

    if (operationModal.value) {
        operationModal.value.open()
        console.log('出站弹窗已打开')
    } else {
        console.error('operationModal ref 为空')
    }

    console.log('=== 出站操作初始化完成 ===')
}
// 确认操作
const confirmOperation = async () => {
    console.log('=== 确认操作开始 ===')
    console.log('操作类型:', operationType.value)
    console.log('当前批次信息:', batchInfo.value)
    console.log('输入的颗粒数:', operationParticleCount.value)
    console.log('报废原因:', operationScrapReason.value)
    console.log('操作备注:', operationRemark.value)

    // 验证颗粒数
    console.log('开始验证颗粒数...')
    if (!operationParticleCount.value || operationParticleCount.value.trim() === '') {
        console.error('颗粒数为空')
        uni.showToast({
            title: '请输入颗粒数',
            icon: 'none'
        })
        return
    }

    const particleCount = parseInt(operationParticleCount.value)
    console.log('解析后的颗粒数:', particleCount)

    if (isNaN(particleCount) || particleCount < 0) {
        console.error('颗粒数无效:', particleCount)
        uni.showToast({
            title: '请输入有效的颗粒数',
            icon: 'none'
        })
        return
    }

    // 检查颗粒数是否发生变化，如果变化则必须填写报废原因
    const originalCount = batchInfo.value.particleCount || 0
    console.log('原始颗粒数:', originalCount, '输入颗粒数:', particleCount)

    if (particleCount !== originalCount) {
        console.log('颗粒数发生变化，检查是否填写了变化原因...')
        if (!operationScrapReason.value || operationScrapReason.value.trim() === '') {
            console.error('颗粒数发生变化但未填写变化原因')
            uni.showToast({
                title: `颗粒数发生变化（从${originalCount}变为${particleCount}），必须填写变化原因`,
                icon: 'none'
            })
            return
        }
        console.log('变化原因已填写:', operationScrapReason.value.trim())
    } else {
        console.log('颗粒数未发生变化')
    }

    console.log('所有验证通过，开始执行操作...')

    // 关闭弹窗
    if (operationModal.value) {
        operationModal.value.close()
        console.log('弹窗已关闭')
    }

    operating.value = true

    try {
        const params = {
            batchId: batchInfo.value.id,
            lotId: batchInfo.value.lotId,
            particleCount: particleCount,
            scrapReason: operationScrapReason.value.trim(),
            remark: operationRemark.value.trim()
        }

        console.log('=== API调用参数 ===')
        console.log('batchId:', params.batchId)
        console.log('lotId:', params.lotId)
        console.log('particleCount:', params.particleCount)
        console.log('scrapReason:', params.scrapReason)
        console.log('remark:', params.remark)
        console.log('=== API调用参数结束 ===')

        let res
        if (operationType.value === 'enter') {
            console.log('调用进站API: batchEnterByLotId')
            res = await batchEnterByLotId(params)
        } else if (operationType.value === 'exit') {
            console.log('调用出站API: batchExitByLotId')
            res = await batchExitByLotId(params)
        } else {
            console.error('未知的操作类型:', operationType.value)
            throw new Error('未知的操作类型: ' + operationType.value)
        }

        console.log('=== API响应 ===')
        console.log('完整响应:', res)
        console.log('响应码:', res?.code)
        console.log('响应消息:', res?.message)
        console.log('响应数据:', res?.data)
        console.log('=== API响应结束 ===')

        if (res.code === 200) {
            console.log('操作成功！')
            uni.showToast({
                title: operationType.value === 'enter' ? '进站成功' : '出站成功',
                icon: 'success'
            })

            // 刷新批次信息
            console.log('1秒后刷新批次信息...')
            setTimeout(() => {
                console.log('开始刷新批次信息')
                searchBatch()
            }, 1000)
        } else {
            console.error('操作失败，错误信息:', res.message)
            uni.showToast({
                title: res.message || '操作失败',
                icon: 'none'
            })
        }
    } catch (error) {
        console.error('=== API调用异常 ===')
        console.error('异常类型:', error.constructor.name)
        console.error('异常消息:', error.message)
        console.error('异常堆栈:', error.stack)
        console.error('完整异常对象:', error)
        console.error('=== API调用异常结束 ===')

        uni.showToast({
            title: '操作失败',
            icon: 'none'
        })
    } finally {
        console.log('操作完成，重置状态...')
        operating.value = false
        operationType.value = ''
        console.log('=== 确认操作结束 ===')
    }
}

// 获取状态文本
const getStatusText = (status) => {
    const statusMap = {
        'WAIT': '等待',
        'RUN': '运行中',
        'SHIP': '已入库'
    }
    return statusMap[status] || status
}

// 获取状态类型
const getStatusType = (status) => {
    const typeMap = {
        'WAIT': 'warning',
        'RUN': 'success',
        'SHIP': 'info'
    }
    return typeMap[status] || 'default'
}
</script>

<style scoped>
.station-container {
    width: 100%;
    min-height: 100vh;
    background: #f5f5f5;
}

.page-header {
    background: #fff;
    padding: 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.header-left {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-center {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-right {
    width: 60rpx;
    height: 60rpx;
}

.page-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-left: 15rpx;
}

.search-section {
    background: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.input-wrapper {
    background-color: #f5f6f7;
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    padding: 0 20rpx;
    min-height: 90rpx;
    margin-bottom: 20rpx;
}

.icon {
    margin-right: 20rpx;
    color: #999;
}

.input {
    flex: 1;
    font-size: 28rpx;
    line-height: 40rpx;
    color: #333;
    background: transparent;
    border: none;
    outline: none;
}

.scan-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f2ff;
    border-radius: 12rpx;
    margin-left: 20rpx;
}

.action-buttons {
    display: flex;
    gap: 20rpx;
    justify-content: space-between;
}

.batch-info {
    background: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.info-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
    border-left: 6rpx solid #5677fc;
    padding-left: 20rpx;
}

.info-label {
    font-size: 28rpx;
    color: #666;
}

.info-value {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
}

.modal-content {
    padding: 30rpx 20rpx;
    min-height: 350rpx;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.form-item {
    margin-bottom: 35rpx;
    flex-shrink: 0;
}

.form-item:last-child {
    margin-bottom: 0;
}

.form-label {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 15rpx;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.required {
    color: #ff4757;
    margin-left: 5rpx;
}

.form-tip {
    font-size: 24rpx;
    color: #ff6b35;
    margin-top: 10rpx;
    padding: 15rpx;
    background: #fff5f5;
    border-radius: 8rpx;
    border-left: 4rpx solid #ff6b35;
    line-height: 1.4;
    word-break: break-all;
}

.change-reason-item {
    background: #fafbfc;
    border-radius: 12rpx;
    padding: 20rpx;
    border: 2rpx solid #e8f4fd;
    margin-bottom: 40rpx !important;
}

.change-reason-item .form-label {
    color: #ff6b35;
    font-weight: 600;
}

/* 颗粒数输入样式 */
.particle-count-item {
    background: #f8f9fa;
    border-radius: 12rpx;
    padding: 25rpx 20rpx;
    border: 2rpx solid #e9ecef;
}

.particle-count-item .form-label {
    color: #495057;
    font-weight: 600;
    margin-bottom: 20rpx;
}

.particle-count-input {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15rpx;
}

.count-btn {
    width: 70rpx;
    height: 70rpx;
    background: #ffffff;
    border: 2rpx solid #dee2e6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.count-btn:active {
    background: #f8f9fa;
    transform: scale(0.95);
}

.decrease-btn:active {
    border-color: #dc3545;
}

.increase-btn:active {
    border-color: #28a745;
}

.count-input {
    flex: 1;
    margin: 0 10rpx;
}

/* 针对uv-input内部样式的调整 */
.count-input :deep(.uv-input__content) {
    text-align: center;
    background: #ffffff;
    border: 2rpx solid #dee2e6;
    border-radius: 12rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #495057;
    height: 70rpx;
    line-height: 70rpx;
}

.count-input :deep(.uv-input__content input) {
    text-align: center;
    font-size: 32rpx;
    font-weight: 600;
    color: #495057;
}

.modal-content {
    padding: 20rpx 0;
}
</style>
