<!--批次退步-->
<template>
    <view class="warehouse-container">
        <!-- 搜索区域 -->
        <view class="search-section">
<!--            <view class="search-title">批次退步</view>-->

            <!-- 输入框区域 -->
            <view class="input-wrapper">
                <uv-icon class="icon" name="scan" size="20" color="#999"></uv-icon>
                <input
                    v-model="batchId"
                    class="input"
                    type="text"
                    placeholder="请输入批次ID"
                    maxlength="50"
                    @confirm="addBatchId"
                />
                <view class="scan-btn" @tap="scanCode">
                    <uv-icon name="scan" size="20" color="#5677fc"></uv-icon>
                </view>
            </view>

            <!-- 按钮区域 -->
            <view class="action-buttons-top">
                <uv-button
                    type="default"
                    @tap="clearAll"
                    :disabled="searching || operating"
                    customStyle="width: 90px; height: 45px; border-radius: 25rpx; font-size: 28rpx; font-weight: 500; background: linear-gradient(135deg, #6c757d, #495057); color: #fff; border: none;"
                >
                    清空
                </uv-button>

                <uv-button
                    type="primary"
                    @tap="addBatchId"
                    :disabled="!batchId.trim() || searching"
                    :loading="searching"
                    customStyle="width: 90px; height: 45px; border-radius: 25rpx; font-size: 28rpx; font-weight: 500;"
                >
                    {{ searching ? '查询中' : '查询' }}
                </uv-button>

                <uv-button
                    type="warning"
                    @tap="handleRegressionClick"
                    :disabled="operating || selectedBatches.length === 0"
                    customStyle="width: 90px; height: 45px; border-radius: 25rpx; font-size: 28rpx; font-weight: 500; background: linear-gradient(135deg, #ff6b6b, #ee5a24); border: none;"
                >
                    退步 {{ selectedBatches.length > 0 ? `(${selectedBatches.length})` : '' }}
                </uv-button>
            </view>
        </view>
        
        <!-- 批次列表 -->
        <view class="batch-list" v-if="batchList.length > 0">
            <view class="list-title">
                <text>查询结果 ({{ batchList.length }}个批次)</text>
                <uv-button
                    type="success"
                    size="mini"
                    @tap="selectAll"
                    v-if="!allSelected"
                >
                    全选
                </uv-button>
                <uv-button
                    type="default"
                    size="mini"
                    @tap="unselectAll"
                    v-else
                >
                    取消全选
                </uv-button>
            </view>

            <uv-checkbox-group v-model="selectedBatchIds" @change="onCheckboxGroupChange">
                <slide-delete
                    v-for="(item, index) in batchList"
                    :key="item.id || item.lotId"
                    :index="index"
                    :data="item"
                    @delete="handleSlideDelete"
                    style="width: 100%"
                >
                    <view
                        class="batch-item"
                        :class="{ 'selected': selectedBatchIds.includes(item.id || item.lotId), 'disabled': item.status === 'SHIP' || item.status === 'REGRESSION' }"
                    >
                        <view class="item-header">
                            <view class="batch-id" @tap="toggleSelectById(item.id || item.lotId)">{{ item.lotId }}</view>
                            <uv-checkbox
                                :name="item.id || item.lotId"
                                :disabled="item.status === 'SHIP' || item.status === 'REGRESSION'"
                                shape="circle"
                                @tap.stop
                            ></uv-checkbox>
                        </view>

                    <view class="item-content" @tap="toggleSelectById(item.id || item.lotId)">
                        <uv-row customStyle="margin-bottom: 10rpx">
                            <uv-col :span="6">
                                <text class="label">产品名称:</text>
                                <text class="value">{{ item.productName }}</text>
                            </uv-col>
                            <uv-col :span="6" textAlign="right">
                                <uv-tags
                                    :text="getStatusText(item.status)"
                                    :type="getStatusType(item.status)"
                                    size="mini"
                                ></uv-tags>
                            </uv-col>
                        </uv-row>

                        <uv-row customStyle="margin-bottom: 10rpx">
                            <uv-col :span="6">
                                <text class="label">当前站点:</text>
                                <text class="value">{{ item.currentStationName || '未设置' }}</text>
                            </uv-col>
                            <uv-col :span="6" textAlign="right">
                                <text class="label">颗粒数:</text>
                                <text class="value">{{ item.particleCount || 0 }}</text>
                            </uv-col>
                        </uv-row>
                    </view>

                    <view class="item-status" v-if="item.status === 'SHIP'">
                        <text class="status-text">该批次已入库</text>
                    </view>
                    <view class="item-status" v-if="item.status === 'REGRESSION'">
                        <text class="status-text">该批次已退步</text>
                    </view>
                </view>
                </slide-delete>
            </uv-checkbox-group>
        </view>

        
        <!-- 空状态 -->
        <uv-empty 
            v-if="batchList.length === 0 && !searching" 
            text="请输入批次ID进行查询"
            icon="search"
        ></uv-empty>
        


        <!-- 退步确认弹窗 -->
        <view v-if="showRegressionModal" class="custom-modal-overlay" @tap="handleModalCancel">
            <view class="custom-modal" @tap.stop>
                <view class="modal-header">
                    <text class="modal-title">确认退步</text>
                </view>

                <view class="modal-body">
                    <view class="confirm-text">
                        确认将以下 {{ selectedBatches.length }} 个批次进行退步操作？
                    </view>

                    <view class="batch-summary">
                        <view
                            class="summary-item"
                            v-for="(batch, index) in selectedBatches"
                            :key="index"
                        >
                            <text class="summary-id">{{ batch.lotId }}</text>
                            <text class="summary-product">{{ batch.productName }}</text>
                        </view>
                    </view>

                    <!-- 备注输入区域 -->
                    <view class="remark-section">
                        <view class="remark-label">退步备注：</view>
                        <textarea
                            v-model="regressionRemark"
                            class="remark-textarea"
                            placeholder="请输入退步备注信息"
                            maxlength="200"
                            auto-height
                        ></textarea>
                        <view class="char-count">{{ regressionRemark.length }}/200</view>
                    </view>
                </view>

                <view class="modal-footer">
                    <button class="modal-btn cancel-btn" @tap="handleModalCancel">取消</button>
                    <button class="modal-btn confirm-btn" @tap="confirmRegression" :disabled="operating">
                        {{ operating ? '处理中...' : '确认退步' }}
                    </button>
                </view>
            </view>
        </view>

        <!-- 异常确认弹窗 -->
        <view v-if="showErrorModal" class="custom-modal-overlay" @tap="handleErrorModalClose">
            <view class="custom-modal error-modal" @tap.stop>
                <view class="modal-header">
                    <text class="modal-title error-title">操作异常</text>
                </view>

                <view class="modal-body">
                    <view class="error-icon">
                        <uv-icon name="close-circle-fill" size="60" color="#ff4757"></uv-icon>
                    </view>
                    <view class="error-text">
                        {{ errorMessage }}
                    </view>
                </view>

                <view class="modal-footer">
                    <button class="modal-btn confirm-btn error-confirm-btn" @tap="handleErrorModalClose">
                        确定
                    </button>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { searchBatchByLotId, batchRegression } from '@/api/choiceway/mesBatchApi.js'
import SlideDelete from '@/components/slide-delete/index.vue'

export default {
    components: {
        SlideDelete
    },
    data() {
        return {
            batchId: '',
            batchList: [],
            selectedBatchIds: [], // 选中的批次ID数组
            searching: false,
            operating: false,
            showRegressionModal: false,
            regressionRemark: '',
            showErrorModal: false, // 控制异常弹窗显示
            errorMessage: '' // 存储异常信息
        }
    },
    
    computed: {
        // 已选择的批次
        selectedBatches() {
            console.log('=== 计算selectedBatches开始 ===')
            console.log('当前batchList:', this.batchList)
            console.log('当前selectedBatchIds:', this.selectedBatchIds)

            const result = this.batchList.filter(item => {
                const batchId = item.id || item.lotId
                const isSelected = this.selectedBatchIds.includes(batchId)
                const isAvailable = item.status !== 'SHIP' && item.status !== 'REGRESSION'

                console.log(`批次 ${item.lotId}: ID=${batchId}, 选中=${isSelected}, 可用=${isAvailable}, 状态=${item.status}`)

                return isSelected && isAvailable
            })

            console.log('计算的selectedBatches结果:', result)
            console.log('selectedBatches数量:', result.length)
            console.log('=== 计算selectedBatches结束 ===')
            return result
        },

        // 是否全选
        allSelected() {
            console.log('=== 计算allSelected开始 ===')
            const availableBatches = this.batchList.filter(item => item.status !== 'SHIP' && item.status !== 'REGRESSION')
            const result = availableBatches.length > 0 &&
                   availableBatches.every(item => this.selectedBatchIds.includes(item.id || item.lotId))

            console.log('allSelected计算:', {
                availableBatches: availableBatches.length,
                selectedBatchIds: this.selectedBatchIds,
                result: result
            })
            console.log('=== 计算allSelected结束 ===')

            return result
        }
    },
    
    onLoad(options) {
        if (options.batchId) {
            this.batchId = options.batchId
            this.addBatchId()
        }
    },
    
    methods: {
        // 添加批次ID
        async addBatchId() {
            console.log('=== 添加批次ID开始 ===')
            console.log('输入的批次ID:', this.batchId)

            if (!this.batchId.trim()) {
                console.log('批次ID为空，显示提示')
                uni.showToast({
                    title: '请输入批次ID',
                    icon: 'none'
                })
                return
            }

            const trimmedLotId = this.batchId.trim()
            console.log('处理后的批次ID:', trimmedLotId)

            // 检查是否已存在
            const existingBatch = this.batchList.find(item => item.lotId === trimmedLotId)
            console.log('检查是否已存在:', existingBatch)
            if (existingBatch) {
                console.log('批次已存在，显示提示')
                uni.showToast({
                    title: '该批次已在列表中',
                    icon: 'none'
                })
                this.batchId = ''
                return
            }

            console.log('开始查询批次信息')
            this.searching = true

            try {
                console.log('调用searchBatchByLotId接口，参数:', trimmedLotId)
                const res = await searchBatchByLotId(trimmedLotId)
                console.log('查询批次接口响应:', res)

                if (res.code === 200 && res.data && res.data.records && res.data.records.length > 0) {
                    // 取第一个匹配的批次（通常lotId是唯一的）
                    const batchData = res.data.records[0]
                    console.log('查询到的批次数据:', batchData)

                    const newBatch = {
                        ...batchData,
                        selected: false // 默认不选中，用户需要手动选择
                    }

                    console.log('准备添加的批次:', newBatch)
                    console.log('批次ID字段:', newBatch.id, '批次LOT ID:', newBatch.lotId)

                    this.batchList.push(newBatch)
                    console.log('批次添加到列表成功')

                    this.batchId = '' // 清空输入框
                    console.log('输入框已清空')

                    console.log('添加后的批次列表:', this.batchList)
                    console.log('当前selectedBatchIds:', this.selectedBatchIds)

                    uni.showToast({
                        title: '批次添加成功',
                        icon: 'success'
                    })
                } else {
                    console.log('查询失败，响应数据:', res)
                    uni.showToast({
                        title: '未找到该批次',
                        icon: 'none'
                    })
                }
            } catch (error) {
                console.error('查询批次失败:', error)
                uni.showToast({
                    title: '查询失败',
                    icon: 'none'
                })
            } finally {
                this.searching = false
                console.log('查询状态重置为false')
                console.log('=== 添加批次ID结束 ===')
            }
        },

        // 扫码功能
        scanCode() {
            uni.scanCode({
                success: (res) => {
                    this.batchId = res.result
                    // 扫码成功后自动添加
                    this.addBatchId()
                },
                fail: (err) => {
                    console.error('扫码失败:', err)
                    uni.showToast({
                        title: '扫码失败',
                        icon: 'none'
                    })
                }
            })
        },
        
        // 清空所有
        clearAll() {
            console.log('=== 清空所有数据开始 ===')
            console.log('清空前的数据状态:')
            console.log('batchId:', this.batchId)
            console.log('batchList:', this.batchList)
            console.log('selectedBatchIds:', this.selectedBatchIds)
            console.log('regressionRemark:', this.regressionRemark)

            this.batchId = ''
            this.batchList = []
            this.selectedBatchIds = []
            this.regressionRemark = ''

            console.log('清空后的数据状态:')
            console.log('batchId:', this.batchId)
            console.log('batchList:', this.batchList)
            console.log('selectedBatchIds:', this.selectedBatchIds)
            console.log('regressionRemark:', this.regressionRemark)
            console.log('=== 清空所有数据结束 ===')

            uni.showToast({
                title: '已清空',
                icon: 'success'
            })
        },

        // 复选框组变化
        onCheckboxGroupChange(value) {
            console.log('=== 复选框组状态变化 ===')
            console.log('新选中的ID列表:', value)
            console.log('当前批次列表:', this.batchList)
            this.selectedBatchIds = value
            console.log('更新后的selectedBatchIds:', this.selectedBatchIds)
            console.log('计算的selectedBatches:', this.selectedBatches)
            console.log('=== 复选框组状态变化结束 ===')
        },

        // 处理退步按钮点击
        handleRegressionClick() {
            console.log('=== 退步按钮点击开始 ===')
            console.log('当前operating状态:', this.operating)
            console.log('selectedBatches长度:', this.selectedBatches.length)
            console.log('selectedBatches内容:', this.selectedBatches)
            console.log('selectedBatchIds:', this.selectedBatchIds)
            console.log('batchList:', this.batchList)

            if (this.operating) {
                console.log('操作进行中，忽略点击')
                return
            }

            if (this.selectedBatches.length === 0) {
                console.log('没有选中的批次')
                uni.showToast({
                    title: '请选择要退步的批次',
                    icon: 'none'
                })
                return
            }

            console.log('准备显示退步确认弹窗')

            // 显示自定义弹窗（包含备注输入框）
            this.showRegressionModal = true
            console.log('showRegressionModal设置为:', this.showRegressionModal)

            // 使用 nextTick 确保数据更新
            this.$nextTick(() => {
                console.log('nextTick后的showRegressionModal:', this.showRegressionModal)
            })

            console.log('=== 退步按钮点击结束 ===')
        },

        // 处理弹窗取消
        handleModalCancel() {
            console.log('=== 弹窗取消开始 ===')
            this.showRegressionModal = false
            console.log('showRegressionModal设置为:', this.showRegressionModal)
            console.log('=== 弹窗取消结束 ===')
        },

        // 根据ID切换选择状态
        toggleSelectById(batchId) {
            console.log('=== 切换选择状态 ===')
            console.log('点击的批次ID:', batchId)
            console.log('当前selectedBatchIds:', this.selectedBatchIds)

            const batch = this.batchList.find(item => (item.id || item.lotId) === batchId)
            console.log('找到的批次:', batch)

            if (batch && batch.status !== 'SHIP' && batch.status !== 'REGRESSION') {
                const index = this.selectedBatchIds.indexOf(batchId)
                console.log('在selectedBatchIds中的索引:', index)

                if (index > -1) {
                    this.selectedBatchIds.splice(index, 1)
                    console.log('从选中列表中移除:', batchId)
                } else {
                    this.selectedBatchIds.push(batchId)
                    console.log('添加到选中列表:', batchId)
                }

                console.log('更新后的selectedBatchIds:', this.selectedBatchIds)
                console.log('计算的selectedBatches:', this.selectedBatches)

                // 手动触发复选框组更新
                this.$nextTick(() => {
                    console.log('nextTick后的selectedBatchIds:', this.selectedBatchIds)
                })
            } else {
                console.log('批次不存在或已入库/已退步，无法切换状态')
            }
            console.log('=== 切换选择状态结束 ===')
        },



        // 处理左滑删除
        handleSlideDelete(data) {
            const { index, data: item } = data
            uni.showModal({
                title: '确认删除',
                content: `确定要从列表中移除批次 ${item.lotId} 吗？`,
                success: (res) => {
                    if (res.confirm) {
                        // 从选中列表中移除
                        const batchId = item.id || item.lotId
                        const selectedIndex = this.selectedBatchIds.indexOf(batchId)
                        if (selectedIndex > -1) {
                            this.selectedBatchIds.splice(selectedIndex, 1)
                        }

                        // 从批次列表中移除
                        this.batchList.splice(index, 1)

                        uni.showToast({
                            title: '已移除',
                            icon: 'success'
                        })
                    }
                }
            })
        },
        

        
        // 全选
        selectAll() {
            console.log('=== 全选操作开始 ===')
            const availableBatchIds = this.batchList
                .filter(item => item.status !== 'SHIP' && item.status !== 'REGRESSION')
                .map(item => item.id || item.lotId)
            console.log('可选择的批次ID列表:', availableBatchIds)
            this.selectedBatchIds = [...availableBatchIds]
            console.log('全选后的selectedBatchIds:', this.selectedBatchIds)
            console.log('=== 全选操作结束 ===')
        },

        // 取消全选
        unselectAll() {
            console.log('=== 取消全选操作开始 ===')
            console.log('取消全选前的selectedBatchIds:', this.selectedBatchIds)
            this.selectedBatchIds = []
            console.log('取消全选后的selectedBatchIds:', this.selectedBatchIds)
            console.log('=== 取消全选操作结束 ===')
        },
        
        // 确认退步
        async confirmRegression() {
            console.log('=== 确认退步开始 ===')
            console.log('当前时间:', new Date().toLocaleString())
            console.log('selectedBatches:', this.selectedBatches)
            console.log('selectedBatches长度:', this.selectedBatches.length)
            console.log('退步备注:', this.regressionRemark)
            console.log('当前operating状态:', this.operating)

            if (this.selectedBatches.length === 0) {
                console.log('没有选中的批次，显示提示')
                uni.showToast({
                    title: '请选择要退步的批次',
                    icon: 'none'
                })
                return
            }

            console.log('开始退步操作，设置operating为true')
            this.operating = true

            try {
                // 提取批次的lotId列表
                const lotIds = this.selectedBatches.map(item => item.lotId)
                console.log('提取的批次LOT ID列表:', lotIds)
                console.log('批次详细信息:')
                this.selectedBatches.forEach((batch, index) => {
                    console.log(`  批次${index + 1}: ID=${batch.id}, LOT_ID=${batch.lotId}, 产品=${batch.productName}, 状态=${batch.status}`)
                })

                const requestData = {
                    LOT_IDS: lotIds,
                    REMARKS: this.regressionRemark || ''
                }
                console.log('退步请求数据:', JSON.stringify(requestData, null, 2))
                console.log('准备调用batchRegression接口...')

                const res = await batchRegression(requestData)
                console.log('退步接口响应完成')
                console.log('响应状态码:', res?.code)
                console.log('响应消息:', res?.message)
                console.log('完整响应数据:', JSON.stringify(res, null, 2))

                if (res.code === 200) {
                    console.log('✅ 退步成功处理开始')

                    // 关闭输入备注确认弹窗
                    console.log('关闭输入备注确认弹窗')
                    this.showRegressionModal = false
                    this.regressionRemark = ''
                    console.log('弹窗状态已重置: showRegressionModal =', this.showRegressionModal)

                    // 显示成功提示
                    uni.showToast({
                        title: '退步成功',
                        icon: 'success'
                    })

                    // 清空选中状态
                    console.log('清空选中状态')
                    const oldSelectedIds = [...this.selectedBatchIds]
                    this.selectedBatchIds = []
                    console.log('选中状态已清空: 从', oldSelectedIds, '到', this.selectedBatchIds)

                    // 重新查询所有批次信息
                    console.log('开始重新查询所有批次信息...')
                    await this.refreshAllBatches()
                    console.log('✅ 退步成功处理完成')
                } else {
                    console.log('❌ 退步失败处理开始')
                    console.log('失败原因:', res.message || '未知错误')

                    // 关闭输入备注确认弹窗
                    console.log('关闭输入备注确认弹窗')
                    this.showRegressionModal = false

                    // 显示异常确认弹窗
                    console.log('显示异常确认弹窗')
                    this.showErrorConfirmModal(res.message || '退步失败，请重试')
                    console.log('❌ 退步失败处理完成')
                }
            } catch (error) {
                console.log('❌ 退步异常处理开始')
                console.error('退步异常:', error)
                console.error('异常类型:', error.constructor.name)
                console.error('异常消息:', error.message)
                console.error('异常堆栈:', error.stack)

                // 如果是网络错误，记录更多信息
                if (error.response) {
                    console.error('HTTP响应错误:')
                    console.error('  状态码:', error.response.status)
                    console.error('  状态文本:', error.response.statusText)
                    console.error('  响应数据:', error.response.data)
                } else if (error.request) {
                    console.error('网络请求错误:', error.request)
                } else {
                    console.error('其他错误:', error.message)
                }

                // 关闭输入备注确认弹窗
                console.log('异常情况下关闭输入备注确认弹窗')
                this.showRegressionModal = false

                // 显示异常确认弹窗
                console.log('显示异常确认弹窗')
                const errorMsg = error.message || '网络异常，请检查网络连接后重试'
                this.showErrorConfirmModal(errorMsg)
                console.log('❌ 退步异常处理完成')
            } finally {
                console.log('退步操作结束，设置operating为false')
                this.operating = false
                console.log('最终状态: operating =', this.operating)
                console.log('=== 确认退步结束 ===')
            }
        },

        // 显示异常确认弹窗
        showErrorConfirmModal(message) {
            console.log('=== 显示异常确认弹窗开始 ===')
            console.log('异常消息:', message)

            this.errorMessage = message
            this.showErrorModal = true

            console.log('异常弹窗状态设置完成:')
            console.log('  errorMessage:', this.errorMessage)
            console.log('  showErrorModal:', this.showErrorModal)
            console.log('=== 显示异常确认弹窗结束 ===')
        },

        // 关闭异常弹窗
        handleErrorModalClose() {
            console.log('=== 关闭异常弹窗开始 ===')
            console.log('关闭前状态: showErrorModal =', this.showErrorModal)

            this.showErrorModal = false
            this.errorMessage = ''

            console.log('关闭后状态:')
            console.log('  showErrorModal:', this.showErrorModal)
            console.log('  errorMessage:', this.errorMessage)
            console.log('=== 关闭异常弹窗结束 ===')
        },

        // 重新查询所有批次信息
        async refreshAllBatches() {
            console.log('=== 重新查询所有批次信息开始 ===')
            console.log('当前批次列表长度:', this.batchList.length)
            console.log('需要重新查询的批次:')

            const lotIdsToRefresh = this.batchList.map(batch => batch.lotId)
            console.log('批次LOT ID列表:', lotIdsToRefresh)

            if (lotIdsToRefresh.length === 0) {
                console.log('没有需要刷新的批次，直接返回')
                console.log('=== 重新查询所有批次信息结束 ===')
                return
            }

            console.log('开始逐个重新查询批次信息...')
            const refreshedBatches = []

            for (let i = 0; i < lotIdsToRefresh.length; i++) {
                const lotId = lotIdsToRefresh[i]
                console.log(`正在查询第${i + 1}/${lotIdsToRefresh.length}个批次: ${lotId}`)

                try {
                    const res = await searchBatchByLotId(lotId)
                    console.log(`批次 ${lotId} 查询响应:`, res)

                    if (res.code === 200 && res.data && res.data.records && res.data.records.length > 0) {
                        const batchData = res.data.records[0]
                        console.log(`批次 ${lotId} 查询成功，新状态: ${batchData.status}`)
                        refreshedBatches.push(batchData)
                    } else {
                        console.log(`批次 ${lotId} 查询失败或无数据`)
                        // 如果查询失败，保留原数据但标记为可能已删除
                        const originalBatch = this.batchList.find(b => b.lotId === lotId)
                        if (originalBatch) {
                            console.log(`保留原批次数据: ${lotId}`)
                            refreshedBatches.push(originalBatch)
                        }
                    }
                } catch (error) {
                    console.error(`查询批次 ${lotId} 异常:`, error)
                    // 查询异常时保留原数据
                    const originalBatch = this.batchList.find(b => b.lotId === lotId)
                    if (originalBatch) {
                        console.log(`查询异常，保留原批次数据: ${lotId}`)
                        refreshedBatches.push(originalBatch)
                    }
                }
            }

            console.log('所有批次查询完成，更新批次列表')
            console.log('刷新前批次数量:', this.batchList.length)
            console.log('刷新后批次数量:', refreshedBatches.length)

            this.batchList = refreshedBatches

            console.log('批次列表更新完成:')
            this.batchList.forEach((batch, index) => {
                console.log(`  批次${index + 1}: ${batch.lotId} - 状态: ${batch.status}`)
            })

            console.log('=== 重新查询所有批次信息结束 ===')
        },

        // 获取状态文本
        getStatusText(status) {
            const statusMap = {
                'WAIT': '等待',
                'RUN': '运行中',
                'SHIP': '已入库',
                'REGRESSION': '已退步'
            }
            return statusMap[status] || status
        },
        
        // 获取状态类型
        getStatusType(status) {
            const typeMap = {
                'WAIT': 'warning',
                'RUN': 'success',
                'SHIP': 'info',
                'REGRESSION': 'error'
            }
            return typeMap[status] || 'default'
        }
    }
}
</script>

<style scoped>
.warehouse-container {
    padding: 20rpx;
    background: #f5f5f5;
    min-height: 100vh;
}

.search-section {
    background: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
}

.search-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
}

.input-wrapper {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 8rpx;
    padding: 0 20rpx;
    margin-bottom: 20rpx;
    height: 80rpx;
}

.input-wrapper .icon {
    margin-right: 15rpx;
}

.input-wrapper .input {
    flex: 1;
    font-size: 28rpx;
    color: #333;
    background: transparent;
    border: none;
    outline: none;
}

.input-wrapper .input::placeholder {
    color: #999;
}

.scan-btn {
    padding: 10rpx;
    margin-left: 15rpx;
    border-radius: 6rpx;
    background: #f0f4ff;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-buttons-top {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-top: 20rpx;
    padding: 0 20rpx;
}

.action-buttons-top .uv-button {
    width: 90px !important;
    height: 45px !important;
    border-radius: 25rpx !important;
    font-size: 28rpx !important;
    font-weight: 500 !important;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.action-buttons-top .uv-button:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

/* 清空按钮样式 */
.action-buttons-top .uv-button[type="default"] {
    background: linear-gradient(135deg, #6c757d, #495057) !important;
    border: none !important;
    color: #fff !important;
}

.action-buttons-top .uv-button[type="default"]:not([disabled]):hover {
    background: linear-gradient(135deg, #495057, #343a40) !important;
    color: #fff !important;
}

/* 查询按钮样式 */
.action-buttons-top .uv-button[type="primary"] {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
    border: none !important;
    color: #fff !important;
}

.action-buttons-top .uv-button[type="primary"]:not([disabled]):hover {
    background: linear-gradient(135deg, #0056b3, #004085) !important;
}

/* 入库按钮样式 */
.action-buttons-top .uv-button[type="warning"] {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24) !important;
    border: none !important;
    color: #fff !important;
}

.action-buttons-top .uv-button[type="warning"]:not([disabled]):hover {
    background: linear-gradient(135deg, #ee5a24, #d63031) !important;
}

/* 禁用状态 */
.action-buttons-top .uv-button[disabled] {
    opacity: 0.6 !important;
    transform: none !important;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05) !important;
    cursor: not-allowed !important;
}

/* 加载状态动画 */
.action-buttons-top .uv-button[loading] {
    position: relative;
}

.action-buttons-top .uv-button[loading]::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20rpx;
    height: 20rpx;
    margin: -10rpx 0 0 -10rpx;
    border: 2rpx solid transparent;
    border-top: 2rpx solid #fff;
    border-radius: 50%;
    animation: button-loading 1s linear infinite;
}

@keyframes button-loading {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 按钮文字渐变效果 */
.action-buttons-top .uv-button[type="warning"] {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #d63031 100%) !important;
    background-size: 200% 200% !important;
    animation: gradient-shift 3s ease infinite !important;
}

@keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 入库按钮计数器样式 */
.action-buttons-top .uv-button[type="warning"] .count {
    display: inline-block;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 12rpx;
    padding: 2rpx 8rpx;
    margin-left: 8rpx;
    font-size: 24rpx;
    font-weight: bold;
}

.batch-list {
    background: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
}

.list-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid #f0f0f0;
}

.batch-item {
    border: 2rpx solid #e4e7ed;
    border-radius: 8rpx;
    padding: 20rpx;
    transition: all 0.3s;
}

.batch-item.selected {
    border-color: #5677fc;
    background: #f0f4ff;
}

.batch-item.disabled {
    opacity: 0.6;
    background: #f5f5f5;
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15rpx;
}

.batch-id {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
}

.item-content .label {
    font-size: 26rpx;
    color: #666;
    margin-right: 10rpx;
}

.item-content .value {
    font-size: 26rpx;
    color: #333;
}

.item-status {
    text-align: center;
    margin-top: 15rpx;
    padding-top: 15rpx;
    border-top: 1rpx solid #e4e7ed;
}

.status-text {
    font-size: 24rpx;
    color: #909399;
}



.modal-content {
    padding: 20rpx 0;
}

.confirm-text {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 20rpx;
}

.batch-summary {
    max-height: 200rpx;
    overflow-y: auto;
    background: #f8f9fa;
    border-radius: 8rpx;
    padding: 15rpx;
    margin-bottom: 20rpx;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 8rpx 0;
    border-bottom: 1rpx solid #e4e7ed;
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-id {
    font-size: 26rpx;
    font-weight: bold;
    color: #333;
}

.summary-product {
    font-size: 24rpx;
    color: #666;
}

.remark-section {
    margin-top: 20rpx;
}

.remark-label {
    font-size: 26rpx;
    color: #333;
    margin-bottom: 10rpx;
}

/* 自定义弹窗样式 */
.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.custom-modal {
    background: #fff;
    border-radius: 12rpx;
    width: 600rpx;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
    padding: 30rpx 30rpx 20rpx;
    border-bottom: 1rpx solid #f0f0f0;
    text-align: center;
}

.modal-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
}

.modal-body {
    padding: 30rpx;
    max-height: 60vh;
    overflow-y: auto;
}

.remark-textarea {
    width: 100%;
    min-height: 120rpx;
    padding: 20rpx;
    border: 1rpx solid #e4e7ed;
    border-radius: 8rpx;
    background: #f8f9fa;
    font-size: 28rpx;
    color: #333;
    line-height: 1.5;
    box-sizing: border-box;
}

.remark-textarea::placeholder {
    color: #999;
}

.char-count {
    text-align: right;
    font-size: 24rpx;
    color: #999;
    margin-top: 10rpx;
}

.modal-footer {
    display: flex;
    border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
    flex: 1;
    height: 88rpx;
    border: none;
    font-size: 30rpx;
    font-weight: 500;
    background: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cancel-btn {
    color: #666;
    border-right: 1rpx solid #f0f0f0;
}

.cancel-btn:active {
    background: #f5f5f5;
}

.confirm-btn {
    color: #5677fc;
}

.confirm-btn:active {
    background: #f0f4ff;
}

.confirm-btn:disabled {
    color: #ccc;
    background: none;
}

/* 异常弹窗样式 */
.error-modal {
    border: 2rpx solid #ff4757;
}

.error-title {
    color: #ff4757;
}

.error-icon {
    text-align: center;
    margin-bottom: 20rpx;
}

.error-text {
    font-size: 28rpx;
    color: #333;
    text-align: center;
    line-height: 1.6;
    padding: 0 20rpx;
}

.error-confirm-btn {
    color: #ff4757 !important;
    font-weight: bold;
}

.error-confirm-btn:active {
    background: #fff5f5 !important;
}
</style>
