
.tui-list-cell[data-v-f48b54a1] {
		position: relative;
		width: 100%;
		box-sizing: border-box;
}
.tui-radius[data-v-f48b54a1] {
		overflow: hidden;
}
.tui-cell-hover[data-v-f48b54a1]:active {
		background-color: rgba(0, 0, 0, 0.1) !important;
}
.tui-cell__line[data-v-f48b54a1] {
		position: absolute;
		border-bottom: 1px solid #eaeef1;
		transform: scaleY(0.5) translateZ(0);
		transform-origin: 0 100%;
		bottom: 0;
		right: 0;
		left: 0;
		pointer-events: none;
}
.tui-cell__arrow[data-v-f48b54a1] {
		height: 10px;
		width: 10px;
		border-width: 2px 2px 0 0;
		border-color: #c0c0c0;
		border-style: solid;
		transform: matrix(0.5, 0.5, -0.5, 0.5, 0, 0);
		position: absolute;
		top: 50%;
		margin-top: -6px;
		right: 0.9375rem;
}


.tui-swipeout-wrap[data-v-fa3bde1d] {
		position: relative;
		overflow: hidden;
}
.swipe-action-show[data-v-fa3bde1d] {
		position: relative;
		z-index: 998;
}
.tui-swipeout-item[data-v-fa3bde1d] {
		width: 100%;
		/* padding: 15px 20px; */
		box-sizing: border-box;
		transition: transform 0.2s ease;
		font-size: 14px;
		cursor: pointer;
}
.tui-swipeout-content[data-v-fa3bde1d] {
		white-space: nowrap;
		overflow: hidden;
}
.tui-swipeout-button-right-group[data-v-fa3bde1d] {
		position: absolute;
		right: -100%;
		top: 0;
		height: 100%;
		z-index: 1;
		width: 100%;
}
.tui-swipeout-button-right-item[data-v-fa3bde1d] {
		height: 100%;
		float: left;
		white-space: nowrap;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		justify-content: center;
		text-align: center;
}
.swipe-action_mask[data-v-fa3bde1d] {
		display: block;
		opacity: 0;
		position: fixed;
		z-index: 997;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
}


.tui-list-title[data-v-b9cb6718] {
		width: 100%;
		padding: 0.9375rem;
		box-sizing: border-box;
}
.tui-list-content[data-v-b9cb6718] {
		width: 100%;
		position: relative;
}
.tui-list-content[data-v-b9cb6718]::before {
		content: " ";
		position: absolute;
		top: 0;
		right: 0;
		left: 0;
		border-top: 1px solid #eaeef1;
		transform: scaleY(0.5) translateZ(0);
		transform-origin: 0 0;
		z-index: 2;
		pointer-events: none;
}
.tui-list-content[data-v-b9cb6718]::after {
		content: '';
		width: 100%;
		position: absolute;
		border-bottom: 1px solid #eaeef1;
		transform: scaleY(0.5) translateZ(0);
		transform-origin: 0 100%;
		bottom: 0;
		right: 0;
		left: 0;
}
.tui-border-top[data-v-b9cb6718]::before {
		border-top: 0;
}
.tui-border-bottom[data-v-b9cb6718]::after {
		border-bottom: 0;
}
.tui-border-all[data-v-b9cb6718]::after {
		border-bottom: 0;
}
.tui-border-all[data-v-b9cb6718]::before {
		border-top: 0;
}
.tui-radius[data-v-b9cb6718] {
		overflow: hidden;
}


.tui-nodata-box[data-v-ce1933c5] {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
}
.tui-nodata-fixed[data-v-ce1933c5] {
		width: 90%;
		position: fixed;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
}
.tui-tips-icon[data-v-ce1933c5] {
		display: block;
		flex-shrink: 0;
		width: 8.75rem;
		height: 8.75rem;
}
.tui-tips-content[data-v-ce1933c5] {
		text-align: center;
		color: #666666;
		font-size: 0.875rem;
		padding: 0 1.5625rem 0.875rem 1.5625rem;
		box-sizing: border-box;
		word-break: break-all;
		word-wrap: break-word;
}
.tui-tips-btn[data-v-ce1933c5] {
		color: #fff;
		margin: 0;
		display: flex;
		align-items: center;
		justify-content: center;
}
.tui-tips-btn[data-v-ce1933c5]:active{
		opacity: 0.5;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
uni-view[data-v-b7a6dd5d], uni-scroll-view[data-v-b7a6dd5d], uni-swiper-item[data-v-b7a6dd5d] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
@font-face {
  font-family: "uvicon-iconfont";
  src: url("../../../assets/uvicons.04d281cc.ttf") format("truetype");
}
.uv-icon[data-v-b7a6dd5d] {
  display: flex;
  align-items: center;
}
.uv-icon--left[data-v-b7a6dd5d] {
  flex-direction: row-reverse;
  align-items: center;
}
.uv-icon--right[data-v-b7a6dd5d] {
  flex-direction: row;
  align-items: center;
}
.uv-icon--top[data-v-b7a6dd5d] {
  flex-direction: column-reverse;
  justify-content: center;
}
.uv-icon--bottom[data-v-b7a6dd5d] {
  flex-direction: column;
  justify-content: center;
}
.uv-icon__icon[data-v-b7a6dd5d] {
  font-family: uvicon-iconfont;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.uv-icon__icon--primary[data-v-b7a6dd5d] {
  color: #3c9cff;
}
.uv-icon__icon--success[data-v-b7a6dd5d] {
  color: #5ac725;
}
.uv-icon__icon--error[data-v-b7a6dd5d] {
  color: #f56c6c;
}
.uv-icon__icon--warning[data-v-b7a6dd5d] {
  color: #f9ae3d;
}
.uv-icon__icon--info[data-v-b7a6dd5d] {
  color: #909399;
}
.uv-icon__img[data-v-b7a6dd5d] {
  height: auto;
  will-change: transform;
}
.uv-icon__label[data-v-b7a6dd5d] {
  line-height: 1;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.floating-button[data-v-a612270d] {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background-color: #5677fc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
  transition: background-color 0.3s, transform 0.3s;
  cursor: pointer;
}
.floating-button[data-v-a612270d]:hover {
  background-color: #2979ff;
  transform: scale(1.1);
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.crumb[data-v-ac9f2df1] {
  white-space: nowrap;
  overflow-x: scroll;
  padding: 0.625rem;
}
.crumb .text[data-v-ac9f2df1] {
  display: inline-block;
  margin-left: 0.15625rem;
  text-align: center;
}
.list[data-v-ac9f2df1] {
  margin: 0.46875rem 0;
  background-color: white;
  padding: 0.15625rem 0;
}
.list .item[data-v-ac9f2df1] {
  width: 100%;
  display: flex;
  align-items: center;
}
.list .item .item-img[data-v-ac9f2df1] {
  width: 2.1875rem;
  height: 2.1875rem;
  border-radius: 50%;
  display: block;
  margin-right: 0.15625rem;
  flex-shrink: 0;
}
.list .item .item-left[data-v-ac9f2df1] {
  padding-left: 0.625rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.list .item .item-right[data-v-ac9f2df1] {
  margin-left: auto;
  margin-right: 1.0625rem;
  font-size: 0.8125rem;
  color: #999;
}