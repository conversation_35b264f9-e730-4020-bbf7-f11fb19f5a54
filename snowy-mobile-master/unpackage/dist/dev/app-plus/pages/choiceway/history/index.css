/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
uni-view[data-v-b7a6dd5d], uni-scroll-view[data-v-b7a6dd5d], uni-swiper-item[data-v-b7a6dd5d] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
@font-face {
  font-family: "uvicon-iconfont";
  src: url("../../../assets/uvicons.04d281cc.ttf") format("truetype");
}
.uv-icon[data-v-b7a6dd5d] {
  display: flex;
  align-items: center;
}
.uv-icon--left[data-v-b7a6dd5d] {
  flex-direction: row-reverse;
  align-items: center;
}
.uv-icon--right[data-v-b7a6dd5d] {
  flex-direction: row;
  align-items: center;
}
.uv-icon--top[data-v-b7a6dd5d] {
  flex-direction: column-reverse;
  justify-content: center;
}
.uv-icon--bottom[data-v-b7a6dd5d] {
  flex-direction: column;
  justify-content: center;
}
.uv-icon__icon[data-v-b7a6dd5d] {
  font-family: uvicon-iconfont;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.uv-icon__icon--primary[data-v-b7a6dd5d] {
  color: #3c9cff;
}
.uv-icon__icon--success[data-v-b7a6dd5d] {
  color: #5ac725;
}
.uv-icon__icon--error[data-v-b7a6dd5d] {
  color: #f56c6c;
}
.uv-icon__icon--warning[data-v-b7a6dd5d] {
  color: #f9ae3d;
}
.uv-icon__icon--info[data-v-b7a6dd5d] {
  color: #909399;
}
.uv-icon__img[data-v-b7a6dd5d] {
  height: auto;
  will-change: transform;
}
.uv-icon__label[data-v-b7a6dd5d] {
  line-height: 1;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
uni-view[data-v-29b619ea], uni-scroll-view[data-v-29b619ea], uni-swiper-item[data-v-29b619ea] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-loading-icon[data-v-29b619ea] {
  flex-direction: row;
  align-items: center;
  justify-content: center;
  color: #c8c9cc;
}
.uv-loading-icon__text[data-v-29b619ea] {
  margin-left: 4px;
  color: #606266;
  font-size: 14px;
  line-height: 20px;
}
.uv-loading-icon__spinner[data-v-29b619ea] {
  width: 30px;
  height: 30px;
  position: relative;
  box-sizing: border-box;
  max-width: 100%;
  max-height: 100%;
  animation: uv-rotate-29b619ea 1s linear infinite;
}
.uv-loading-icon__spinner--semicircle[data-v-29b619ea] {
  border-width: 2px;
  border-color: transparent;
  border-top-right-radius: 100px;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  border-bottom-right-radius: 100px;
  border-style: solid;
}
.uv-loading-icon__spinner--circle[data-v-29b619ea] {
  border-top-right-radius: 100px;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  border-bottom-right-radius: 100px;
  border-width: 2px;
  border-top-color: #e5e5e5;
  border-right-color: #e5e5e5;
  border-bottom-color: #e5e5e5;
  border-left-color: #e5e5e5;
  border-style: solid;
}
.uv-loading-icon--vertical[data-v-29b619ea] {
  flex-direction: column;
}
[data-v-29b619ea]:host {
  font-size: 0px;
  line-height: 1;
}
.uv-loading-icon__spinner--spinner[data-v-29b619ea] {
  animation-timing-function: steps(12);
}
.uv-loading-icon__text[data-v-29b619ea]:empty {
  display: none;
}
.uv-loading-icon--vertical .uv-loading-icon__text[data-v-29b619ea] {
  margin: 6px 0 0;
  color: #606266;
}
.uv-loading-icon__dot[data-v-29b619ea] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.uv-loading-icon__dot[data-v-29b619ea]:before {
  display: block;
  width: 2px;
  height: 25%;
  margin: 0 auto;
  background-color: currentColor;
  border-radius: 40%;
  content: " ";
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(1) {
  transform: rotate(30deg);
  opacity: 1;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(2) {
  transform: rotate(60deg);
  opacity: 0.9375;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(3) {
  transform: rotate(90deg);
  opacity: 0.875;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(4) {
  transform: rotate(120deg);
  opacity: 0.8125;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(5) {
  transform: rotate(150deg);
  opacity: 0.75;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(6) {
  transform: rotate(180deg);
  opacity: 0.6875;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(7) {
  transform: rotate(210deg);
  opacity: 0.625;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(8) {
  transform: rotate(240deg);
  opacity: 0.5625;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(9) {
  transform: rotate(270deg);
  opacity: 0.5;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(10) {
  transform: rotate(300deg);
  opacity: 0.4375;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(11) {
  transform: rotate(330deg);
  opacity: 0.375;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(12) {
  transform: rotate(360deg);
  opacity: 0.3125;
}
@keyframes uv-rotate-29b619ea {
0% {
    transform: rotate(0deg);
}
to {
    transform: rotate(1turn);
}
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
uni-view[data-v-a1a98476], uni-scroll-view[data-v-a1a98476], uni-swiper-item[data-v-a1a98476] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-loading-page[data-v-a1a98476] {
  flex: 1;
  justify-content: center;
  align-items: center;
  padding-bottom: 150px;
  transition-property: opacity;
}
.uv-loading-page__text[data-v-a1a98476] {
  margin-top: 10px;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
body[data-v-dd13009a] {
  background-color: #f5f5f5;
}
.transaction-container[data-v-dd13009a] {
  width: 100%;
  min-height: 100vh;
  background: #f5f5f5;
}
.page-header[data-v-dd13009a] {
  background: #fff;
  padding: 0.9375rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.1);
}
.header-left[data-v-dd13009a] {
  width: 1.875rem;
  height: 1.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header-center[data-v-dd13009a] {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header-right[data-v-dd13009a] {
  width: 1.875rem;
  height: 1.875rem;
}
.page-title[data-v-dd13009a] {
  font-size: 1.125rem;
  font-weight: bold;
  color: #333;
  margin-left: 0.46875rem;
}
.search-section[data-v-dd13009a] {
  background: #fff;
  margin: 0.625rem;
  border-radius: 0.375rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.1);
}
.input-wrapper[data-v-dd13009a] {
  background-color: #f5f6f7;
  border-radius: 0.625rem;
  display: flex;
  align-items: center;
  padding: 0 0.625rem;
  min-height: 2.8125rem;
}
.icon[data-v-dd13009a] {
  margin-right: 0.625rem;
  color: #999;
}
.input[data-v-dd13009a] {
  flex: 1;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}
.scan-btn[data-v-dd13009a] {
  width: 1.875rem;
  height: 1.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f2ff;
  border-radius: 0.375rem;
  margin-left: 0.625rem;
}
.transaction-list[data-v-dd13009a] {
  background: #fff;
  margin: 0.625rem;
  border-radius: 0.375rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.1);
}
.list-title[data-v-dd13009a] {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.9375rem;
  border-left: 0.1875rem solid #5677fc;
  padding-left: 0.625rem;
}
.transaction-item[data-v-dd13009a] {
  background: #f8f9fa;
  border-radius: 0.375rem;
  padding: 0.625rem;
  margin-bottom: 0.625rem;
}
.transaction-item[data-v-dd13009a]:last-child {
  margin-bottom: 0;
}
.transaction-header[data-v-dd13009a] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.46875rem;
}
.operation-type-tag[data-v-dd13009a] {
  padding: 0.25rem 0.5rem;
  border-radius: 0.625rem;
  font-size: 0.75rem;
  font-weight: bold;
  color: #fff;
}
.operation-type-tag.create[data-v-dd13009a] {
  background: #67c23a;
}
.operation-type-tag.feed[data-v-dd13009a] {
  background: #409eff;
}
.operation-type-tag.enter[data-v-dd13009a] {
  background: #67c23a;
}
.operation-type-tag.exit[data-v-dd13009a] {
  background: #e6a23c;
}
.operation-type-tag.warehouse[data-v-dd13009a] {
  background: #909399;
}
.operation-type-tag.edit[data-v-dd13009a] {
  background: #409eff;
}
.operation-type-tag.delete[data-v-dd13009a] {
  background: #f56c6c;
}
.operation-type-tag.default[data-v-dd13009a] {
  background: #c0c4cc;
}
.transaction-time[data-v-dd13009a] {
  font-size: 0.75rem;
  color: #999;
}
.transaction-details[data-v-dd13009a] {
  font-size: 0.8125rem;
}
.detail-row[data-v-dd13009a] {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.3125rem;
}
.detail-row[data-v-dd13009a]:last-child {
  margin-bottom: 0;
}
.detail-item[data-v-dd13009a] {
  flex: 1;
  margin-right: 0.625rem;
}
.detail-item[data-v-dd13009a]:last-child {
  margin-right: 0;
}
.detail-label[data-v-dd13009a] {
  color: #666;
  margin-right: 0.3125rem;
}
.detail-value[data-v-dd13009a] {
  color: #333;
  font-weight: 500;
}
.empty-state[data-v-dd13009a] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.125rem 1.25rem;
  text-align: center;
}
.empty-text[data-v-dd13009a] {
  font-size: 0.875rem;
  color: #999;
  margin-top: 0.9375rem;
}