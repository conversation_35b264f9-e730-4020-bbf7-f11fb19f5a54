/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-border[data-v-d5a7e73a] {
  border-width: 0.5px !important;
  border-color: #dadbde !important;
  border-style: solid;
}
.uv-border-bottom[data-v-d5a7e73a] {
  border-bottom-width: 0.5px !important;
  border-color: #dadbde !important;
  border-bottom-style: solid;
}
uni-view[data-v-d5a7e73a], uni-scroll-view[data-v-d5a7e73a], uni-swiper-item[data-v-d5a7e73a] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-textarea[data-v-d5a7e73a] {
  border-radius: 4px;
  background-color: #fff;
  position: relative;
  display: flex;
  flex-direction: row;
  flex: 1;
  padding: 9px;
}
.uv-textarea--radius[data-v-d5a7e73a] {
  border-radius: 4px;
}
.uv-textarea--no-radius[data-v-d5a7e73a] {
  border-radius: 0;
}
.uv-textarea--disabled[data-v-d5a7e73a] {
  background-color: #f5f7fa;
}
.uv-textarea__field[data-v-d5a7e73a] {
  flex: 1;
  font-size: 15px;
  color: #606266;
  width: 100%;
}
.uv-textarea__count[data-v-d5a7e73a] {
  position: absolute;
  right: 5px;
  bottom: 2px;
  font-size: 12px;
  color: #909193;
  background-color: #ffffff;
  padding: 1px 4px;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
uni-view[data-v-29b619ea], uni-scroll-view[data-v-29b619ea], uni-swiper-item[data-v-29b619ea] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-loading-icon[data-v-29b619ea] {
  flex-direction: row;
  align-items: center;
  justify-content: center;
  color: #c8c9cc;
}
.uv-loading-icon__text[data-v-29b619ea] {
  margin-left: 4px;
  color: #606266;
  font-size: 14px;
  line-height: 20px;
}
.uv-loading-icon__spinner[data-v-29b619ea] {
  width: 30px;
  height: 30px;
  position: relative;
  box-sizing: border-box;
  max-width: 100%;
  max-height: 100%;
  animation: uv-rotate-29b619ea 1s linear infinite;
}
.uv-loading-icon__spinner--semicircle[data-v-29b619ea] {
  border-width: 2px;
  border-color: transparent;
  border-top-right-radius: 100px;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  border-bottom-right-radius: 100px;
  border-style: solid;
}
.uv-loading-icon__spinner--circle[data-v-29b619ea] {
  border-top-right-radius: 100px;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  border-bottom-right-radius: 100px;
  border-width: 2px;
  border-top-color: #e5e5e5;
  border-right-color: #e5e5e5;
  border-bottom-color: #e5e5e5;
  border-left-color: #e5e5e5;
  border-style: solid;
}
.uv-loading-icon--vertical[data-v-29b619ea] {
  flex-direction: column;
}
[data-v-29b619ea]:host {
  font-size: 0px;
  line-height: 1;
}
.uv-loading-icon__spinner--spinner[data-v-29b619ea] {
  animation-timing-function: steps(12);
}
.uv-loading-icon__text[data-v-29b619ea]:empty {
  display: none;
}
.uv-loading-icon--vertical .uv-loading-icon__text[data-v-29b619ea] {
  margin: 6px 0 0;
  color: #606266;
}
.uv-loading-icon__dot[data-v-29b619ea] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.uv-loading-icon__dot[data-v-29b619ea]:before {
  display: block;
  width: 2px;
  height: 25%;
  margin: 0 auto;
  background-color: currentColor;
  border-radius: 40%;
  content: " ";
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(1) {
  transform: rotate(30deg);
  opacity: 1;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(2) {
  transform: rotate(60deg);
  opacity: 0.9375;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(3) {
  transform: rotate(90deg);
  opacity: 0.875;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(4) {
  transform: rotate(120deg);
  opacity: 0.8125;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(5) {
  transform: rotate(150deg);
  opacity: 0.75;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(6) {
  transform: rotate(180deg);
  opacity: 0.6875;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(7) {
  transform: rotate(210deg);
  opacity: 0.625;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(8) {
  transform: rotate(240deg);
  opacity: 0.5625;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(9) {
  transform: rotate(270deg);
  opacity: 0.5;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(10) {
  transform: rotate(300deg);
  opacity: 0.4375;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(11) {
  transform: rotate(330deg);
  opacity: 0.375;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(12) {
  transform: rotate(360deg);
  opacity: 0.3125;
}
@keyframes uv-rotate-29b619ea {
0% {
    transform: rotate(0deg);
}
to {
    transform: rotate(1turn);
}
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
uni-view[data-v-b7a6dd5d], uni-scroll-view[data-v-b7a6dd5d], uni-swiper-item[data-v-b7a6dd5d] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
@font-face {
  font-family: "uvicon-iconfont";
  src: url("../../../assets/uvicons.04d281cc.ttf") format("truetype");
}
.uv-icon[data-v-b7a6dd5d] {
  display: flex;
  align-items: center;
}
.uv-icon--left[data-v-b7a6dd5d] {
  flex-direction: row-reverse;
  align-items: center;
}
.uv-icon--right[data-v-b7a6dd5d] {
  flex-direction: row;
  align-items: center;
}
.uv-icon--top[data-v-b7a6dd5d] {
  flex-direction: column-reverse;
  justify-content: center;
}
.uv-icon--bottom[data-v-b7a6dd5d] {
  flex-direction: column;
  justify-content: center;
}
.uv-icon__icon[data-v-b7a6dd5d] {
  font-family: uvicon-iconfont;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.uv-icon__icon--primary[data-v-b7a6dd5d] {
  color: #3c9cff;
}
.uv-icon__icon--success[data-v-b7a6dd5d] {
  color: #5ac725;
}
.uv-icon__icon--error[data-v-b7a6dd5d] {
  color: #f56c6c;
}
.uv-icon__icon--warning[data-v-b7a6dd5d] {
  color: #f9ae3d;
}
.uv-icon__icon--info[data-v-b7a6dd5d] {
  color: #909399;
}
.uv-icon__img[data-v-b7a6dd5d] {
  height: auto;
  will-change: transform;
}
.uv-icon__label[data-v-b7a6dd5d] {
  line-height: 1;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-reset-button[data-v-ae8e42c7] {
  padding: 0;
  background-color: transparent;
}
.uv-reset-button[data-v-ae8e42c7]::after {
  border: none;
}
uni-view[data-v-ae8e42c7], uni-scroll-view[data-v-ae8e42c7], uni-swiper-item[data-v-ae8e42c7] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-button-wrapper[data-v-ae8e42c7] {
  position: relative;
}
.uv-button-wrapper--dis[data-v-ae8e42c7] {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 9;
}
.uv-button[data-v-ae8e42c7] {
  width: 100%;
}
.uv-button__text[data-v-ae8e42c7] {
  white-space: nowrap;
  line-height: 1;
}
.uv-button[data-v-ae8e42c7]:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: inherit;
  border-radius: inherit;
  transform: translate(-50%, -50%);
  opacity: 0;
  content: " ";
  background-color: #000;
  border-color: #000;
}
.uv-button--active[data-v-ae8e42c7]:before {
  opacity: 0.15;
}
.uv-button__icon + .uv-button__text[data-v-ae8e42c7]:not(:empty), .uv-button__loading-text[data-v-ae8e42c7] {
  margin-left: 4px;
}
.uv-button--plain.uv-button--primary[data-v-ae8e42c7] {
  color: #3c9cff;
}
.uv-button--plain.uv-button--info[data-v-ae8e42c7] {
  color: #909399;
}
.uv-button--plain.uv-button--success[data-v-ae8e42c7] {
  color: #5ac725;
}
.uv-button--plain.uv-button--error[data-v-ae8e42c7] {
  color: #f56c6c;
}
.uv-button--plain.uv-button--warning[data-v-ae8e42c7] {
  color: #f9ae3d;
}
.uv-button[data-v-ae8e42c7] {
  height: 40px;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  flex-direction: row;
}
.uv-button__text[data-v-ae8e42c7] {
  font-size: 15px;
}
.uv-button__loading-text[data-v-ae8e42c7] {
  font-size: 15px;
  margin-left: 4px;
}
.uv-button--large[data-v-ae8e42c7] {
  width: 100%;
  height: 50px;
  padding: 0 15px;
}
.uv-button--normal[data-v-ae8e42c7] {
  padding: 0 12px;
  font-size: 14px;
}
.uv-button--small[data-v-ae8e42c7] {
  min-width: 60px;
  height: 30px;
  padding: 0px 8px;
  font-size: 12px;
}
.uv-button--mini[data-v-ae8e42c7] {
  height: 22px;
  font-size: 10px;
  min-width: 50px;
  padding: 0px 8px;
}
.uv-button--disabled[data-v-ae8e42c7] {
  opacity: 0.5;
}
.uv-button--info[data-v-ae8e42c7] {
  color: #323233;
  background-color: #fff;
  border-color: #ebedf0;
  border-width: 1px;
  border-style: solid;
}
.uv-button--success[data-v-ae8e42c7] {
  color: #fff;
  background-color: #5ac725;
  border-color: #5ac725;
  border-width: 1px;
  border-style: solid;
}
.uv-button--primary[data-v-ae8e42c7] {
  color: #fff;
  background-color: #3c9cff;
  border-color: #3c9cff;
  border-width: 1px;
  border-style: solid;
}
.uv-button--error[data-v-ae8e42c7] {
  color: #fff;
  background-color: #f56c6c;
  border-color: #f56c6c;
  border-width: 1px;
  border-style: solid;
}
.uv-button--warning[data-v-ae8e42c7] {
  color: #fff;
  background-color: #f9ae3d;
  border-color: #f9ae3d;
  border-width: 1px;
  border-style: solid;
}
.uv-button--block[data-v-ae8e42c7] {
  display: flex;
  flex-direction: row;
  width: 100%;
}
.uv-button--circle[data-v-ae8e42c7] {
  border-top-right-radius: 100px;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  border-bottom-right-radius: 100px;
}
.uv-button--square[data-v-ae8e42c7] {
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.uv-button__icon[data-v-ae8e42c7] {
  min-width: 1em;
  line-height: inherit !important;
  vertical-align: top;
}
.uv-button--plain[data-v-ae8e42c7] {
  background-color: #fff;
}
.uv-button--hairline[data-v-ae8e42c7] {
  border-width: 0.5px !important;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-border-bottom[data-v-a311a468] {
  border-bottom-width: 0.5px !important;
  border-color: #dadbde !important;
  border-bottom-style: solid;
}
uni-view[data-v-a311a468], uni-scroll-view[data-v-a311a468], uni-swiper-item[data-v-a311a468] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-checkbox[data-v-a311a468] {
  display: flex;
  flex-direction: row;
  overflow: hidden;
  flex-direction: row;
  align-items: center;
}
.uv-checkbox-label--left[data-v-a311a468] {
  flex-direction: row;
}
.uv-checkbox-label--right[data-v-a311a468] {
  flex-direction: row-reverse;
  justify-content: space-between;
}
.uv-checkbox__icon-wrap[data-v-a311a468] {
  box-sizing: border-box;
  transition-property: border-color, background-color, color;
  transition-duration: 0.2s;
  color: #606266;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  color: transparent;
  text-align: center;
  font-size: 6px;
  border-width: 1px;
  border-color: #c8c9cc;
  border-style: solid;
}
.uv-checkbox__icon-wrap--circle[data-v-a311a468] {
  border-radius: 100%;
}
.uv-checkbox__icon-wrap--square[data-v-a311a468] {
  border-radius: 3px;
}
.uv-checkbox__icon-wrap--checked[data-v-a311a468] {
  color: #fff;
  background-color: red;
  border-color: #2979ff;
}
.uv-checkbox__icon-wrap--disabled[data-v-a311a468] {
  background-color: #ebedf0 !important;
}
.uv-checkbox__icon-wrap--disabled--checked[data-v-a311a468] {
  color: #c8c9cc !important;
}
.uv-checkbox__label[data-v-a311a468] {
  word-wrap: break-word;
  margin-left: 5px;
  margin-right: 12px;
  color: #606266;
  font-size: 15px;
}
.uv-checkbox__label--disabled[data-v-a311a468] {
  color: #c8c9cc;
}
.uv-checkbox__label-wrap[data-v-a311a468] {
  flex: 1;
  padding-left: 6px;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
uni-view[data-v-d2bffd23], uni-scroll-view[data-v-d2bffd23], uni-swiper-item[data-v-d2bffd23] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-col[data-v-d2bffd23] {
  padding: 0;
  box-sizing: border-box;
}
.uv-col-0[data-v-d2bffd23] {
  width: 0;
}
.uv-col-1[data-v-d2bffd23] {
  width: 8.3333333333%;
}
.uv-col-2[data-v-d2bffd23] {
  width: 16.6666666667%;
}
.uv-col-3[data-v-d2bffd23] {
  width: 25%;
}
.uv-col-4[data-v-d2bffd23] {
  width: 33.3333333333%;
}
.uv-col-5[data-v-d2bffd23] {
  width: 41.6666666667%;
}
.uv-col-6[data-v-d2bffd23] {
  width: 50%;
}
.uv-col-7[data-v-d2bffd23] {
  width: 58.3333333333%;
}
.uv-col-8[data-v-d2bffd23] {
  width: 66.6666666667%;
}
.uv-col-9[data-v-d2bffd23] {
  width: 75%;
}
.uv-col-10[data-v-d2bffd23] {
  width: 83.3333333333%;
}
.uv-col-11[data-v-d2bffd23] {
  width: 91.6666666667%;
}
.uv-col-12[data-v-d2bffd23] {
  width: 100%;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
uni-view[data-v-b10c8d02], uni-scroll-view[data-v-b10c8d02], uni-swiper-item[data-v-b10c8d02] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-tags-wrapper[data-v-b10c8d02] {
  position: relative;
}
.uv-tags[data-v-b10c8d02] {
  display: flex;
  flex-direction: row;
  align-items: center;
  border-style: solid;
}
.uv-tags--circle[data-v-b10c8d02] {
  border-radius: 100px;
}
.uv-tags--square[data-v-b10c8d02] {
  border-radius: 3px;
}
.uv-tags__icon[data-v-b10c8d02] {
  margin-right: 4px;
}
.uv-tags__text--mini[data-v-b10c8d02] {
  font-size: 12px;
  line-height: 12px;
}
.uv-tags__text--medium[data-v-b10c8d02] {
  font-size: 13px;
  line-height: 13px;
}
.uv-tags__text--large[data-v-b10c8d02] {
  font-size: 15px;
  line-height: 15px;
}
.uv-tags--mini[data-v-b10c8d02] {
  height: 22px;
  line-height: 22px;
  padding: 0 5px;
}
.uv-tags--mini--right[data-v-b10c8d02] {
  padding-right: 2px;
}
.uv-tags--medium[data-v-b10c8d02] {
  height: 26px;
  line-height: 22px;
  padding: 0 10px;
}
.uv-tags--medium--right[data-v-b10c8d02] {
  padding: 0 4px 0 8px;
}
.uv-tags--large[data-v-b10c8d02] {
  height: 32px;
  line-height: 32px;
  padding: 0 15px;
}
.uv-tags--large--right[data-v-b10c8d02] {
  padding: 0 4px 0 8px;
}
.uv-tags--primary[data-v-b10c8d02] {
  background-color: #3c9cff;
  border-width: 1px;
  border-color: #3c9cff;
}
.uv-tags--primary--plain[data-v-b10c8d02] {
  border-width: 1px;
  border-color: #3c9cff;
}
.uv-tags--primary--plain--fill[data-v-b10c8d02] {
  background-color: #ecf5ff;
}
.uv-tags__text--primary[data-v-b10c8d02] {
  color: #FFFFFF;
}
.uv-tags__text--primary--plain[data-v-b10c8d02] {
  color: #3c9cff;
}
.uv-tags--error[data-v-b10c8d02] {
  background-color: #f56c6c;
  border-width: 1px;
  border-color: #f56c6c;
}
.uv-tags--error--plain[data-v-b10c8d02] {
  border-width: 1px;
  border-color: #f56c6c;
}
.uv-tags--error--plain--fill[data-v-b10c8d02] {
  background-color: #fef0f0;
}
.uv-tags__text--error[data-v-b10c8d02] {
  color: #FFFFFF;
}
.uv-tags__text--error--plain[data-v-b10c8d02] {
  color: #f56c6c;
}
.uv-tags--warning[data-v-b10c8d02] {
  background-color: #f9ae3d;
  border-width: 1px;
  border-color: #f9ae3d;
}
.uv-tags--warning--plain[data-v-b10c8d02] {
  border-width: 1px;
  border-color: #f9ae3d;
}
.uv-tags--warning--plain--fill[data-v-b10c8d02] {
  background-color: #fdf6ec;
}
.uv-tags__text--warning[data-v-b10c8d02] {
  color: #FFFFFF;
}
.uv-tags__text--warning--plain[data-v-b10c8d02] {
  color: #f9ae3d;
}
.uv-tags--success[data-v-b10c8d02] {
  background-color: #5ac725;
  border-width: 1px;
  border-color: #5ac725;
}
.uv-tags--success--plain[data-v-b10c8d02] {
  border-width: 1px;
  border-color: #5ac725;
}
.uv-tags--success--plain--fill[data-v-b10c8d02] {
  background-color: #f5fff0;
}
.uv-tags__text--success[data-v-b10c8d02] {
  color: #FFFFFF;
}
.uv-tags__text--success--plain[data-v-b10c8d02] {
  color: #5ac725;
}
.uv-tags--info[data-v-b10c8d02] {
  background-color: #909399;
  border-width: 1px;
  border-color: #909399;
}
.uv-tags--info--plain[data-v-b10c8d02] {
  border-width: 1px;
  border-color: #909399;
}
.uv-tags--info--plain--fill[data-v-b10c8d02] {
  background-color: #f4f4f5;
}
.uv-tags__text--info[data-v-b10c8d02] {
  color: #FFFFFF;
}
.uv-tags__text--info--plain[data-v-b10c8d02] {
  color: #909399;
}
.uv-tags__close[data-v-b10c8d02] {
  border-radius: 100px;
  background-color: #C6C7CB;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  transform: scale(0.6);
}
.uv-tags__close--right-top[data-v-b10c8d02] {
  position: absolute;
  z-index: 999;
  top: 10px;
  right: 10px;
  transform: scale(0.6) translate(80%, -80%);
}
.uv-tags__close--mini[data-v-b10c8d02] {
  width: 18px;
  height: 18px;
}
.uv-tags__close--medium[data-v-b10c8d02] {
  width: 22px;
  height: 22px;
}
.uv-tags__close--large[data-v-b10c8d02] {
  width: 25px;
  height: 25px;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
uni-view[data-v-692ff899], uni-scroll-view[data-v-692ff899], uni-swiper-item[data-v-692ff899] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-row[data-v-692ff899] {
  display: flex;
  flex-direction: row;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
uni-view[data-v-6efcec67], uni-scroll-view[data-v-6efcec67], uni-swiper-item[data-v-6efcec67] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-empty[data-v-6efcec67] {
  display: flex;
  flex-direction: row;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.uv-empty__text[data-v-6efcec67] {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 0.625rem;
}
.uv-slot-wrap[data-v-6efcec67] {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 0.625rem;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-line[data-v-dcf8cb8f] {
  vertical-align: middle;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-overlay[data-v-7303e1aa] {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-status-bar[data-v-f5bd6f5a] {
  width: 100%;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-safe-bottom[data-v-560f16b2] {
  width: 100%;
}
.uv-safe-area-inset-top[data-v-560f16b2] {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.uv-safe-area-inset-right[data-v-560f16b2] {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.uv-safe-area-inset-bottom[data-v-560f16b2] {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.uv-safe-area-inset-left[data-v-560f16b2] {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-popup[data-v-01a3ad6e] {
  position: fixed;
  z-index: 99;
}
.uv-popup.top[data-v-01a3ad6e], .uv-popup.left[data-v-01a3ad6e], .uv-popup.right[data-v-01a3ad6e] {
  top: 0;
}
.uv-popup .uv-popup__content[data-v-01a3ad6e] {
  display: block;
  overflow: hidden;
  position: relative;
}
.uv-popup .uv-popup__content.left[data-v-01a3ad6e], .uv-popup .uv-popup__content.right[data-v-01a3ad6e] {
  padding-top: 0;
  flex: 1;
}
.uv-popup .uv-popup__content__close[data-v-01a3ad6e] {
  position: absolute;
}
.uv-popup .uv-popup__content__close--hover[data-v-01a3ad6e] {
  opacity: 0.4;
}
.uv-popup .uv-popup__content__close--top-left[data-v-01a3ad6e] {
  top: 15px;
  left: 15px;
}
.uv-popup .uv-popup__content__close--top-right[data-v-01a3ad6e] {
  top: 15px;
  right: 15px;
}
.uv-popup .uv-popup__content__close--bottom-left[data-v-01a3ad6e] {
  bottom: 15px;
  left: 15px;
}
.uv-popup .uv-popup__content__close--bottom-right[data-v-01a3ad6e] {
  right: 15px;
  bottom: 15px;
}
.fixforpc-z-index[data-v-01a3ad6e] {
  z-index: 999;
}
.fixforpc-top[data-v-01a3ad6e] {
  top: 0;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
uni-view[data-v-4b4aa5ec], uni-scroll-view[data-v-4b4aa5ec], uni-swiper-item[data-v-4b4aa5ec] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-modal[data-v-4b4aa5ec] {
  width: 20.3125rem;
  border-radius: 6px;
  overflow: hidden;
}
.uv-modal__title[data-v-4b4aa5ec] {
  font-size: 16px;
  font-weight: bold;
  color: #606266;
  text-align: center;
  padding-top: 25px;
}
.uv-modal__content[data-v-4b4aa5ec] {
  padding: 12px 25px 25px 25px;
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.uv-modal__content__text[data-v-4b4aa5ec] {
  line-height: 1.5rem;
  font-size: 15px;
  color: #606266;
  flex: 1;
}
.uv-modal__button-group[data-v-4b4aa5ec] {
  display: flex;
  flex-direction: row;
  height: 48px;
}
.uv-modal__button-group__wrapper[data-v-4b4aa5ec] {
  flex: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 48px;
}
.uv-modal__button-group__wrapper--confirm[data-v-4b4aa5ec], .uv-modal__button-group__wrapper--only-cancel[data-v-4b4aa5ec] {
  border-bottom-right-radius: 6px;
}
.uv-modal__button-group__wrapper--cancel[data-v-4b4aa5ec], .uv-modal__button-group__wrapper--only-confirm[data-v-4b4aa5ec] {
  border-bottom-left-radius: 6px;
}
.uv-modal__button-group__wrapper--hover[data-v-4b4aa5ec] {
  background-color: #f3f4f6;
}
.uv-modal__button-group__wrapper__text[data-v-4b4aa5ec] {
  color: #606266;
  font-size: 16px;
  text-align: center;
}

.warehouse-container[data-v-ea83b398] {
    padding: 0.625rem;
    background: #f5f5f5;
    min-height: 100vh;
}
.search-section[data-v-ea83b398] {
    background: #fff;
    border-radius: 0.375rem;
    padding: 0.9375rem;
    margin-bottom: 0.625rem;
}
.search-title[data-v-ea83b398] {
    font-size: 1rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.3125rem;
}
.search-desc[data-v-ea83b398] {
    font-size: 0.8125rem;
    color: #666;
    margin-bottom: 0.625rem;
}
.search-buttons[data-v-ea83b398] {
    display: flex;
    gap: 0.625rem;
    margin-top: 0.625rem;
}
.batch-list[data-v-ea83b398] {
    background: #fff;
    border-radius: 0.375rem;
    padding: 0.9375rem;
    margin-bottom: 0.625rem;
}
.list-title[data-v-ea83b398] {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9375rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.625rem;
    padding-bottom: 0.625rem;
    border-bottom: 0.03125rem solid #f0f0f0;
}
.batch-item[data-v-ea83b398] {
    border: 0.0625rem solid #e4e7ed;
    border-radius: 0.25rem;
    padding: 0.625rem;
    margin-bottom: 0.46875rem;
    transition: all 0.3s;
}
.batch-item.selected[data-v-ea83b398] {
    border-color: #5677fc;
    background: #f0f4ff;
}
.batch-item.disabled[data-v-ea83b398] {
    opacity: 0.6;
    background: #f5f5f5;
}
.item-header[data-v-ea83b398] {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.46875rem;
}
.batch-id[data-v-ea83b398] {
    font-size: 0.9375rem;
    font-weight: bold;
    color: #333;
}
.item-content .label[data-v-ea83b398] {
    font-size: 0.8125rem;
    color: #666;
    margin-right: 0.3125rem;
}
.item-content .value[data-v-ea83b398] {
    font-size: 0.8125rem;
    color: #333;
}
.item-status[data-v-ea83b398] {
    text-align: center;
    margin-top: 0.46875rem;
    padding-top: 0.46875rem;
    border-top: 0.03125rem solid #e4e7ed;
}
.status-text[data-v-ea83b398] {
    font-size: 0.75rem;
    color: #909399;
}
.action-section[data-v-ea83b398] {
    background: #fff;
    border-radius: 0.375rem;
    padding: 0.9375rem;
    position: fixed;
    bottom: 0.625rem;
    left: 0.625rem;
    right: 0.625rem;
    box-shadow: 0 -0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.action-info[data-v-ea83b398] {
    font-size: 0.875rem;
    color: #333;
    text-align: center;
    margin-bottom: 0.625rem;
}
.modal-content[data-v-ea83b398] {
    padding: 0.625rem 0;
}
.confirm-text[data-v-ea83b398] {
    font-size: 0.875rem;
    color: #333;
    margin-bottom: 0.625rem;
}
.batch-summary[data-v-ea83b398] {
    max-height: 6.25rem;
    overflow-y: auto;
    background: #f8f9fa;
    border-radius: 0.25rem;
    padding: 0.46875rem;
    margin-bottom: 0.625rem;
}
.summary-item[data-v-ea83b398] {
    display: flex;
    justify-content: space-between;
    padding: 0.25rem 0;
    border-bottom: 0.03125rem solid #e4e7ed;
}
.summary-item[data-v-ea83b398]:last-child {
    border-bottom: none;
}
.summary-id[data-v-ea83b398] {
    font-size: 0.8125rem;
    font-weight: bold;
    color: #333;
}
.summary-product[data-v-ea83b398] {
    font-size: 0.75rem;
    color: #666;
}
.remark-section[data-v-ea83b398] {
    margin-top: 0.625rem;
}
.remark-label[data-v-ea83b398] {
    font-size: 0.8125rem;
    color: #333;
    margin-bottom: 0.3125rem;
}
