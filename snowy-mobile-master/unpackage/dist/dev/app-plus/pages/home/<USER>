/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
uni-view[data-v-29b619ea], uni-scroll-view[data-v-29b619ea], uni-swiper-item[data-v-29b619ea] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-loading-icon[data-v-29b619ea] {
  flex-direction: row;
  align-items: center;
  justify-content: center;
  color: #c8c9cc;
}
.uv-loading-icon__text[data-v-29b619ea] {
  margin-left: 4px;
  color: #606266;
  font-size: 14px;
  line-height: 20px;
}
.uv-loading-icon__spinner[data-v-29b619ea] {
  width: 30px;
  height: 30px;
  position: relative;
  box-sizing: border-box;
  max-width: 100%;
  max-height: 100%;
  animation: uv-rotate-29b619ea 1s linear infinite;
}
.uv-loading-icon__spinner--semicircle[data-v-29b619ea] {
  border-width: 2px;
  border-color: transparent;
  border-top-right-radius: 100px;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  border-bottom-right-radius: 100px;
  border-style: solid;
}
.uv-loading-icon__spinner--circle[data-v-29b619ea] {
  border-top-right-radius: 100px;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  border-bottom-right-radius: 100px;
  border-width: 2px;
  border-top-color: #e5e5e5;
  border-right-color: #e5e5e5;
  border-bottom-color: #e5e5e5;
  border-left-color: #e5e5e5;
  border-style: solid;
}
.uv-loading-icon--vertical[data-v-29b619ea] {
  flex-direction: column;
}
[data-v-29b619ea]:host {
  font-size: 0px;
  line-height: 1;
}
.uv-loading-icon__spinner--spinner[data-v-29b619ea] {
  animation-timing-function: steps(12);
}
.uv-loading-icon__text[data-v-29b619ea]:empty {
  display: none;
}
.uv-loading-icon--vertical .uv-loading-icon__text[data-v-29b619ea] {
  margin: 6px 0 0;
  color: #606266;
}
.uv-loading-icon__dot[data-v-29b619ea] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.uv-loading-icon__dot[data-v-29b619ea]:before {
  display: block;
  width: 2px;
  height: 25%;
  margin: 0 auto;
  background-color: currentColor;
  border-radius: 40%;
  content: " ";
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(1) {
  transform: rotate(30deg);
  opacity: 1;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(2) {
  transform: rotate(60deg);
  opacity: 0.9375;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(3) {
  transform: rotate(90deg);
  opacity: 0.875;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(4) {
  transform: rotate(120deg);
  opacity: 0.8125;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(5) {
  transform: rotate(150deg);
  opacity: 0.75;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(6) {
  transform: rotate(180deg);
  opacity: 0.6875;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(7) {
  transform: rotate(210deg);
  opacity: 0.625;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(8) {
  transform: rotate(240deg);
  opacity: 0.5625;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(9) {
  transform: rotate(270deg);
  opacity: 0.5;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(10) {
  transform: rotate(300deg);
  opacity: 0.4375;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(11) {
  transform: rotate(330deg);
  opacity: 0.375;
}
.uv-loading-icon__dot[data-v-29b619ea]:nth-of-type(12) {
  transform: rotate(360deg);
  opacity: 0.3125;
}
@keyframes uv-rotate-29b619ea {
0% {
    transform: rotate(0deg);
}
to {
    transform: rotate(1turn);
}
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
uni-view[data-v-09034092], uni-scroll-view[data-v-09034092], uni-swiper-item[data-v-09034092] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-swiper-indicator__wrapper[data-v-09034092] {
  display: flex;
  flex-direction: row;
}
.uv-swiper-indicator__wrapper--line[data-v-09034092] {
  border-radius: 100px;
  height: 4px;
}
.uv-swiper-indicator__wrapper--line__bar[data-v-09034092] {
  width: 22px;
  height: 4px;
  border-radius: 100px;
  background-color: #FFFFFF;
  transition: transform 0.3s;
}
.uv-swiper-indicator__wrapper__dot[data-v-09034092] {
  width: 5px;
  height: 5px;
  border-radius: 100px;
  margin: 0 4px;
}
.uv-swiper-indicator__wrapper__dot--active[data-v-09034092] {
  width: 12px;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-line-1[data-v-7522af0b] {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical !important;
}
.uv-line-2[data-v-7522af0b] {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.uv-line-3[data-v-7522af0b] {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.uv-line-4[data-v-7522af0b] {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.uv-line-5[data-v-7522af0b] {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
uni-view[data-v-7522af0b], uni-scroll-view[data-v-7522af0b], uni-swiper-item[data-v-7522af0b] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-swiper[data-v-7522af0b] {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}
.uv-swiper__wrapper[data-v-7522af0b] {
  flex: 1;
}
.uv-swiper__wrapper__item[data-v-7522af0b] {
  flex: 1;
}
.uv-swiper__wrapper__item__wrapper[data-v-7522af0b] {
  display: flex;
  flex-direction: row;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s;
  flex: 1;
}
.uv-swiper__wrapper__item__wrapper__image[data-v-7522af0b] {
  flex: 1;
}
.uv-swiper__wrapper__item__wrapper__video[data-v-7522af0b] {
  flex: 1;
}
.uv-swiper__wrapper__item__wrapper__title[data-v-7522af0b] {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
  bottom: 0;
  left: 0;
  right: 0;
  font-size: 0.875rem;
  height: 1.875rem;
  line-height: 1.875rem;
  color: #FFFFFF;
  flex: 1;
}
.uv-swiper__indicator[data-v-7522af0b] {
  position: absolute;
  bottom: 10px;
}

.tui-section[data-v-2c303f6d] {

		width: 100%;
		box-sizing: border-box;
}
.tui-section__title[data-v-2c303f6d] {
		position: relative;

		display: flex;
		word-break: break-all;
		flex-shrink: 0;

		flex-direction: row;
		align-items: center;
}
.tui-section__decorate[data-v-2c303f6d] {
		position: absolute;
}
.tui-section__sub[data-v-2c303f6d] {

		word-break: break-all;
}
.tui-section__descr[data-v-2c303f6d] {
		font-weight: 400;
}


.container[data-v-0e645258] {
  width: 30px;
  height: 30px;
  position: relative;
}
.container.loading1[data-v-0e645258] {
  transform: rotate(45deg);
}
.container .shape[data-v-0e645258] {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 1px;
}
.container .shape.shape1[data-v-0e645258] {
  left: 0;
  background-color: #1890FF;
}
.container .shape.shape2[data-v-0e645258] {
  right: 0;
  background-color: #91CB74;
}
.container .shape.shape3[data-v-0e645258] {
  bottom: 0;
  background-color: #FAC858;
}
.container .shape.shape4[data-v-0e645258] {
  bottom: 0;
  right: 0;
  background-color: #EE6666;
}
.loading1 .shape1[data-v-0e645258] {
  animation: animation1shape1-0e645258 0.5s ease 0s infinite alternate;
}
@keyframes animation1shape1-0e645258 {
from {
    transform: translate(0, 0);
}
to {
    transform: translate(16px, 16px);
}
}
.loading1 .shape2[data-v-0e645258] {
  animation: animation1shape2-0e645258 0.5s ease 0s infinite alternate;
}
@keyframes animation1shape2-0e645258 {
from {
    transform: translate(0, 0);
}
to {
    transform: translate(-16px, 16px);
}
}
.loading1 .shape3[data-v-0e645258] {
  animation: animation1shape3-0e645258 0.5s ease 0s infinite alternate;
}
@keyframes animation1shape3-0e645258 {
from {
    transform: translate(0, 0);
}
to {
    transform: translate(16px, -16px);
}
}
.loading1 .shape4[data-v-0e645258] {
  animation: animation1shape4-0e645258 0.5s ease 0s infinite alternate;
}
@keyframes animation1shape4-0e645258 {
from {
    transform: translate(0, 0);
}
to {
    transform: translate(-16px, -16px);
}
}




.container[data-v-3df48dc2] {
  width: 30px;
  height: 30px;
  position: relative;
}
.container.loading2[data-v-3df48dc2] {
  transform: rotate(10deg);
}
.container.loading2 .shape[data-v-3df48dc2] {
  border-radius: 5px;
}
.container.loading2[data-v-3df48dc2]{
  animation: rotation 1s infinite;
}
.container .shape[data-v-3df48dc2] {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 1px;
}
.container .shape.shape1[data-v-3df48dc2] {
  left: 0;
  background-color: #1890FF;
}
.container .shape.shape2[data-v-3df48dc2] {
  right: 0;
  background-color: #91CB74;
}
.container .shape.shape3[data-v-3df48dc2] {
  bottom: 0;
  background-color: #FAC858;
}
.container .shape.shape4[data-v-3df48dc2] {
  bottom: 0;
  right: 0;
  background-color: #EE6666;
}
.loading2 .shape1[data-v-3df48dc2] {
  animation: animation2shape1-3df48dc2 0.5s ease 0s infinite alternate;
}
@keyframes animation2shape1-3df48dc2 {
from {
    transform: translate(0, 0);
}
to {
    transform: translate(20px, 20px);
}
}
.loading2 .shape2[data-v-3df48dc2] {
  animation: animation2shape2-3df48dc2 0.5s ease 0s infinite alternate;
}
@keyframes animation2shape2-3df48dc2 {
from {
    transform: translate(0, 0);
}
to {
    transform: translate(-20px, 20px);
}
}
.loading2 .shape3[data-v-3df48dc2] {
  animation: animation2shape3-3df48dc2 0.5s ease 0s infinite alternate;
}
@keyframes animation2shape3-3df48dc2 {
from {
    transform: translate(0, 0);
}
to {
    transform: translate(20px, -20px);
}
}
.loading2 .shape4[data-v-3df48dc2] {
  animation: animation2shape4-3df48dc2 0.5s ease 0s infinite alternate;
}
@keyframes animation2shape4-3df48dc2 {
from {
    transform: translate(0, 0);
}
to {
    transform: translate(-20px, -20px);
}
}



.container[data-v-27a8293c] {
  width: 30px;
  height: 30px;
  position: relative;
}
.container.loading3[data-v-27a8293c] {
  animation: rotation 1s infinite;
}
.container.loading3 .shape1[data-v-27a8293c] {
  border-top-left-radius: 10px;
}
.container.loading3 .shape2[data-v-27a8293c] {
  border-top-right-radius: 10px;
}
.container.loading3 .shape3[data-v-27a8293c] {
  border-bottom-left-radius: 10px;
}
.container.loading3 .shape4[data-v-27a8293c] {
  border-bottom-right-radius: 10px;
}
.container .shape[data-v-27a8293c] {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 1px;
}
.container .shape.shape1[data-v-27a8293c] {
  left: 0;
  background-color: #1890FF;
}
.container .shape.shape2[data-v-27a8293c] {
  right: 0;
  background-color: #91CB74;
}
.container .shape.shape3[data-v-27a8293c] {
  bottom: 0;
  background-color: #FAC858;
}
.container .shape.shape4[data-v-27a8293c] {
  bottom: 0;
  right: 0;
  background-color: #EE6666;
}
.loading3 .shape1[data-v-27a8293c] {
  animation: animation3shape1-27a8293c 0.5s ease 0s infinite alternate;
}
@keyframes animation3shape1-27a8293c {
from {
    transform: translate(0, 0);
}
to {
    transform: translate(5px, 5px);
}
}
.loading3 .shape2[data-v-27a8293c] {
  animation: animation3shape2-27a8293c 0.5s ease 0s infinite alternate;
}
@keyframes animation3shape2-27a8293c {
from {
    transform: translate(0, 0);
}
to {
    transform: translate(-5px, 5px);
}
}
.loading3 .shape3[data-v-27a8293c] {
  animation: animation3shape3-27a8293c 0.5s ease 0s infinite alternate;
}
@keyframes animation3shape3-27a8293c {
from {
    transform: translate(0, 0);
}
to {
    transform: translate(5px, -5px);
}
}
.loading3 .shape4[data-v-27a8293c] {
  animation: animation3shape4-27a8293c 0.5s ease 0s infinite alternate;
}
@keyframes animation3shape4-27a8293c {
from {
    transform: translate(0, 0);
}
to {
    transform: translate(-5px, -5px);
}
}


.container[data-v-2e7deb83] {
  width: 30px;
  height: 30px;
  position: relative;
}
.container.loading5 .shape[data-v-2e7deb83] {
  width: 15px;
  height: 15px;
}
.container .shape[data-v-2e7deb83] {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 1px;
}
.container .shape.shape1[data-v-2e7deb83] {
  left: 0;
  background-color: #1890FF;
}
.container .shape.shape2[data-v-2e7deb83] {
  right: 0;
  background-color: #91CB74;
}
.container .shape.shape3[data-v-2e7deb83] {
  bottom: 0;
  background-color: #FAC858;
}
.container .shape.shape4[data-v-2e7deb83] {
  bottom: 0;
  right: 0;
  background-color: #EE6666;
}
.loading5 .shape1[data-v-2e7deb83] {
  animation: animation5shape1-2e7deb83 2s ease 0s infinite reverse;
}
@keyframes animation5shape1-2e7deb83 {
0% {
    transform: translate(0, 0);
}
25% {
    transform: translate(0, 15px);
}
50% {
    transform: translate(15px, 15px);
}
75% {
    transform: translate(15px, 0);
}
}
.loading5 .shape2[data-v-2e7deb83] {
  animation: animation5shape2-2e7deb83 2s ease 0s infinite reverse;
}
@keyframes animation5shape2-2e7deb83 {
0% {
    transform: translate(0, 0);
}
25% {
    transform: translate(-15px, 0);
}
50% {
    transform: translate(-15px, 15px);
}
75% {
    transform: translate(0, 15px);
}
}
.loading5 .shape3[data-v-2e7deb83] {
  animation: animation5shape3-2e7deb83 2s ease 0s infinite reverse;
}
@keyframes animation5shape3-2e7deb83 {
0% {
    transform: translate(0, 0);
}
25% {
    transform: translate(15px, 0);
}
50% {
    transform: translate(15px, -15px);
}
75% {
    transform: translate(0, -15px);
}
}
.loading5 .shape4[data-v-2e7deb83] {
  animation: animation5shape4-2e7deb83 2s ease 0s infinite reverse;
}
@keyframes animation5shape4-2e7deb83 {
0% {
    transform: translate(0, 0);
}
25% {
    transform: translate(0, -15px);
}
50% {
    transform: translate(-15px, -15px);
}
75% {
    transform: translate(-15px, 0);
}
}



.container[data-v-ef674bbb] {
  width: 30px;
  height: 30px;
  position: relative;
}
.container.loading6[data-v-ef674bbb] {
  animation: rotation 1s infinite;
}
.container.loading6 .shape[data-v-ef674bbb] {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}
.container .shape[data-v-ef674bbb] {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 1px;
}
.container .shape.shape1[data-v-ef674bbb] {
  left: 0;
  background-color: #1890FF;
}
.container .shape.shape2[data-v-ef674bbb] {
  right: 0;
  background-color: #91CB74;
}
.container .shape.shape3[data-v-ef674bbb] {
  bottom: 0;
  background-color: #FAC858;
}
.container .shape.shape4[data-v-ef674bbb] {
  bottom: 0;
  right: 0;
  background-color: #EE6666;
}
.loading6 .shape1[data-v-ef674bbb] {
  animation: animation6shape1-ef674bbb 2s linear 0s infinite normal;
}
@keyframes animation6shape1-ef674bbb {
0% {
    transform: translate(0, 0);
}
25% {
    transform: translate(0, 18px);
}
50% {
    transform: translate(18px, 18px);
}
75% {
    transform: translate(18px, 0);
}
}
.loading6 .shape2[data-v-ef674bbb] {
  animation: animation6shape2-ef674bbb 2s linear 0s infinite normal;
}
@keyframes animation6shape2-ef674bbb {
0% {
    transform: translate(0, 0);
}
25% {
    transform: translate(-18px, 0);
}
50% {
    transform: translate(-18px, 18px);
}
75% {
    transform: translate(0, 18px);
}
}
.loading6 .shape3[data-v-ef674bbb] {
  animation: animation6shape3-ef674bbb 2s linear 0s infinite normal;
}
@keyframes animation6shape3-ef674bbb {
0% {
    transform: translate(0, 0);
}
25% {
    transform: translate(18px, 0);
}
50% {
    transform: translate(18px, -18px);
}
75% {
    transform: translate(0, -18px);
}
}
.loading6 .shape4[data-v-ef674bbb] {
  animation: animation6shape4-ef674bbb 2s linear 0s infinite normal;
}
@keyframes animation6shape4-ef674bbb {
0% {
    transform: translate(0, 0);
}
25% {
    transform: translate(0, -18px);
}
50% {
    transform: translate(-18px, -18px);
}
75% {
    transform: translate(-18px, 0);
}
}


.chartsview[data-v-a99d579b] {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		justify-content: center;
		align-items: center;
}
.charts-font[data-v-a99d579b]{
		font-size: 14px;
		color: #CCCCCC;
		margin-top: 10px;
}
.charts-error[data-v-a99d579b]{
		width: 128px;
		height: 128px;
		background: url("data:image/png;base64,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");
		background-position: center;
}


.chartsview[data-v-0ca34aee] {
  width: 100%;
  height: 100%;
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-line-1[data-v-298cf9e4] {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical !important;
}
.uv-line-2[data-v-298cf9e4] {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.uv-line-3[data-v-298cf9e4] {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.uv-line-4[data-v-298cf9e4] {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.uv-line-5[data-v-298cf9e4] {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
.uv-border-bottom[data-v-298cf9e4] {
  border-bottom-width: 0.5px !important;
  border-color: #dadbde !important;
  border-bottom-style: solid;
}
.uv-hover-class[data-v-298cf9e4] {
  opacity: 0.7;
}
uni-view[data-v-298cf9e4], uni-scroll-view[data-v-298cf9e4], uni-swiper-item[data-v-298cf9e4] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-toolbar[data-v-298cf9e4] {
  height: 42px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.uv-toolbar__wrapper__cancel[data-v-298cf9e4] {
  color: #909193;
  font-size: 15px;
  padding: 0 15px;
}
.uv-toolbar__title[data-v-298cf9e4] {
  color: #303133;
  padding: 0 1.875rem;
  font-size: 16px;
  flex: 1;
  text-align: center;
}
.uv-toolbar__wrapper__confirm[data-v-298cf9e4] {
  color: #3c9cff;
  font-size: 15px;
  padding: 0 15px;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-overlay[data-v-7303e1aa] {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-status-bar[data-v-f5bd6f5a] {
  width: 100%;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-safe-bottom[data-v-560f16b2] {
  width: 100%;
}
.uv-safe-area-inset-top[data-v-560f16b2] {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.uv-safe-area-inset-right[data-v-560f16b2] {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.uv-safe-area-inset-bottom[data-v-560f16b2] {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.uv-safe-area-inset-left[data-v-560f16b2] {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
uni-view[data-v-b7a6dd5d], uni-scroll-view[data-v-b7a6dd5d], uni-swiper-item[data-v-b7a6dd5d] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
@font-face {
  font-family: "uvicon-iconfont";
  src: url("../../assets/uvicons.04d281cc.ttf") format("truetype");
}
.uv-icon[data-v-b7a6dd5d] {
  display: flex;
  align-items: center;
}
.uv-icon--left[data-v-b7a6dd5d] {
  flex-direction: row-reverse;
  align-items: center;
}
.uv-icon--right[data-v-b7a6dd5d] {
  flex-direction: row;
  align-items: center;
}
.uv-icon--top[data-v-b7a6dd5d] {
  flex-direction: column-reverse;
  justify-content: center;
}
.uv-icon--bottom[data-v-b7a6dd5d] {
  flex-direction: column;
  justify-content: center;
}
.uv-icon__icon[data-v-b7a6dd5d] {
  font-family: uvicon-iconfont;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.uv-icon__icon--primary[data-v-b7a6dd5d] {
  color: #3c9cff;
}
.uv-icon__icon--success[data-v-b7a6dd5d] {
  color: #5ac725;
}
.uv-icon__icon--error[data-v-b7a6dd5d] {
  color: #f56c6c;
}
.uv-icon__icon--warning[data-v-b7a6dd5d] {
  color: #f9ae3d;
}
.uv-icon__icon--info[data-v-b7a6dd5d] {
  color: #909399;
}
.uv-icon__img[data-v-b7a6dd5d] {
  height: auto;
  will-change: transform;
}
.uv-icon__label[data-v-b7a6dd5d] {
  line-height: 1;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-popup[data-v-01a3ad6e] {
  position: fixed;
  z-index: 99;
}
.uv-popup.top[data-v-01a3ad6e], .uv-popup.left[data-v-01a3ad6e], .uv-popup.right[data-v-01a3ad6e] {
  top: 0;
}
.uv-popup .uv-popup__content[data-v-01a3ad6e] {
  display: block;
  overflow: hidden;
  position: relative;
}
.uv-popup .uv-popup__content.left[data-v-01a3ad6e], .uv-popup .uv-popup__content.right[data-v-01a3ad6e] {
  padding-top: 0;
  flex: 1;
}
.uv-popup .uv-popup__content__close[data-v-01a3ad6e] {
  position: absolute;
}
.uv-popup .uv-popup__content__close--hover[data-v-01a3ad6e] {
  opacity: 0.4;
}
.uv-popup .uv-popup__content__close--top-left[data-v-01a3ad6e] {
  top: 15px;
  left: 15px;
}
.uv-popup .uv-popup__content__close--top-right[data-v-01a3ad6e] {
  top: 15px;
  right: 15px;
}
.uv-popup .uv-popup__content__close--bottom-left[data-v-01a3ad6e] {
  bottom: 15px;
  left: 15px;
}
.uv-popup .uv-popup__content__close--bottom-right[data-v-01a3ad6e] {
  right: 15px;
  bottom: 15px;
}
.fixforpc-z-index[data-v-01a3ad6e] {
  z-index: 999;
}
.fixforpc-top[data-v-01a3ad6e] {
  top: 0;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-calendar-item__weeks-box[data-v-68116d39] {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
}
.uv-calendar-item__weeks-top-text[data-v-68116d39] {
  height: 1rem;
  line-height: 1rem;
  font-size: 0.75rem;
}
.uv-calendar-item__weeks-box-text[data-v-68116d39] {
  font-size: 14px;
  color: #333;
}
.uv-calendar-item__weeks-lunar-text[data-v-68116d39] {
  height: 1rem;
  line-height: 1rem;
  font-size: 0.75rem;
  color: #333;
}
.uv-calendar-item__weeks-lunar-text--equal[data-v-68116d39] {
  font-size: 0.625rem;
}
.uv-calendar-item__weeks-box-item[data-v-68116d39] {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 3.3125rem;
  height: 56px;
}
.uv-calendar-item__weeks-box-circle[data-v-68116d39] {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 8px;
  height: 8px;
  border-radius: 8px;
  background-color: #f56c6c;
}
.uv-calendar-item--disable[data-v-68116d39] {
  background-color: rgba(249, 249, 249, 0.3);
  color: #c0c0c0;
}
.uv-calendar-item--isDay-text[data-v-68116d39] {
  color: #3c9cff;
}
.uv-calendar-item--isDay[data-v-68116d39] {
  background-color: #3c9cff;
  color: #fff;
}
.uv-calendar-item--checked[data-v-68116d39] {
  background-color: #3c9cff;
  color: #fff;
  border-radius: 4px;
}
.uv-calendar-item--before-checked[data-v-68116d39] {
  color: #fff;
}
.uv-calendar-item--after-checked[data-v-68116d39] {
  color: #fff;
}
.uv-calendar-item--multiple[data-v-68116d39] {
  background-color: #3c9cff;
  color: #fff;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-calendar[data-v-d658b772] {
  display: flex;
  flex-direction: column;
}
.uv-calendar__mask[data-v-d658b772] {
  position: fixed;
  bottom: 0;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.4);
  transition-property: opacity;
  transition-duration: 0.3s;
  opacity: 0;
  z-index: 99;
}
.uv-calendar--mask-show[data-v-d658b772] {
  opacity: 1;
}
.uv-calendar--fixed[data-v-d658b772] {
  position: fixed;
  left: 0;
  right: 0;
  transition-property: transform;
  transition-duration: 0.3s;
  transform: translateY(460px);
  bottom: calc(var(--window-bottom));
  z-index: 99;
}
.uv-calendar--ani-show[data-v-d658b772] {
  transform: translateY(0);
}
.uv-calendar__content[data-v-d658b772] {
  background-color: #fff;
}
.uv-calendar__header[data-v-d658b772] {
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 50px;
  border-bottom-color: #EDEDED;
  border-bottom-style: solid;
  border-bottom-width: 1px;
}
.uv-calendar--fixed-top[data-v-d658b772] {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  border-top-color: #EDEDED;
  border-top-style: solid;
  border-top-width: 1px;
}
.uv-calendar--fixed-width[data-v-d658b772] {
  width: 50px;
}
.uv-calendar__backtoday[data-v-d658b772] {
  position: absolute;
  right: 0;
  top: 0.78125rem;
  padding: 0 5px;
  padding-left: 10px;
  height: 25px;
  line-height: 25px;
  font-size: 12px;
  border-top-left-radius: 25px;
  border-bottom-left-radius: 25px;
  color: #333;
  background-color: #f1f1f1;
}
.uv-calendar__header-text[data-v-d658b772] {
  text-align: center;
  width: 100px;
  font-size: 14px;
  color: #333;
}
.uv-calendar__header-btn-box[data-v-d658b772] {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
}
.uv-calendar__header-btn[data-v-d658b772] {
  width: 10px;
  height: 10px;
  border-left-color: #808080;
  border-left-style: solid;
  border-left-width: 2px;
  border-top-color: #555555;
  border-top-style: solid;
  border-top-width: 2px;
}
.uv-calendar--left[data-v-d658b772] {
  transform: rotate(-45deg);
}
.uv-calendar--right[data-v-d658b772] {
  transform: rotate(135deg);
}
.uv-calendar__weeks[data-v-d658b772] {
  position: relative;
  display: flex;
  flex-direction: row;
}
.uv-calendar__weeks-week[data-v-d658b772] {
  padding: 0 0 0.0625rem;
}
.uv-calendar__weeks-item[data-v-d658b772] {
  flex: 1;
}
.uv-calendar__weeks-day[data-v-d658b772] {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 45px;
  border-bottom-color: #F5F5F5;
  border-bottom-style: solid;
  border-bottom-width: 1px;
}
.uv-calendar__weeks-day-text[data-v-d658b772] {
  font-size: 14px;
}
.uv-calendar__box[data-v-d658b772] {
  position: relative;
}
.uv-calendar__box-bg[data-v-d658b772] {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.uv-calendar__box-bg-text[data-v-d658b772] {
  font-size: 200px;
  font-weight: bold;
  color: #999;
  opacity: 0.1;
  text-align: center;
  line-height: 1;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-calendar__content[data-v-4990eb9c] {
  background-color: #fff;
}
.line[data-v-4990eb9c] {
  width: 23.4375rem;
  height: 1px;
  border-bottom-color: #EDEDED;
  border-bottom-style: solid;
  border-bottom-width: 1px;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
uni-view[data-v-d2bffd23], uni-scroll-view[data-v-d2bffd23], uni-swiper-item[data-v-d2bffd23] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-col[data-v-d2bffd23] {
  padding: 0;
  box-sizing: border-box;
}
.uv-col-0[data-v-d2bffd23] {
  width: 0;
}
.uv-col-1[data-v-d2bffd23] {
  width: 8.3333333333%;
}
.uv-col-2[data-v-d2bffd23] {
  width: 16.6666666667%;
}
.uv-col-3[data-v-d2bffd23] {
  width: 25%;
}
.uv-col-4[data-v-d2bffd23] {
  width: 33.3333333333%;
}
.uv-col-5[data-v-d2bffd23] {
  width: 41.6666666667%;
}
.uv-col-6[data-v-d2bffd23] {
  width: 50%;
}
.uv-col-7[data-v-d2bffd23] {
  width: 58.3333333333%;
}
.uv-col-8[data-v-d2bffd23] {
  width: 66.6666666667%;
}
.uv-col-9[data-v-d2bffd23] {
  width: 75%;
}
.uv-col-10[data-v-d2bffd23] {
  width: 83.3333333333%;
}
.uv-col-11[data-v-d2bffd23] {
  width: 91.6666666667%;
}
.uv-col-12[data-v-d2bffd23] {
  width: 100%;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
uni-view[data-v-692ff899], uni-scroll-view[data-v-692ff899], uni-swiper-item[data-v-692ff899] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-row[data-v-692ff899] {
  display: flex;
  flex-direction: row;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-line[data-v-dcf8cb8f] {
  vertical-align: middle;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
uni-view[data-v-d1e73275], uni-scroll-view[data-v-d1e73275], uni-swiper-item[data-v-d1e73275] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-form-item[data-v-d1e73275] {
  display: flex;
  flex-direction: column;
  font-size: 14px;
  color: #303133;
}
.uv-form-item__body[data-v-d1e73275] {
  display: flex;
  flex-direction: row;
  padding: 10px 0;
}
.uv-form-item__body__left[data-v-d1e73275] {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.uv-form-item__body__left__content[data-v-d1e73275] {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-right: 0.3125rem;
  flex: 1;
}
.uv-form-item__body__left__content__icon[data-v-d1e73275] {
  margin-right: 0.25rem;
}
.uv-form-item__body__left__content__required[data-v-d1e73275] {
  position: absolute;
  left: -9px;
  color: #f56c6c;
  line-height: 20px;
  font-size: 20px;
  top: 3px;
}
.uv-form-item__body__left__content__label[data-v-d1e73275] {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
  color: #303133;
  font-size: 15px;
}
.uv-form-item__body__right[data-v-d1e73275] {
  flex: 1;
}
.uv-form-item__body__right__content[data-v-d1e73275] {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
}
.uv-form-item__body__right__content__slot[data-v-d1e73275] {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.uv-form-item__body__right__content__icon[data-v-d1e73275] {
  margin-left: 0.3125rem;
  color: #c0c4cc;
  font-size: 0.9375rem;
}
.uv-form-item__body__right__message__box[data-v-d1e73275] {
  height: 16px;
  line-height: 16px;
}
.uv-form-item__body__right__message[data-v-d1e73275] {
  margin-top: -6px;
  line-height: 24px;
  font-size: 12px;
  color: #f56c6c;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-border[data-v-651602aa] {
  border-width: 0.5px !important;
  border-color: #dadbde !important;
  border-style: solid;
}
.uv-border-bottom[data-v-651602aa] {
  border-bottom-width: 0.5px !important;
  border-color: #dadbde !important;
  border-bottom-style: solid;
}
uni-view[data-v-651602aa], uni-scroll-view[data-v-651602aa], uni-swiper-item[data-v-651602aa] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-input[data-v-651602aa] {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}
.uv-input--radius[data-v-651602aa], .uv-input--square[data-v-651602aa] {
  border-radius: 4px;
}
.uv-input--no-radius[data-v-651602aa] {
  border-radius: 0;
}
.uv-input--circle[data-v-651602aa] {
  border-radius: 100px;
}
.uv-input__content[data-v-651602aa] {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.uv-input__content__field-wrapper[data-v-651602aa] {
  position: relative;
  display: flex;
  flex-direction: row;
  margin: 0;
  flex: 1;
}
.uv-input__content__field-wrapper__field[data-v-651602aa] {
  line-height: 26px;
  text-align: left;
  color: #303133;
  height: 24px;
  font-size: 15px;
  flex: 1;
}
.uv-input__content__clear[data-v-651602aa] {
  width: 20px;
  height: 20px;
  border-radius: 100px;
  background-color: #c6c7cb;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  transform: scale(0.82);
  margin-left: 15px;
}
.uv-input__content__subfix-icon[data-v-651602aa] {
  margin-left: 4px;
}
.uv-input__content__prefix-icon[data-v-651602aa] {
  margin-right: 4px;
}

.tui-button__wrap[data-v-835d2781] {
		position: relative;
}
.tui-button__hover[data-v-835d2781]:active::after {
		content: '';
		position: absolute;
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
		background-color: rgba(0, 0, 0, 0.15);
		border-radius: 0.1875rem;
		pointer-events: none;
}

	/* button start*/
.tui-btn[data-v-835d2781] {
		width: 100%;
		position: relative;
		border: 0 !important;
		box-sizing: border-box;
		border-radius: 0.1875rem;
		padding-left: 0;
		padding-right: 0;
		overflow: visible;
		display: flex;
		align-items: center;
		justify-content: center;
}
.tui-btn[data-v-835d2781]::after {
		border: 0;
}
.tui-btn__flex-1[data-v-835d2781] {
		flex: 1;
}
.tui-button__border[data-v-835d2781] {
		position: absolute;
		width: 200%;
		height: 200%;
		transform-origin: 0 0;
		transform: scale(0.5, 0.5) translateZ(0);
		box-sizing: border-box;
		left: 0;
		top: 0;
		border-radius: 0.375rem;
		border: 1px solid transparent;
		pointer-events: none;
}
.tui-text-bold[data-v-835d2781] {
		font-weight: bold;
}
.tui-dark-disabled[data-v-835d2781] {
		opacity: 0.6 !important;
		color: #fafbfc !important;
}
.tui-dark-disabled-outline[data-v-835d2781] {
		opacity: 0.5 !important;
}
.tui-gray-disabled[data-v-835d2781] {
		background: #f3f3f3 !important;
		color: #919191 !important;
		box-shadow: none;
}

	/*圆角 */
.tui-fillet[data-v-835d2781] {
		border-radius: 6.875rem !important;
}
.tui-fillet[data-v-835d2781]::after {
		border-radius: 6.875rem !important;
}
.tui-outline-fillet[data-v-835d2781] {
		border-radius: 6.875rem !important;
}
.tui-outline-fillet[data-v-835d2781]::after {
		border-radius: 6.875rem !important;
}

	/*平角*/
.tui-rightAngle[data-v-835d2781] {
		border-radius: 0 !important;
}
.tui-rightAngle[data-v-835d2781]::after {
		border-radius: 0 !important;
}
.tui-outline-rightAngle[data-v-835d2781] {
		border-radius: 0 !important;
}
.tui-outline-rightAngle[data-v-835d2781]::after {
		border-radius: 0 !important;
}
.tui-btn__link[data-v-835d2781]::after {
		border: 0 !important;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.container[data-v-90785673] {
  margin: 0.46875rem;
  border-radius: 0.3125rem;
  padding: 0.625rem;
  background-color: white;
}
.container .close[data-v-90785673] {
  display: flex;
  justify-content: flex-end;
}
.container .uni-input[data-v-90785673] {
  height: 1.5625rem;
  padding: 0.46875rem 0.78125rem;
  line-height: 1.5625rem;
  font-size: 0.875rem;
  background: #FFF;
  flex: 1;
}
.container .input-value-border[data-v-90785673] {
  border: 1px solid #EDEDED;
  border-radius: 0.15625rem;
}
.container .btn-sub[data-v-90785673] {
  background-color: #2979ff;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.add-schedule[data-v-70aafa2d] {
  cursor: pointer;
  margin: 0.78125rem;
  color: #5677fc;
  text-align: right;
  font-size: 0.9375rem;
}
.item[data-v-70aafa2d] {
  padding: 0.78125rem;
}
.item .item-left[data-v-70aafa2d] {
  font-size: 0.8125rem;
}
.item .item-right[data-v-70aafa2d] {
  color: #999;
  font-size: 0.8125rem;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.item[data-v-96efdf43] {
  background-color: #ffffff;
  margin: 0.46875rem 0;
  padding: 0.3125rem 0;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */