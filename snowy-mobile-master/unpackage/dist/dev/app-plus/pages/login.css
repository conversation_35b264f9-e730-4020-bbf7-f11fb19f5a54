/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
uni-view[data-v-b7a6dd5d], uni-scroll-view[data-v-b7a6dd5d], uni-swiper-item[data-v-b7a6dd5d] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
@font-face {
  font-family: "uvicon-iconfont";
  src: url("../assets/uvicons.04d281cc.ttf") format("truetype");
}
.uv-icon[data-v-b7a6dd5d] {
  display: flex;
  align-items: center;
}
.uv-icon--left[data-v-b7a6dd5d] {
  flex-direction: row-reverse;
  align-items: center;
}
.uv-icon--right[data-v-b7a6dd5d] {
  flex-direction: row;
  align-items: center;
}
.uv-icon--top[data-v-b7a6dd5d] {
  flex-direction: column-reverse;
  justify-content: center;
}
.uv-icon--bottom[data-v-b7a6dd5d] {
  flex-direction: column;
  justify-content: center;
}
.uv-icon__icon[data-v-b7a6dd5d] {
  font-family: uvicon-iconfont;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.uv-icon__icon--primary[data-v-b7a6dd5d] {
  color: #3c9cff;
}
.uv-icon__icon--success[data-v-b7a6dd5d] {
  color: #5ac725;
}
.uv-icon__icon--error[data-v-b7a6dd5d] {
  color: #f56c6c;
}
.uv-icon__icon--warning[data-v-b7a6dd5d] {
  color: #f9ae3d;
}
.uv-icon__icon--info[data-v-b7a6dd5d] {
  color: #909399;
}
.uv-icon__img[data-v-b7a6dd5d] {
  height: auto;
  will-change: transform;
}
.uv-icon__label[data-v-b7a6dd5d] {
  line-height: 1;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
body[data-v-e8ce220f] {
  background-color: #ffffff;
}
.login-container[data-v-e8ce220f] {
  width: 100%;
}
.login-container .logo-content[data-v-e8ce220f] {
  width: 100%;
  font-size: 21px;
  text-align: center;
  padding-top: 15%;
  align-items: center;
  justify-content: center;
  display: flex;
}
.login-container .logo-content uni-image[data-v-e8ce220f] {
  border-radius: 4px;
  width: 3.125rem;
  height: 3.125rem;
}
.login-container .logo-content .title[data-v-e8ce220f] {
  margin-left: 0.46875rem;
}
.login-container .login-form-content[data-v-e8ce220f] {
  text-align: center;
  margin: 20px auto;
  margin-top: 15%;
  width: 80%;
}
.login-container .login-form-content .input-item[data-v-e8ce220f] {
  margin: 20px auto;
  background-color: #f5f6f7;
  height: 45px;
  border-radius: 20px;
  display: flex;
  align-items: center;
}
.login-container .login-form-content .input-item .icon[data-v-e8ce220f] {
  font-size: 1.1875rem;
  margin-left: 10px;
  color: #999;
}
.login-container .login-form-content .input-item .input[data-v-e8ce220f] {
  width: 100%;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  padding-left: 15px;
}
.login-container .login-form-content .input-item .login-code-img[data-v-e8ce220f] {
  border: 1px solid var(--border-color-split);
  cursor: pointer;
  width: 70%;
  height: 45px;
}
.login-container .login-form-content .login-btn[data-v-e8ce220f] {
  margin-top: 40px;
  height: 45px;
  background-color: #2979ff;
  border-radius: 31.25rem;
  color: #ffffff;
}
.login-code-img[data-v-e8ce220f] {
  border: 1px solid var(--border-color-split);
  cursor: pointer;
  width: 70%;
  height: 45px;
}