import{r as e,aq as s,ay as a,o as t,b as l,w as r,h as u,i as o,e as i,az as n}from"./index-58695647.js";const c={__name:"index",setup(c){const v=e({}),w=e({progress:{color:"#FF3333"}});return s((e=>{v.value=e,e.title&&a({title:e.title})})),(e,s)=>{const a=n,c=o;return v.value.url?(t(),l(c,{key:0},{default:r((()=>[i(a,{"webview-styles":w.value,src:`${v.value.url}`},null,8,["webview-styles","src"])])),_:1})):u("",!0)}}};export{c as default};
