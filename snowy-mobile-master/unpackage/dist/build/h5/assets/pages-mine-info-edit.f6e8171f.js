import{a as e,_ as a,b as l}from"./uv-form.bce81edd.js";import{r as o,s as u,a as r,o as t,b as n,w as d,i as m,e as s,a7 as p,a9 as i,u as v,a8 as f,f as c,aM as _,l as g,m as h}from"./index-58695647.js";import{_ as b,a as V}from"./uv-radio-group.57606234.js";import{_ as y}from"./snowy-calendar.226962e5.js";import{_ as j}from"./tui-button.600cfc04.js";import{_ as x}from"./_plugin-vue_export-helper.1b428a4d.js";import"./uv-icon.019f93fe.js";import"./uv-transition.8392ab8b.js";import"./uv-calendars.cf386575.js";import"./uv-popup.f10e3fa8.js";const k=x({__name:"edit",setup(x){const k=o(),w=o(uni.$xeu.clone(u.getters.userInfo,!0)),U=r({name:[{type:"string",required:!0,message:"请输入姓名",trigger:["blur","change"]}],phone:[{pattern:/^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,message:"请填写符合要求的11位手机号",trigger:["blur","change"]}],email:[{pattern:/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,message:"请填写正确的邮箱号",trigger:["blur","change"]}]}),z=uni.$snowy.tool.dictList("GENDER"),E=()=>{k.value.validate().then((e=>{_(w.value).then((e=>{u.commit("SET_userInfo",w.value)}))}))};return(o,u)=>{const r=g(h("uv-input"),e),_=g(h("uv-form-item"),a),x=g(h("uv-radio"),b),S=g(h("uv-radio-group"),V),$=g(h("snowy-calendar"),y),q=g(h("uv-form"),l),A=g(h("tui-button"),j),I=m;return t(),n(I,{class:"edit-container snowy-shadow"},{default:d((()=>[s(q,{ref_key:"formRef",ref:k,model:w.value,rules:U,"label-position":"top",labelWidth:"auto",labelStyle:{marginBottom:"25rpx",fontSize:"27rpx",color:"#606266"}},{default:d((()=>[s(_,{label:"姓名",prop:"name",required:""},{default:d((()=>[s(r,{modelValue:w.value.name,"onUpdate:modelValue":u[0]||(u[0]=e=>w.value.name=e),placeholder:"请输入姓名"},null,8,["modelValue"])])),_:1}),s(_,{label:"手机",prop:"phone"},{default:d((()=>[s(r,{modelValue:w.value.phone,"onUpdate:modelValue":u[1]||(u[1]=e=>w.value.phone=e),placeholder:"请输入手机"},null,8,["modelValue"])])),_:1}),s(_,{label:"昵称",prop:"nickname"},{default:d((()=>[s(r,{modelValue:w.value.nickname,"onUpdate:modelValue":u[2]||(u[2]=e=>w.value.nickname=e),placeholder:"请输入昵称"},null,8,["modelValue"])])),_:1}),s(_,{label:"性别",prop:"gender"},{default:d((()=>[s(S,{modelValue:w.value.gender,"onUpdate:modelValue":u[3]||(u[3]=e=>w.value.gender=e)},{default:d((()=>[(t(!0),p(f,null,i(v(z),((e,a)=>(t(),n(x,{customStyle:{marginRight:"50rpx"},key:a,label:e.text,name:e.value},null,8,["label","name"])))),128))])),_:1},8,["modelValue"])])),_:1}),s(_,{label:"生日",prop:"birthday"},{default:d((()=>[s($,{modelValue:w.value.birthday,"onUpdate:modelValue":u[4]||(u[4]=e=>w.value.birthday=e),placeholder:"请选择出生日期"},null,8,["modelValue"])])),_:1}),s(_,{label:"邮箱",prop:"email"},{default:d((()=>[s(r,{modelValue:w.value.email,"onUpdate:modelValue":u[5]||(u[5]=e=>w.value.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"]),s(A,{margin:"50rpx 0",preventClick:!0,shadow:!0,onClick:E},{default:d((()=>[c("提交")])),_:1})])),_:1})}}},[["__scopeId","data-v-301f7696"]]);export{k as default};
