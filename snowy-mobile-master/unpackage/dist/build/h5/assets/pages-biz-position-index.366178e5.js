import{r as e,o as a,b as l,w as o,e as s,f as t,h as i,P as n,i as r,l as u,m as d,a as c,aj as m,aB as f,ak as p,al as v,a7 as h,a8 as _,an as y,a9 as k,a1 as w,a2 as g,ae as b,t as F,k as x,j as C}from"./index-58695647.js";import{_ as j,a as z}from"./tui-list-view.2e7f2ee0.js";import{_ as P}from"./snowy-icon.e7a99593.js";import{_ as O}from"./snowy-empty.8508b7b3.js";import{_ as $}from"./snowy-float-btn.c0b619f0.js";import{_ as I,a as A}from"./department.42e35462.js";import{o as B}from"./bizOrgApi.fca39c8b.js";import{p as E,a as D}from"./bizPositionApi.11993909.js";import{_ as H}from"./uv-popup.f10e3fa8.js";import{_ as R}from"./_plugin-vue_export-helper.1b428a4d.js";import"./uv-icon.019f93fe.js";import"./uv-transition.8392ab8b.js";const T=R({__name:"more",emits:["handleOk"],setup(c,{expose:m,emit:f}){const p=e(),v=e({}),h=()=>{n({url:"/pages/biz/position/form?id="+v.value.id}),p.value.close()},_=()=>{uni.$snowy.modal.confirm(`是否确认删除【${v.value.name}】职位？`).then((()=>{E([{id:v.value.id}]).then((e=>{f("handleOk"),p.value.close()}))}))},y=()=>{p.value.close()};return m({open:e=>{v.value=e,p.value.open()}}),(e,n)=>{const c=r,m=u(d("tui-list-cell"),j),f=u(d("tui-list-view"),z),v=u(d("uv-popup"),H);return a(),l(v,{ref_key:"popRef",ref:p,mode:"bottom","bg-color":"null","z-index":"99"},{default:o((()=>[s(c,{class:"container"},{default:o((()=>[s(f,{unlined:"all","background-color":"transparent"},{default:o((()=>[e.$snowy.hasPerm("mobileBizPositionEdit")?(a(),l(m,{key:0,hover:!0,arrow:!1,onClick:h,radius:10},{default:o((()=>[s(c,{class:"item"},{default:o((()=>[t(" 编辑 ")])),_:1})])),_:1})):i("",!0),e.$snowy.hasPerm("mobileBizPositionDelete")?(a(),l(m,{key:1,hover:!0,arrow:!1,onClick:_,radius:10,"margin-top":2},{default:o((()=>[s(c,{class:"item"},{default:o((()=>[t(" 刪除 ")])),_:1})])),_:1})):i("",!0),s(m,{hover:!0,arrow:!1,onClick:y,"margin-top":10,radius:10},{default:o((()=>[s(c,{class:"item"},{default:o((()=>[t(" 取消 ")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},512)}}},[["__scopeId","data-v-eaf3a4c4"]]),G=R({__name:"index",setup(E){const H=e([]),R=e([]);B().then((e=>{R.value=(null==e?void 0:e.data)||[],H.value.push({id:"0",name:"全部",children:(null==e?void 0:e.data)||[]})}));const G=c({}),M=c({current:1,size:10}),N=e([]),Y=e=>{e&&(M.current=1,N.value=[]),Object.assign(M,G),D(M).then((e=>{var a;uni.$xeu.isEmpty(null==(a=null==e?void 0:e.data)?void 0:a.records)||(N.value=N.value.concat(e.data.records),M.current++)})).finally((()=>{y()}))};Y(!0);m((()=>{f("formBack",(e=>{Y(!0)}))})),p((()=>{Y(!0)})),v((()=>{Y()}));const L=()=>{n({url:"/pages/biz/position/form"})};return(e,n)=>{const c=x,m=r,f=C,p=u(d("tui-list-cell"),j),v=u(d("tui-list-view"),z),y=u(d("snowy-icon"),P),B=u(d("snowy-empty"),O),E=u(d("snowy-float-btn"),$);return a(),h(_,null,[s(m,{class:"crumb snowy-shadow"},{default:o((()=>[(a(!0),h(_,null,k(H.value,((e,s)=>(a(),l(c,{class:b(["crumb-text",s===H.value.length-1?"uni-secondary-color":"uni-primary"]),key:s,onClick:a=>((e,a)=>{R.value=e.children,H.value.splice(a+1,H.value.length-(a+1)),G.orgId="0"===e.id?"":e.id,Y(!0)})(e,s)},{default:o((()=>[t(F(e.name+(s===H.value.length-1?"":" | ")),1)])),_:2},1032,["class","onClick"])))),128))])),_:1}),s(m,{class:"org-list snowy-shadow"},{default:o((()=>[s(v,{unlined:"all"},{default:o((()=>[(a(!0),h(_,null,k(R.value,((e,n)=>(a(),l(p,{key:n,"line-left":0,hover:!!e.children,arrow:!!e.children,onClick:a=>((e,a)=>{R.value=e.children,H.value.push(e),G.orgId=e.id,Y(!0)})(e)},{default:o((()=>[s(m,{class:"item"},{default:o((()=>["COMPANY"===e.category?(a(),l(f,{key:0,class:"item-img",src:I,mode:"widthFix"})):i("",!0),"DEPT"===e.category?(a(),l(f,{key:1,class:"item-img",src:A,mode:"widthFix"})):i("",!0),s(m,{class:"item-left"},{default:o((()=>[t(F(e.name),1)])),_:2},1024),s(m,{class:"item-right"})])),_:2},1024)])),_:2},1032,["hover","arrow","onClick"])))),128))])),_:1})])),_:1}),s(m,{class:"biz-list snowy-shadow"},{default:o((()=>[s(v,{unlined:"all"},{default:o((()=>[(a(!0),h(_,null,k(N.value,((i,n)=>(a(),l(p,{key:n,"line-left":0,hover:!0,arrow:!1,onClick:a=>e.$refs.moreRef.open(i)},{default:o((()=>[s(m,{class:"item"},{default:o((()=>["HIGH"==i.category?(a(),l(m,{key:0,style:{width:"42px",height:"42px"}},{default:o((()=>[s(y,{backgroundColor:"#f3a73f",name:"integral",size:"20",color:"#FFFFFF"})])),_:1})):"MIDDLE"==i.category?(a(),l(m,{key:1,style:{width:"42px",height:"42px"}},{default:o((()=>[s(y,{backgroundColor:"#2979ff",name:"pushpin",size:"20",color:"#FFFFFF"})])),_:1})):(a(),l(m,{key:2,style:{width:"42px",height:"42px"}},{default:o((()=>[s(y,{backgroundColor:"#18bc37",name:"account",size:"20",color:"#FFFFFF"})])),_:1})),s(m,{class:"item-left"},{default:o((()=>[t(F(i.name),1)])),_:2},1024),s(m,{class:"item-right"},{default:o((()=>[t(F(e.$snowy.tool.dictTypeData("POSITION_CATEGORY",i.category)),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1}),w(s(B,null,null,512),[[g,e.$xeu.isEmpty(N.value)]])])),_:1}),e.$snowy.hasPerm("mobileBizPositionAdd")?(a(),l(E,{key:0,onClick:L})):i("",!0),s(T,{ref:"moreRef",onHandleOk:n[0]||(n[0]=e=>Y(!0))},null,512)],64)}}},[["__scopeId","data-v-b8074347"]]);export{G as default};
