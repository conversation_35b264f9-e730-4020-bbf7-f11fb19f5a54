var e,t,a,i;import{_ as l}from"./uv-icon.019f93fe.js";import{ab as o,ac as r,l as s,m as n,o as d,b as c,w as h,e as u,ae as p,ap as b,q as m,v as f,f as v,t as C,i as S,k as D}from"./index-58695647.js";import{_ as g}from"./_plugin-vue_export-helper.1b428a4d.js";const y=g({name:"uv-radio",mixins:[o,r,{props:{name:{type:[String,Number,Boolean],default:""},shape:{type:String,default:""},disabled:{type:[String,Boolean],default:""},labelDisabled:{type:[String,Boolean],default:""},activeColor:{type:String,default:""},inactiveColor:{type:String,default:""},iconSize:{type:[String,Number],default:""},labelSize:{type:[String,Number],default:""},label:{type:[String,Number,Boolean],default:""},size:{type:[String,Number],default:""},iconColor:{type:String,default:""},labelColor:{type:String,default:""},...null==(t=null==(e=uni.$uv)?void 0:e.props)?void 0:t.radio}}],data:()=>({checked:!1,parentData:{iconSize:12,labelSize:14,labelDisabled:null,disabled:null,shape:null,activeColor:null,inactiveColor:null,size:18,value:null,modelValue:null,iconColor:null,placement:"row",borderBottom:!1,iconPlacement:"left"}}),computed:{elDisabled(){return""!==this.disabled?this.disabled:null!==this.parentData.disabled&&this.parentData.disabled},elLabelDisabled(){return""!==this.labelDisabled?this.labelDisabled:null!==this.parentData.labelDisabled&&this.parentData.labelDisabled},elSize(){return this.size?this.size:this.parentData.size?this.parentData.size:21},elIconSize(){return this.iconSize?this.iconSize:this.parentData.iconSize?this.parentData.iconSize:12},elActiveColor(){return this.activeColor?this.activeColor:this.parentData.activeColor?this.parentData.activeColor:"#2979ff"},elInactiveColor(){return this.inactiveColor?this.inactiveColor:this.parentData.inactiveColor?this.parentData.inactiveColor:"#c8c9cc"},elLabelColor(){return this.labelColor?this.labelColor:this.parentData.labelColor?this.parentData.labelColor:"#606266"},elShape(){return this.shape?this.shape:this.parentData.shape?this.parentData.shape:"circle"},elLabelSize(){return this.$uv.addUnit(this.labelSize?this.labelSize:this.parentData.labelSize?this.parentData.labelSize:"15")},elIconColor(){const e=this.iconColor?this.iconColor:this.parentData.iconColor?this.parentData.iconColor:"#ffffff";return this.elDisabled?this.checked?this.elInactiveColor:"transparent":this.checked?e:"transparent"},iconClasses(){let e=[];return e.push("uv-radio__icon-wrap--"+this.elShape),this.elDisabled&&e.push("uv-radio__icon-wrap--disabled"),this.checked&&this.elDisabled&&e.push("uv-radio__icon-wrap--disabled--checked"),e},iconWrapStyle(){const e={};return e.backgroundColor=this.checked&&!this.elDisabled?this.elActiveColor:"#ffffff",e.borderColor=this.checked&&!this.elDisabled?this.elActiveColor:this.elInactiveColor,e.width=this.$uv.addUnit(this.elSize),e.height=this.$uv.addUnit(this.elSize),"right"===this.parentData.iconPlacement&&(e.marginRight=0),e},radioStyle(){const e={};return this.parentData.borderBottom&&"row"===this.parentData.placement&&error("检测到您将borderBottom设置为true，需要同时将uv-radio-group的placement设置为column才有效"),this.parentData.borderBottom&&"column"===this.parentData.placement&&(e.paddingBottom="ios"===this.$uv.os()?"12px":"8px"),this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))}},mounted(){this.init()},methods:{init(){this.updateParentData(),this.parent||error("uv-radio必须搭配uv-radio-group组件使用"),this.$nextTick((()=>{let e=null;e=this.parentData.modelValue,this.checked=this.name===e}))},updateParentData(){this.getParentData("uv-radio-group")},iconClickHandler(e){this.preventEvent(e),this.elDisabled||this.setRadioCheckedStatus()},wrapperClickHandler(e){"right"===this.parentData.iconPlacement&&this.iconClickHandler(e)},labelClickHandler(e){this.preventEvent(e),this.elLabelDisabled||this.elDisabled||this.setRadioCheckedStatus()},emitEvent(){this.checked||(this.$emit("change",this.name),this.$nextTick((()=>{this.$uv.formValidate(this,"change")})))},setRadioCheckedStatus(){this.emitEvent(),this.checked=!0,"function"==typeof this.parent.unCheckedOther&&this.parent.unCheckedOther(this)}}},[["render",function(e,t,a,i,o,r){const g=s(n("uv-icon"),l),y=S,k=D;return d(),c(y,{class:p(["uv-radio",[`uv-radio-label--${o.parentData.iconPlacement}`,o.parentData.borderBottom&&"column"===o.parentData.placement&&"uv-border-bottom"]]),onClick:b(r.wrapperClickHandler,["stop"]),style:m([r.radioStyle])},{default:h((()=>[u(y,{class:p(["uv-radio__icon-wrap",r.iconClasses]),onClick:b(r.iconClickHandler,["stop"]),style:m([r.iconWrapStyle])},{default:h((()=>[f(e.$slots,"icon",{},(()=>[u(g,{class:"uv-radio__icon-wrap__icon",name:"checkbox-mark",size:r.elIconSize,color:r.elIconColor},null,8,["size","color"])]),!0)])),_:3},8,["onClick","class","style"]),u(y,{class:"uv-radio__label-wrap",onClick:b(r.labelClickHandler,["stop"])},{default:h((()=>[f(e.$slots,"default",{},(()=>[u(k,{style:m({color:r.elDisabled?r.elInactiveColor:r.elLabelColor,fontSize:r.elLabelSize,lineHeight:r.elLabelSize})},{default:h((()=>[v(C(e.label),1)])),_:1},8,["style"])]),!0)])),_:3},8,["onClick"])])),_:3},8,["onClick","style","class"])}],["__scopeId","data-v-4ca27a99"]]);const k=g({name:"uv-radio-group",mixins:[o,r,{props:{value:{type:[String,Number,Boolean],default:""},modelValue:{type:[String,Number,Boolean],default:""},disabled:{type:Boolean,default:!1},shape:{type:String,default:"circle"},activeColor:{type:String,default:"#2979ff"},inactiveColor:{type:String,default:"#c8c9cc"},name:{type:String,default:""},size:{type:[String,Number],default:18},placement:{type:String,default:"row"},label:{type:[String],default:""},labelColor:{type:[String],default:"#303133"},labelSize:{type:[String,Number],default:14},labelDisabled:{type:Boolean,default:!1},iconColor:{type:String,default:"#fff"},iconSize:{type:[String,Number],default:12},borderBottom:{type:Boolean,default:!1},iconPlacement:{type:String,default:"left"},...null==(i=null==(a=uni.$uv)?void 0:a.props)?void 0:i.radioGroup}}],computed:{parentData(){return[this.value||this.modelValue,this.disabled,this.inactiveColor,this.activeColor,this.size,this.labelDisabled,this.shape,this.iconSize,this.borderBottom,this.placement]},bemClass(){return this.bem("radio-group",["placement"])}},watch:{parentData(){this.children.length&&this.children.map((e=>{"function"==typeof e.init&&e.init()}))}},data:()=>({}),created(){this.children=[]},methods:{unCheckedOther(e){this.children.map((t=>{e!==t&&(t.checked=!1)}));const{name:t}=e;this.$emit("update:modelValue",t),this.$emit("change",t)}}},[["render",function(e,t,a,i,l,o){const r=S;return d(),c(r,{class:p(["uv-radio-group",o.bemClass]),style:m([e.$uv.addStyle(this.customStyle)])},{default:h((()=>[f(e.$slots,"default",{},void 0,!0)])),_:3},8,["class","style"])}],["__scopeId","data-v-fe601beb"]]);export{y as _,k as a};
