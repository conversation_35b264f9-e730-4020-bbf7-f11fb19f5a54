import{r as e,a,aq as l,o as r,b as o,w as t,i as n,e as u,f as s,s as m,ax as d,l as i,m as p}from"./index-58695647.js";import{a as v,_ as b,b as c}from"./uv-form.bce81edd.js";import{_ as f}from"./tui-button.600cfc04.js";import{_ as g}from"./_plugin-vue_export-helper.1b428a4d.js";import"./uv-icon.019f93fe.js";import"./uv-transition.8392ab8b.js";const _=g({__name:"form",setup(g){const _=e(),U=e({key:"",name:"",baseUrl:"",tenantDomain:""}),y=a({key:[{type:"string",required:!0,message:"请输入环境key",trigger:["blur","change"]}],name:[{type:"string",required:!0,message:"请输入环境名称",trigger:["blur","change"]}],baseUrl:[{type:"string",required:!0,message:"请输入baseUrl",trigger:["blur","change"]}]});l((e=>{e.record&&(U.value=JSON.parse(decodeURIComponent(e.record)))}));const h=()=>{_.value.validate().then((e=>{let a=uni.$xeu.clone(m.getters.allEnv,!0);a[U.value.key]={name:U.value.name,baseUrl:U.value.baseUrl,tenantDomain:U.value.tenantDomain},m.commit("SET_allEnv",a),d({delta:1})}))};return(e,a)=>{const l=i(p("uv-input"),v),m=i(p("uv-form-item"),b),d=i(p("uv-form"),c),g=i(p("tui-button"),f),V=n;return r(),o(V,{class:"container snowy-shadow"},{default:t((()=>[u(d,{ref_key:"formRef",ref:_,model:U.value,rules:y,"label-position":"top",labelWidth:"auto",labelStyle:{marginBottom:"25rpx",fontSize:"27rpx",color:"#606266"}},{default:t((()=>[u(m,{label:"环境KEY",prop:"key",required:""},{default:t((()=>[u(l,{modelValue:U.value.key,"onUpdate:modelValue":a[0]||(a[0]=e=>U.value.key=e),placeholder:"请输入环境key"},null,8,["modelValue"])])),_:1}),u(m,{label:"环境名称",prop:"name",required:""},{default:t((()=>[u(l,{modelValue:U.value.name,"onUpdate:modelValue":a[1]||(a[1]=e=>U.value.name=e),placeholder:"请输入环境名称"},null,8,["modelValue"])])),_:1}),u(m,{label:"baseUrl",prop:"baseUrl",required:""},{default:t((()=>[u(l,{modelValue:U.value.baseUrl,"onUpdate:modelValue":a[2]||(a[2]=e=>U.value.baseUrl=e),placeholder:"请输入baseUrl"},null,8,["modelValue"])])),_:1}),u(m,{label:"tenantDomain",prop:"tenantDomain",required:""},{default:t((()=>[u(l,{modelValue:U.value.tenantDomain,"onUpdate:modelValue":a[3]||(a[3]=e=>U.value.tenantDomain=e),placeholder:"请输入tenantDomain"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"]),u(g,{margin:"50rpx 0",preventClick:!0,shadow:!0,onClick:h},{default:t((()=>[s("提交")])),_:1})])),_:1})}}},[["__scopeId","data-v-e48ce3a5"]]);export{_ as default};
