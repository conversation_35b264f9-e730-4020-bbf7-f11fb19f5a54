!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const e={},t=function(t,n,o){if(!n||0===n.length)return t();const r=document.getElementsByTagName("link");return Promise.all(n.map((t=>{if((t=function(e){return"/"+e}(t))in e)return;e[t]=!0;const n=t.endsWith(".css"),i=n?'[rel="stylesheet"]':"";if(!!o)for(let e=r.length-1;e>=0;e--){const o=r[e];if(o.href===t&&(!n||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${t}"]${i}`))return;const s=document.createElement("link");return s.rel=n?"stylesheet":"modulepreload",n||(s.as="script",s.crossOrigin=""),s.href=t,document.head.appendChild(s),n?new Promise(((e,n)=>{s.addEventListener("load",e),s.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${t}`))))})):void 0}))).then((()=>t()))};function n(e,t){const n=Object.create(null),o=e.split(",");for(let r=0;r<o.length;r++)n[o[r]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}function o(e){if(C(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],i=I(r)?a(r):o(r);if(i)for(const e in i)t[e]=i[e]}return t}return I(e)||D(e)?e:void 0}const r=/;(?![^(]*\))/g,i=/:([^]+)/,s=/\/\*.*?\*\//gs;function a(e){const t={};return e.replace(s,"").split(r).forEach((e=>{if(e){const n=e.split(i);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function l(e){let t="";if(I(e))t=e;else if(C(e))for(let n=0;n<e.length;n++){const o=l(e[n]);o&&(t+=o+" ")}else if(D(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function c(e){if(!e)return null;let{class:t,style:n}=e;return t&&!I(t)&&(e.class=l(t)),n&&(e.style=o(n)),e}const u=n("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function f(e){return!!e||""===e}function d(e,t){if(e===t)return!0;let n=A(e),o=A(t);if(n||o)return!(!n||!o)&&e.getTime()===t.getTime();if(n=$(e),o=$(t),n||o)return e===t;if(n=C(e),o=C(t),n||o)return!(!n||!o)&&function(e,t){if(e.length!==t.length)return!1;let n=!0;for(let o=0;n&&o<e.length;o++)n=d(e[o],t[o]);return n}(e,t);if(n=D(e),o=D(t),n||o){if(!n||!o)return!1;if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e){const o=e.hasOwnProperty(n),r=t.hasOwnProperty(n);if(o&&!r||!o&&r||!d(e[n],t[n]))return!1}}return String(e)===String(t)}function h(e,t){return e.findIndex((e=>d(e,t)))}const p=e=>I(e)?e:null==e?"":C(e)||D(e)&&(e.toString===B||!P(e.toString))?JSON.stringify(e,g,2):String(e),g=(e,t)=>t&&t.__v_isRef?g(e,t.value):O(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n])=>(e[`${t} =>`]=n,e)),{})}:M(t)?{[`Set(${t.size})`]:[...t.values()]}:!D(t)||C(t)||R(t)?t:String(t),m={},v=[],y=()=>{},b=()=>!1,_=/^on[^a-z]/,w=e=>_.test(e),x=e=>e.startsWith("onUpdate:"),T=Object.assign,S=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},E=Object.prototype.hasOwnProperty,k=(e,t)=>E.call(e,t),C=Array.isArray,O=e=>"[object Map]"===F(e),M=e=>"[object Set]"===F(e),A=e=>"[object Date]"===F(e),P=e=>"function"==typeof e,I=e=>"string"==typeof e,$=e=>"symbol"==typeof e,D=e=>null!==e&&"object"==typeof e,L=e=>D(e)&&P(e.then)&&P(e.catch),B=Object.prototype.toString,F=e=>B.call(e),R=e=>"[object Object]"===F(e),N=e=>I(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,j=n(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),q=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},z=/-(\w)/g,H=q((e=>e.replace(z,((e,t)=>t?t.toUpperCase():"")))),V=/\B([A-Z])/g,W=q((e=>e.replace(V,"-$1").toLowerCase())),U=q((e=>e.charAt(0).toUpperCase()+e.slice(1))),Y=q((e=>e?`on${U(e)}`:"")),X=(e,t)=>!Object.is(e,t),K=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},G=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},Z=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let J;const Q=["ad","ad-content-page","ad-draw","audio","button","camera","canvas","checkbox","checkbox-group","cover-image","cover-view","editor","form","functional-page-navigator","icon","image","input","label","live-player","live-pusher","map","movable-area","movable-view","navigator","official-account","open-data","picker","picker-view","picker-view-column","progress","radio","radio-group","rich-text","scroll-view","slider","swiper","swiper-item","switch","text","textarea","video","view","web-view"].map((e=>"uni-"+e));const ee=["%","%"],te=/^([a-z-]+:)?\/\//i,ne=/^data:.*,.*/;function oe(e){return e&&(e.appContext?e.proxy:e)}function re(e){if(!e)return;let t=e.type.name;for(;t&&(n=W(t),-1!==Q.indexOf("uni-"+n.replace("v-uni-","")));)t=(e=e.parent).type.name;var n;return e.proxy}function ie(e){return 1===e.nodeType}function se(e){return 0===e.indexOf("/")}function ae(e){return se(e)?e:"/"+e}function le(e,t){for(const n in t)e.style[n]=t[n]}function ce(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}const ue=e=>e>9?e:"0"+e;function fe({date:e=new Date,mode:t="date"}){return"time"===t?ue(e.getHours())+":"+ue(e.getMinutes()):e.getFullYear()+"-"+ue(e.getMonth()+1)+"-"+ue(e.getDate())}function de(e){return H(e.substring(5))}const he=ce((()=>{const e=HTMLElement.prototype,t=e.setAttribute;e.setAttribute=function(e,n){if(e.startsWith("data-")&&this.tagName.startsWith("UNI-")){(this.__uniDataset||(this.__uniDataset={}))[de(e)]=n}t.call(this,e,n)};const n=e.removeAttribute;e.removeAttribute=function(e){this.__uniDataset&&e.startsWith("data-")&&this.tagName.startsWith("UNI-")&&delete this.__uniDataset[de(e)],n.call(this,e)}}));function pe(e){return T({},e.dataset,e.__uniDataset)}const ge=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function me(e){return{passive:e}}function ve(e){const{id:t,offsetTop:n,offsetLeft:o}=e;return{id:t,dataset:pe(e),offsetTop:n,offsetLeft:o}}function ye(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function be(e={}){const t={};return Object.keys(e).forEach((n=>{try{t[n]=ye(e[n])}catch(o){t[n]=e[n]}})),t}const _e=/\+/g;function we(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(_e," ");let r=e.indexOf("="),i=ye(r<0?e:e.slice(0,r)),s=r<0?null:ye(e.slice(r+1));if(i in t){let e=t[i];C(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function xe(e,t,{clearTimeout:n,setTimeout:o}){let r;const i=function(){n(r);const i=()=>e.apply(this,arguments);r=o(i,t)};return i.cancel=function(){n(r)},i}class Te{constructor(e,t){this.id=e,this.listener={},this.emitCache=[],t&&Object.keys(t).forEach((e=>{this.on(e,t[e])}))}emit(e,...t){const n=this.listener[e];if(!n)return this.emitCache.push({eventName:e,args:t});n.forEach((e=>{e.fn.apply(e.fn,t)})),this.listener[e]=n.filter((e=>"once"!==e.type))}on(e,t){this._addListener(e,"on",t),this._clearCache(e)}once(e,t){this._addListener(e,"once",t),this._clearCache(e)}off(e,t){const n=this.listener[e];if(n)if(t)for(let o=0;o<n.length;)n[o].fn===t&&(n.splice(o,1),o--),o++;else delete this.listener[e]}_clearCache(e){for(let t=0;t<this.emitCache.length;t++){const n=this.emitCache[t],o=e?n.eventName===e?e:null:n.eventName;if(!o)continue;"number"!=typeof this.emit.apply(this,[o,...n.args])?(this.emitCache.splice(t,1),t--):this.emitCache.pop()}}_addListener(e,t,n){(this.listener[e]||(this.listener[e]=[])).push({fn:n,type:t})}}const Se=["onInit","onLoad","onShow","onHide","onUnload","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onShareAppMessage","onAddToFavorites","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"],Ee=["onLoad","onShow"];const ke=["onShow","onHide","onLaunch","onError","onThemeChange","onPageNotFound","onUnhandledRejection","onExit","onInit","onLoad","onReady","onUnload","onResize","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onAddToFavorites","onShareAppMessage","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const Ce=[];const Oe=ce(((e,t)=>{if(P(e._component.onError))return t(e)})),Me=function(){};Me.prototype={on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t)for(var i=0,s=o.length;i<s;i++)o[i].fn!==t&&o[i].fn._!==t&&r.push(o[i]);return r.length?n[e]=r:delete n[e],this}};var Ae=Me;const Pe={black:"rgba(0,0,0,0.4)",white:"rgba(255,255,255,0.4)"};function Ie(e,t={},n="light"){const o=t[n],r={};return o?(Object.keys(e).forEach((i=>{let s=e[i];r[i]=(()=>{if(R(s))return Ie(s,t,n);if(C(s))return s.map((e=>R(e)?Ie(e,t,n):e));if(I(s)&&s.startsWith("@")){const t=s.replace("@","");let n=o[t]||s;switch(i){case"titleColor":n="black"===n?"#000000":"#ffffff";break;case"borderStyle":n=(e=n)&&e in Pe?Pe[e]:e}return n}var e;return s})()})),r):e}let $e;class De{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=$e,!e&&$e&&(this.index=($e.scopes||($e.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=$e;try{return $e=this,e()}finally{$e=t}}}on(){$e=this}off(){$e=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function Le(e){return new De(e)}const Be=e=>{const t=new Set(e);return t.w=0,t.n=0,t},Fe=e=>(e.w&qe)>0,Re=e=>(e.n&qe)>0,Ne=new WeakMap;let je=0,qe=1;let ze;const He=Symbol(""),Ve=Symbol("");class We{constructor(e,t=null,n){this.fn=e,this.scheduler=t,this.active=!0,this.deps=[],this.parent=void 0,function(e,t=$e){t&&t.active&&t.effects.push(e)}(this,n)}run(){if(!this.active)return this.fn();let e=ze,t=Ye;for(;e;){if(e===this)return;e=e.parent}try{return this.parent=ze,ze=this,Ye=!0,qe=1<<++je,je<=30?(({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=qe})(this):Ue(this),this.fn()}finally{je<=30&&(e=>{const{deps:t}=e;if(t.length){let n=0;for(let o=0;o<t.length;o++){const r=t[o];Fe(r)&&!Re(r)?r.delete(e):t[n++]=r,r.w&=~qe,r.n&=~qe}t.length=n}})(this),qe=1<<--je,ze=this.parent,Ye=t,this.parent=void 0,this.deferStop&&this.stop()}}stop(){ze===this?this.deferStop=!0:this.active&&(Ue(this),this.onStop&&this.onStop(),this.active=!1)}}function Ue(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let Ye=!0;const Xe=[];function Ke(){Xe.push(Ye),Ye=!1}function Ge(){const e=Xe.pop();Ye=void 0===e||e}function Ze(e,t,n){if(Ye&&ze){let t=Ne.get(e);t||Ne.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=Be()),Je(o)}}function Je(e,t){let n=!1;je<=30?Re(e)||(e.n|=qe,n=!Fe(e)):n=!e.has(ze),n&&(e.add(ze),ze.deps.push(e))}function Qe(e,t,n,o,r,i){const s=Ne.get(e);if(!s)return;let a=[];if("clear"===t)a=[...s.values()];else if("length"===n&&C(e)){const e=Number(o);s.forEach(((t,n)=>{("length"===n||n>=e)&&a.push(t)}))}else switch(void 0!==n&&a.push(s.get(n)),t){case"add":C(e)?N(n)&&a.push(s.get("length")):(a.push(s.get(He)),O(e)&&a.push(s.get(Ve)));break;case"delete":C(e)||(a.push(s.get(He)),O(e)&&a.push(s.get(Ve)));break;case"set":O(e)&&a.push(s.get(He))}if(1===a.length)a[0]&&et(a[0]);else{const e=[];for(const t of a)t&&e.push(...t);et(Be(e))}}function et(e,t){const n=C(e)?e:[...e];for(const o of n)o.computed&&tt(o);for(const o of n)o.computed||tt(o)}function tt(e,t){(e!==ze||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}const nt=n("__proto__,__v_isRef,__isVue"),ot=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter($)),rt=ut(),it=ut(!1,!0),st=ut(!0),at=lt();function lt(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=Xt(this);for(let t=0,r=this.length;t<r;t++)Ze(n,0,t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(Xt)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){Ke();const n=Xt(this)[t].apply(this,e);return Ge(),n}})),e}function ct(e){const t=Xt(this);return Ze(t,0,e),t.hasOwnProperty(e)}function ut(e=!1,t=!1){return function(n,o,r){if("__v_isReactive"===o)return!e;if("__v_isReadonly"===o)return e;if("__v_isShallow"===o)return t;if("__v_raw"===o&&r===(e?t?Nt:Rt:t?Ft:Bt).get(n))return n;const i=C(n);if(!e){if(i&&k(at,o))return Reflect.get(at,o,r);if("hasOwnProperty"===o)return ct}const s=Reflect.get(n,o,r);return($(o)?ot.has(o):nt(o))?s:(e||Ze(n,0,o),t?s:en(s)?i&&N(o)?s:s.value:D(s)?e?zt(s):qt(s):s)}}function ft(e=!1){return function(t,n,o,r){let i=t[n];if(Wt(i)&&en(i)&&!en(o))return!1;if(!e&&(Ut(o)||Wt(o)||(i=Xt(i),o=Xt(o)),!C(t)&&en(i)&&!en(o)))return i.value=o,!0;const s=C(t)&&N(n)?Number(n)<t.length:k(t,n),a=Reflect.set(t,n,o,r);return t===Xt(r)&&(s?X(o,i)&&Qe(t,"set",n,o):Qe(t,"add",n,o)),a}}const dt={get:rt,set:ft(),deleteProperty:function(e,t){const n=k(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&Qe(e,"delete",t,void 0),o},has:function(e,t){const n=Reflect.has(e,t);return $(t)&&ot.has(t)||Ze(e,0,t),n},ownKeys:function(e){return Ze(e,0,C(e)?"length":He),Reflect.ownKeys(e)}},ht={get:st,set:(e,t)=>!0,deleteProperty:(e,t)=>!0},pt=T({},dt,{get:it,set:ft(!0)}),gt=e=>e,mt=e=>Reflect.getPrototypeOf(e);function vt(e,t,n=!1,o=!1){const r=Xt(e=e.__v_raw),i=Xt(t);n||(t!==i&&Ze(r,0,t),Ze(r,0,i));const{has:s}=mt(r),a=o?gt:n?Zt:Gt;return s.call(r,t)?a(e.get(t)):s.call(r,i)?a(e.get(i)):void(e!==r&&e.get(t))}function yt(e,t=!1){const n=this.__v_raw,o=Xt(n),r=Xt(e);return t||(e!==r&&Ze(o,0,e),Ze(o,0,r)),e===r?n.has(e):n.has(e)||n.has(r)}function bt(e,t=!1){return e=e.__v_raw,!t&&Ze(Xt(e),0,He),Reflect.get(e,"size",e)}function _t(e){e=Xt(e);const t=Xt(this);return mt(t).has.call(t,e)||(t.add(e),Qe(t,"add",e,e)),this}function wt(e,t){t=Xt(t);const n=Xt(this),{has:o,get:r}=mt(n);let i=o.call(n,e);i||(e=Xt(e),i=o.call(n,e));const s=r.call(n,e);return n.set(e,t),i?X(t,s)&&Qe(n,"set",e,t):Qe(n,"add",e,t),this}function xt(e){const t=Xt(this),{has:n,get:o}=mt(t);let r=n.call(t,e);r||(e=Xt(e),r=n.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return r&&Qe(t,"delete",e,void 0),i}function Tt(){const e=Xt(this),t=0!==e.size,n=e.clear();return t&&Qe(e,"clear",void 0,void 0),n}function St(e,t){return function(n,o){const r=this,i=r.__v_raw,s=Xt(i),a=t?gt:e?Zt:Gt;return!e&&Ze(s,0,He),i.forEach(((e,t)=>n.call(o,a(e),a(t),r)))}}function Et(e,t,n){return function(...o){const r=this.__v_raw,i=Xt(r),s=O(i),a="entries"===e||e===Symbol.iterator&&s,l="keys"===e&&s,c=r[e](...o),u=n?gt:t?Zt:Gt;return!t&&Ze(i,0,l?Ve:He),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:a?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function kt(e){return function(...t){return"delete"!==e&&this}}function Ct(){const e={get(e){return vt(this,e)},get size(){return bt(this)},has:yt,add:_t,set:wt,delete:xt,clear:Tt,forEach:St(!1,!1)},t={get(e){return vt(this,e,!1,!0)},get size(){return bt(this)},has:yt,add:_t,set:wt,delete:xt,clear:Tt,forEach:St(!1,!0)},n={get(e){return vt(this,e,!0)},get size(){return bt(this,!0)},has(e){return yt.call(this,e,!0)},add:kt("add"),set:kt("set"),delete:kt("delete"),clear:kt("clear"),forEach:St(!0,!1)},o={get(e){return vt(this,e,!0,!0)},get size(){return bt(this,!0)},has(e){return yt.call(this,e,!0)},add:kt("add"),set:kt("set"),delete:kt("delete"),clear:kt("clear"),forEach:St(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((r=>{e[r]=Et(r,!1,!1),n[r]=Et(r,!0,!1),t[r]=Et(r,!1,!0),o[r]=Et(r,!0,!0)})),[e,n,t,o]}const[Ot,Mt,At,Pt]=Ct();function It(e,t){const n=t?e?Pt:At:e?Mt:Ot;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(k(n,o)&&o in t?n:t,o,r)}const $t={get:It(!1,!1)},Dt={get:It(!1,!0)},Lt={get:It(!0,!1)},Bt=new WeakMap,Ft=new WeakMap,Rt=new WeakMap,Nt=new WeakMap;function jt(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>F(e).slice(8,-1))(e))}function qt(e){return Wt(e)?e:Ht(e,!1,dt,$t,Bt)}function zt(e){return Ht(e,!0,ht,Lt,Rt)}function Ht(e,t,n,o,r){if(!D(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const s=jt(e);if(0===s)return e;const a=new Proxy(e,2===s?o:n);return r.set(e,a),a}function Vt(e){return Wt(e)?Vt(e.__v_raw):!(!e||!e.__v_isReactive)}function Wt(e){return!(!e||!e.__v_isReadonly)}function Ut(e){return!(!e||!e.__v_isShallow)}function Yt(e){return Vt(e)||Wt(e)}function Xt(e){const t=e&&e.__v_raw;return t?Xt(t):e}function Kt(e){return G(e,"__v_skip",!0),e}const Gt=e=>D(e)?qt(e):e,Zt=e=>D(e)?zt(e):e;function Jt(e){Ye&&ze&&Je((e=Xt(e)).dep||(e.dep=Be()))}function Qt(e,t){const n=(e=Xt(e)).dep;n&&et(n)}function en(e){return!(!e||!0!==e.__v_isRef)}function tn(e){return on(e,!1)}function nn(e){return on(e,!0)}function on(e,t){return en(e)?e:new rn(e,t)}class rn{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:Xt(e),this._value=t?e:Gt(e)}get value(){return Jt(this),this._value}set value(e){const t=this.__v_isShallow||Ut(e)||Wt(e);e=t?e:Xt(e),X(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:Gt(e),Qt(this))}}function sn(e){return en(e)?e.value:e}const an={get:(e,t,n)=>sn(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return en(r)&&!en(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function ln(e){return Vt(e)?e:new Proxy(e,an)}var cn;class un{constructor(e,t,n,o){this._setter=t,this.dep=void 0,this.__v_isRef=!0,this[cn]=!1,this._dirty=!0,this.effect=new We(e,(()=>{this._dirty||(this._dirty=!0,Qt(this))})),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=Xt(this);return Jt(e),!e._dirty&&e._cacheable||(e._dirty=!1,e._value=e.effect.run()),e._value}set value(e){this._setter(e)}}function fn(e,t,n,o){let r;try{r=o?e(...o):e()}catch(i){hn(i,t,n)}return r}function dn(e,t,n,o){if(P(e)){const r=fn(e,t,n,o);return r&&L(r)&&r.catch((e=>{hn(e,t,n)})),r}const r=[];for(let i=0;i<e.length;i++)r.push(dn(e[i],t,n,o));return r}function hn(e,t,n,o=!0){t&&t.vnode;if(t){let o=t.parent;const r=t.proxy,i=n;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const s=t.appContext.config.errorHandler;if(s)return void fn(s,null,10,[e,r,i])}!function(e,t,n,o=!0){console.error(e)}(e,0,0,o)}cn="__v_isReadonly";let pn=!1,gn=!1;const mn=[];let vn=0;const yn=[];let bn=null,_n=0;const wn=Promise.resolve();let xn=null;function Tn(e){const t=xn||wn;return e?t.then(this?e.bind(this):e):t}function Sn(e){mn.length&&mn.includes(e,pn&&e.allowRecurse?vn+1:vn)||(null==e.id?mn.push(e):mn.splice(function(e){let t=vn+1,n=mn.length;for(;t<n;){const o=t+n>>>1;On(mn[o])<e?t=o+1:n=o}return t}(e.id),0,e),En())}function En(){pn||gn||(gn=!0,xn=wn.then(An))}function kn(e,t=(pn?vn+1:0)){for(;t<mn.length;t++){const e=mn[t];e&&e.pre&&(mn.splice(t,1),t--,e())}}function Cn(e){if(yn.length){const e=[...new Set(yn)];if(yn.length=0,bn)return void bn.push(...e);for(bn=e,bn.sort(((e,t)=>On(e)-On(t))),_n=0;_n<bn.length;_n++)bn[_n]();bn=null,_n=0}}const On=e=>null==e.id?1/0:e.id,Mn=(e,t)=>{const n=On(e)-On(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function An(e){gn=!1,pn=!0,mn.sort(Mn);try{for(vn=0;vn<mn.length;vn++){const e=mn[vn];e&&!1!==e.active&&fn(e,null,14)}}finally{vn=0,mn.length=0,Cn(),pn=!1,xn=null,(mn.length||yn.length)&&An()}}function Pn(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||m;let r=n;const i=t.startsWith("update:"),s=i&&t.slice(7);if(s&&s in o){const e=`${"modelValue"===s?"model":s}Modifiers`,{number:t,trim:i}=o[e]||m;i&&(r=n.map((e=>I(e)?e.trim():e))),t&&(r=n.map(Z))}let a,l=o[a=Y(t)]||o[a=Y(H(t))];!l&&i&&(l=o[a=Y(W(t))]),l&&dn(l,e,6,In(e,l,r));const c=o[a+"Once"];if(c){if(e.emitted){if(e.emitted[a])return}else e.emitted={};e.emitted[a]=!0,dn(c,e,6,In(e,c,r))}}function In(e,t,n){if(1!==n.length)return n;if(P(t)){if(t.length<2)return n}else if(!t.find((e=>e.length>=2)))return n;const o=n[0];if(o&&k(o,"type")&&k(o,"timeStamp")&&k(o,"target")&&k(o,"currentTarget")&&k(o,"detail")){const t=e.proxy,o=t.$gcd(t,!0);o&&n.push(o)}return n}function $n(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const i=e.emits;let s={},a=!1;if(!P(e)){const o=e=>{const n=$n(e,t,!0);n&&(a=!0,T(s,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||a?(C(i)?i.forEach((e=>s[e]=null)):T(s,i),D(e)&&o.set(e,s),s):(D(e)&&o.set(e,null),null)}function Dn(e,t){return!(!e||!w(t))&&(t=t.slice(2).replace(/Once$/,""),k(e,t[0].toLowerCase()+t.slice(1))||k(e,W(t))||k(e,t))}let Ln=null,Bn=null;function Fn(e){const t=Ln;return Ln=e,Bn=e&&e.type.__scopeId||null,t}function Rn(e,t=Ln,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&Nr(-1);const r=Fn(t);let i;try{i=e(...n)}finally{Fn(r),o._d&&Nr(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function Nn(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[s],slots:a,attrs:l,emit:c,render:u,renderCache:f,data:d,setupState:h,ctx:p,inheritAttrs:g}=e;let m,v;const y=Fn(e);try{if(4&n.shapeFlag){const e=r||o;m=ei(u.call(e,e,f,i,h,d,p)),v=l}else{const e=t;0,m=ei(e.length>1?e(i,{attrs:l,slots:a,emit:c}):e(i,null)),v=t.props?l:jn(l)}}catch(_){Lr.length=0,hn(_,e,1),m=Kr($r)}let b=m;if(v&&!1!==g){const e=Object.keys(v),{shapeFlag:t}=b;e.length&&7&t&&(s&&e.some(x)&&(v=qn(v,s)),b=Zr(b,v))}return n.dirs&&(b=Zr(b),b.dirs=b.dirs?b.dirs.concat(n.dirs):n.dirs),n.transition&&(b.transition=n.transition),m=b,Fn(y),m}const jn=e=>{let t;for(const n in e)("class"===n||"style"===n||w(n))&&((t||(t={}))[n]=e[n]);return t},qn=(e,t)=>{const n={};for(const o in e)x(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function zn(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const i=o[r];if(t[i]!==e[i]&&!Dn(n,i))return!0}return!1}const Hn=e=>e.__isSuspense;function Vn(e,t){if(ai){let n=ai.provides;const o=ai.parent&&ai.parent.provides;o===n&&(n=ai.provides=Object.create(o)),n[e]=t,"app"===ai.type.mpType&&ai.appContext.app.provide(e,t)}else;}function Wn(e,t,n=!1){const o=ai||Ln;if(o){const r=null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&P(t)?t.call(o.proxy):t}}function Un(e,t){return Kn(e,null,t)}const Yn={};function Xn(e,t,n){return Kn(e,t,n)}function Kn(e,t,{immediate:n,deep:o,flush:r,onTrack:i,onTrigger:s}=m){const a=$e===(null==ai?void 0:ai.scope)?ai:null;let l,c,u=!1,f=!1;if(en(e)?(l=()=>e.value,u=Ut(e)):Vt(e)?(l=()=>e,o=!0):C(e)?(f=!0,u=e.some((e=>Vt(e)||Ut(e))),l=()=>e.map((e=>en(e)?e.value:Vt(e)?Jn(e):P(e)?fn(e,a,2):void 0))):l=P(e)?t?()=>fn(e,a,2):()=>{if(!a||!a.isUnmounted)return c&&c(),dn(e,a,3,[h])}:y,t&&o){const e=l;l=()=>Jn(e())}let d,h=e=>{c=b.onStop=()=>{fn(e,a,4)}};if(di){if(h=y,t?n&&dn(t,a,3,[l(),f?[]:void 0,h]):l(),"sync"!==r)return y;{const e=_i();d=e.__watcherHandles||(e.__watcherHandles=[])}}let p=f?new Array(e.length).fill(Yn):Yn;const g=()=>{if(b.active)if(t){const e=b.run();(o||u||(f?e.some(((e,t)=>X(e,p[t]))):X(e,p)))&&(c&&c(),dn(t,a,3,[e,p===Yn?void 0:f&&p[0]===Yn?[]:p,h]),p=e)}else b.run()};let v;g.allowRecurse=!!t,"sync"===r?v=g:"post"===r?v=()=>Cr(g,a&&a.suspense):(g.pre=!0,a&&(g.id=a.uid),v=()=>Sn(g));const b=new We(l,v);t?n?g():p=b.run():"post"===r?Cr(b.run.bind(b),a&&a.suspense):b.run();const _=()=>{b.stop(),a&&a.scope&&S(a.scope.effects,b)};return d&&d.push(_),_}function Gn(e,t,n){const o=this.proxy,r=I(e)?e.includes(".")?Zn(o,e):()=>o[e]:e.bind(o,o);let i;P(t)?i=t:(i=t.handler,n=t);const s=ai;ci(this);const a=Kn(r,i.bind(o),n);return s?ci(s):ui(),a}function Zn(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function Jn(e,t){if(!D(e)||e.__v_skip)return e;if((t=t||new Set).has(e))return e;if(t.add(e),en(e))Jn(e.value,t);else if(C(e))for(let n=0;n<e.length;n++)Jn(e[n],t);else if(M(e)||O(e))e.forEach((e=>{Jn(e,t)}));else if(R(e))for(const n in e)Jn(e[n],t);return e}const Qn=[Function,Array],eo={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Qn,onEnter:Qn,onAfterEnter:Qn,onEnterCancelled:Qn,onBeforeLeave:Qn,onLeave:Qn,onAfterLeave:Qn,onLeaveCancelled:Qn,onBeforeAppear:Qn,onAppear:Qn,onAfterAppear:Qn,onAppearCancelled:Qn},to={name:"BaseTransition",props:eo,setup(e,{slots:t}){const n=li(),o=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Ao((()=>{e.isMounted=!0})),$o((()=>{e.isUnmounting=!0})),e}();let r;return()=>{const i=t.default&&ao(t.default(),!0);if(!i||!i.length)return;let s=i[0];if(i.length>1)for(const e of i)if(e.type!==$r){s=e;break}const a=Xt(e),{mode:l}=a;if(o.isLeaving)return ro(s);const c=io(s);if(!c)return ro(s);const u=oo(c,a,o,n);so(c,u);const f=n.subTree,d=f&&io(f);let h=!1;const{getTransitionKey:p}=c.type;if(p){const e=p();void 0===r?r=e:e!==r&&(r=e,h=!0)}if(d&&d.type!==$r&&(!Vr(c,d)||h)){const e=oo(d,a,o,n);if(so(d,e),"out-in"===l)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,!1!==n.update.active&&n.update()},ro(s);"in-out"===l&&c.type!==$r&&(e.delayLeave=(e,t,n)=>{no(o,d)[String(d.key)]=d,e._leaveCb=()=>{t(),e._leaveCb=void 0,delete u.delayedLeave},u.delayedLeave=n})}return s}}};function no(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function oo(e,t,n,o){const{appear:r,mode:i,persisted:s=!1,onBeforeEnter:a,onEnter:l,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:f,onLeave:d,onAfterLeave:h,onLeaveCancelled:p,onBeforeAppear:g,onAppear:m,onAfterAppear:v,onAppearCancelled:y}=t,b=String(e.key),_=no(n,e),w=(e,t)=>{e&&dn(e,o,9,t)},x=(e,t)=>{const n=t[1];w(e,t),C(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},T={mode:i,persisted:s,beforeEnter(t){let o=a;if(!n.isMounted){if(!r)return;o=g||a}t._leaveCb&&t._leaveCb(!0);const i=_[b];i&&Vr(e,i)&&i.el._leaveCb&&i.el._leaveCb(),w(o,[t])},enter(e){let t=l,o=c,i=u;if(!n.isMounted){if(!r)return;t=m||l,o=v||c,i=y||u}let s=!1;const a=e._enterCb=t=>{s||(s=!0,w(t?i:o,[e]),T.delayedLeave&&T.delayedLeave(),e._enterCb=void 0)};t?x(t,[e,a]):a()},leave(t,o){const r=String(e.key);if(t._enterCb&&t._enterCb(!0),n.isUnmounting)return o();w(f,[t]);let i=!1;const s=t._leaveCb=n=>{i||(i=!0,o(),w(n?p:h,[t]),t._leaveCb=void 0,_[r]===e&&delete _[r])};_[r]=e,d?x(d,[t,s]):s()},clone:e=>oo(e,t,n,o)};return T}function ro(e){if(ho(e))return(e=Zr(e)).children=null,e}function io(e){return ho(e)?e.children?e.children[0]:void 0:e}function so(e,t){6&e.shapeFlag&&e.component?so(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function ao(e,t=!1,n){let o=[],r=0;for(let i=0;i<e.length;i++){let s=e[i];const a=null==n?s.key:String(n)+String(null!=s.key?s.key:i);s.type===Pr?(128&s.patchFlag&&r++,o=o.concat(ao(s.children,t,a))):(t||s.type!==$r)&&o.push(null!=a?Zr(s,{key:a}):s)}if(r>1)for(let i=0;i<o.length;i++)o[i].patchFlag=-2;return o}function lo(e){return P(e)?{setup:e,name:e.name}:e}const co=e=>!!e.type.__asyncLoader;function uo(e){P(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:o,delay:r=200,timeout:i,suspensible:s=!0,onError:a}=e;let l,c=null,u=0;const f=()=>{let e;return c||(e=c=t().catch((e=>{if(e=e instanceof Error?e:new Error(String(e)),a)return new Promise(((t,n)=>{a(e,(()=>t((u++,c=null,f()))),(()=>n(e)),u+1)}));throw e})).then((t=>e!==c&&c?c:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),l=t,t))))};return lo({name:"AsyncComponentWrapper",__asyncLoader:f,get __asyncResolved(){return l},setup(){const e=ai;if(l)return()=>fo(l,e);const t=t=>{c=null,hn(t,e,13,!o)};if(s&&e.suspense||di)return f().then((t=>()=>fo(t,e))).catch((e=>(t(e),()=>o?Kr(o,{error:e}):null)));const a=tn(!1),u=tn(),d=tn(!!r);return r&&setTimeout((()=>{d.value=!1}),r),null!=i&&setTimeout((()=>{if(!a.value&&!u.value){const e=new Error(`Async component timed out after ${i}ms.`);t(e),u.value=e}}),i),f().then((()=>{a.value=!0,e.parent&&ho(e.parent.vnode)&&Sn(e.parent.update)})).catch((e=>{t(e),u.value=e})),()=>a.value&&l?fo(l,e):u.value&&o?Kr(o,{error:u.value}):n&&!d.value?Kr(n):void 0}})}function fo(e,t){const{ref:n,props:o,children:r,ce:i}=t.vnode,s=Kr(e,o,r);return s.ref=n,s.ce=i,delete t.vnode.ce,s}const ho=e=>e.type.__isKeepAlive;class po{constructor(e){this.max=e,this._cache=new Map,this._keys=new Set,this._max=parseInt(e,10)}get(e){const{_cache:t,_keys:n,_max:o}=this,r=t.get(e);if(r)n.delete(e),n.add(e);else if(n.add(e),o&&n.size>o){const e=n.values().next().value;this.pruneCacheEntry(t.get(e)),this.delete(e)}return r}set(e,t){this._cache.set(e,t)}delete(e){this._cache.delete(e),this._keys.delete(e)}forEach(e,t){this._cache.forEach(e.bind(t))}}const go={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number],matchBy:{type:String,default:"name"},cache:Object},setup(e,{slots:t}){const n=li(),o=n.ctx;if(!o.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const r=e.cache||new po(e.max);r.pruneCacheEntry=s;let i=null;function s(t){var o;!i||!Vr(t,i)||"key"===e.matchBy&&t.key!==i.key?(xo(o=t),u(o,n,a,!0)):i&&xo(i)}const a=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:f}}}=o,d=f("div");function h(t){r.forEach(((n,o)=>{const i=So(n,e.matchBy);!i||t&&t(i)||(r.delete(o),s(n))}))}o.activate=(e,t,n,o,r)=>{const i=e.component;if(i.ba){const e=i.isDeactivated;i.isDeactivated=!1,K(i.ba),i.isDeactivated=e}c(e,t,n,0,a),l(i.vnode,e,t,n,i,a,o,e.slotScopeIds,r),Cr((()=>{i.isDeactivated=!1,i.a&&K(i.a);const t=e.props&&e.props.onVnodeMounted;t&&ri(t,i.parent,e)}),a)},o.deactivate=e=>{const t=e.component;t.bda&&Eo(t.bda),c(e,d,null,1,a),Cr((()=>{t.bda&&ko(t.bda),t.da&&K(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&ri(n,t.parent,e),t.isDeactivated=!0}),a)},Xn((()=>[e.include,e.exclude,e.matchBy]),(([e,t])=>{e&&h((t=>vo(e,t))),t&&h((e=>!vo(t,e)))}),{flush:"post",deep:!0});let p=null;const g=()=>{null!=p&&r.set(p,To(n.subTree))};return Ao(g),Io(g),$o((()=>{r.forEach(((t,o)=>{r.delete(o),s(t);const{subTree:i,suspense:a}=n,l=To(i);if(t.type!==l.type||"key"===e.matchBy&&t.key!==l.key);else{l.component.bda&&K(l.component.bda),xo(l);const e=l.component.da;e&&Cr(e,a)}}))})),()=>{if(p=null,!t.default)return null;const n=t.default(),o=n[0];if(n.length>1)return i=null,n;if(!Hr(o)||!(4&o.shapeFlag)&&!Hn(o.type))return i=null,o;let s=To(o);const a=s.type,l=So(s,e.matchBy),{include:c,exclude:u}=e;if(c&&(!l||!vo(c,l))||u&&l&&vo(u,l))return i=s,o;const f=null==s.key?a:s.key,d=r.get(f);return s.el&&(s=Zr(s),Hn(o.type)&&(o.ssContent=s)),p=f,d&&(s.el=d.el,s.component=d.component,s.transition&&so(s,s.transition),s.shapeFlag|=512),s.shapeFlag|=256,i=s,Hn(o.type)?o:s}}},mo=go;function vo(e,t){return C(e)?e.some((e=>vo(e,t))):I(e)?e.split(",").includes(t):!!e.test&&e.test(t)}function yo(e,t){_o(e,"a",t)}function bo(e,t){_o(e,"da",t)}function _o(e,t,n=ai){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(o.__called=!1,Co(t,o,n),n){let e=n.parent;for(;e&&e.parent;)ho(e.parent.vnode)&&wo(o,t,n,e),e=e.parent}}function wo(e,t,n,o){const r=Co(t,e,o,!0);Do((()=>{S(o[t],r)}),n)}function xo(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function To(e){return Hn(e.type)?e.ssContent:e}function So(e,t){if("name"===t){const t=e.type;return mi(co(e)?t.__asyncResolved||{}:t)}return String(e.key)}function Eo(e){for(let t=0;t<e.length;t++){const n=e[t];n.__called||(n(),n.__called=!0)}}function ko(e){e.forEach((e=>e.__called=!1))}function Co(e,t,n=ai,o=!1){if(n){if(r=e,Se.indexOf(r)>-1&&n.$pageInstance){if(n.type.__reserved)return;if(n!==n.$pageInstance&&(n=n.$pageInstance,function(e){return Ee.indexOf(e)>-1}(e))){const o=n.proxy;dn(t.bind(o),n,e,"onLoad"===e?[o.$page.options]:[])}}const i=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;Ke(),ci(n);const r=dn(t,n,e,o);return ui(),Ge(),r});return o?i.unshift(s):i.push(s),s}var r}const Oo=e=>(t,n=ai)=>(!di||"sp"===e)&&Co(e,((...e)=>t(...e)),n),Mo=Oo("bm"),Ao=Oo("m"),Po=Oo("bu"),Io=Oo("u"),$o=Oo("bum"),Do=Oo("um"),Lo=Oo("sp"),Bo=Oo("rtg"),Fo=Oo("rtc");function Ro(e,t=ai){Co("ec",e,t)}function No(e,t){const n=Ln;if(null===n)return e;const o=gi(n)||n.proxy,r=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[e,n,s,a=m]=t[i];e&&(P(e)&&(e={mounted:e,updated:e}),e.deep&&Jn(n),r.push({dir:e,instance:o,value:n,oldValue:void 0,arg:s,modifiers:a}))}return e}function jo(e,t,n,o){const r=e.dirs,i=t&&t.dirs;for(let s=0;s<r.length;s++){const a=r[s];i&&(a.oldValue=i[s].value);let l=a.dir[o];l&&(Ke(),dn(l,n,8,[e.el,a,e,t]),Ge())}}function qo(e,t){return Vo("components",e,!0,t)||e}const zo=Symbol();function Ho(e){return I(e)?Vo("components",e,!1)||e:e||zo}function Vo(e,t,n=!0,o=!1){const r=Ln||ai;if(r){const n=r.type;if("components"===e){const e=mi(n,!1);if(e&&(e===t||e===H(t)||e===U(H(t))))return n}const i=Wo(r[e]||n[e],t)||Wo(r.appContext[e],t);return!i&&o?n:i}}function Wo(e,t){return e&&(e[t]||e[H(t)]||e[U(H(t))])}function Uo(e,t,n,o){let r;const i=n&&n[o];if(C(e)||I(e)){r=new Array(e.length);for(let n=0,o=e.length;n<o;n++)r[n]=t(e[n],n,void 0,i&&i[n])}else if("number"==typeof e){r=new Array(e);for(let n=0;n<e;n++)r[n]=t(n+1,n,void 0,i&&i[n])}else if(D(e))if(e[Symbol.iterator])r=Array.from(e,((e,n)=>t(e,n,void 0,i&&i[n])));else{const n=Object.keys(e);r=new Array(n.length);for(let o=0,s=n.length;o<s;o++){const s=n[o];r[o]=t(e[s],s,o,i&&i[o])}}else r=[];return n&&(n[o]=r),r}function Yo(e,t,n={},o,r){if(Ln.isCE||Ln.parent&&co(Ln.parent)&&Ln.parent.isCE)return"default"!==t&&(n.name=t),Kr("slot",n,o&&o());let i=e[t];i&&i._c&&(i._d=!1),Fr();const s=i&&Xo(i(n)),a=zr(Pr,{key:n.key||s&&s.key||`_${t}`},s||(o?o():[]),s&&1===e._?64:-2);return!r&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),i&&i._c&&(i._d=!0),a}function Xo(e){return e.some((e=>!Hr(e)||e.type!==$r&&!(e.type===Pr&&!Xo(e.children))))?e:null}const Ko=e=>e?fi(e)?gi(e)||e.proxy:Ko(e.parent):null,Go=T(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ko(e.parent),$root:e=>Ko(e.root),$emit:e=>e.emit,$options:e=>or(e),$forceUpdate:e=>e.f||(e.f=()=>Sn(e.update)),$nextTick:e=>e.n||(e.n=Tn.bind(e.proxy)),$watch:e=>Gn.bind(e)}),Zo=(e,t)=>e!==m&&!e.__isScriptSetup&&k(e,t),Jo={get({_:e},t){const{ctx:n,setupState:o,data:r,props:i,accessCache:s,type:a,appContext:l}=e;let c;if("$"!==t[0]){const a=s[t];if(void 0!==a)switch(a){case 1:return o[t];case 2:return r[t];case 4:return n[t];case 3:return i[t]}else{if(Zo(o,t))return s[t]=1,o[t];if(r!==m&&k(r,t))return s[t]=2,r[t];if((c=e.propsOptions[0])&&k(c,t))return s[t]=3,i[t];if(n!==m&&k(n,t))return s[t]=4,n[t];Qo&&(s[t]=0)}}const u=Go[t];let f,d;return u?("$attrs"===t&&Ze(e,0,t),u(e)):(f=a.__cssModules)&&(f=f[t])?f:n!==m&&k(n,t)?(s[t]=4,n[t]):(d=l.config.globalProperties,k(d,t)?d[t]:void 0)},set({_:e},t,n){const{data:o,setupState:r,ctx:i}=e;return Zo(r,t)?(r[t]=n,!0):o!==m&&k(o,t)?(o[t]=n,!0):!k(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(i[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:r,propsOptions:i}},s){let a;return!!n[s]||e!==m&&k(e,s)||Zo(t,s)||(a=i[0])&&k(a,s)||k(o,s)||k(Go,s)||k(r.config.globalProperties,s)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:k(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};let Qo=!0;function er(e){const t=or(e),n=e.proxy,o=e.ctx;Qo=!1,t.beforeCreate&&tr(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:s,watch:a,provide:l,inject:c,created:u,beforeMount:f,mounted:d,beforeUpdate:h,updated:p,activated:g,deactivated:m,beforeDestroy:v,beforeUnmount:b,destroyed:_,unmounted:w,render:x,renderTracked:T,renderTriggered:S,errorCaptured:E,serverPrefetch:k,expose:O,inheritAttrs:M,components:A,directives:I,filters:$}=t;if(c&&function(e,t,n=y,o=!1){C(e)&&(e=ar(e));for(const r in e){const n=e[r];let i;i=D(n)?"default"in n?Wn(n.from||r,n.default,!0):Wn(n.from||r):Wn(n),en(i)&&o?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:e=>i.value=e}):t[r]=i}}(c,o,null,e.appContext.config.unwrapInjectedRef),s)for(const y in s){const e=s[y];P(e)&&(o[y]=e.bind(n))}if(r){const t=r.call(n,n);D(t)&&(e.data=qt(t))}if(Qo=!0,i)for(const C in i){const e=i[C],t=P(e)?e.bind(n,n):P(e.get)?e.get.bind(n,n):y,r=!P(e)&&P(e.set)?e.set.bind(n):y,s=vi({get:t,set:r});Object.defineProperty(o,C,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e})}if(a)for(const y in a)nr(a[y],o,n,y);if(l){const e=P(l)?l.call(n):l;Reflect.ownKeys(e).forEach((t=>{Vn(t,e[t])}))}function L(e,t){C(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(u&&tr(u,e,"c"),L(Mo,f),L(Ao,d),L(Po,h),L(Io,p),L(yo,g),L(bo,m),L(Ro,E),L(Fo,T),L(Bo,S),L($o,b),L(Do,w),L(Lo,k),C(O))if(O.length){const t=e.exposed||(e.exposed={});O.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});x&&e.render===y&&(e.render=x),null!=M&&(e.inheritAttrs=M),A&&(e.components=A),I&&(e.directives=I);const B=e.appContext.config.globalProperties.$applyOptions;B&&B(t,e,n)}function tr(e,t,n){dn(C(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function nr(e,t,n,o){const r=o.includes(".")?Zn(n,o):()=>n[o];if(I(e)){const n=t[e];P(n)&&Xn(r,n)}else if(P(e))Xn(r,e.bind(n));else if(D(e))if(C(e))e.forEach((e=>nr(e,t,n,o)));else{const o=P(e.handler)?e.handler.bind(n):t[e.handler];P(o)&&Xn(r,o,e)}}function or(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,a=i.get(t);let l;return a?l=a:r.length||n||o?(l={},r.length&&r.forEach((e=>rr(l,e,s,!0))),rr(l,t,s)):l=t,D(t)&&i.set(t,l),l}function rr(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&rr(e,i,n,!0),r&&r.forEach((t=>rr(e,t,n,!0)));for(const s in t)if(o&&"expose"===s);else{const o=ir[s]||n&&n[s];e[s]=o?o(e[s],t[s]):t[s]}return e}const ir={data:sr,props:cr,emits:cr,methods:cr,computed:cr,beforeCreate:lr,created:lr,beforeMount:lr,mounted:lr,beforeUpdate:lr,updated:lr,beforeDestroy:lr,beforeUnmount:lr,destroyed:lr,unmounted:lr,activated:lr,deactivated:lr,errorCaptured:lr,serverPrefetch:lr,components:cr,directives:cr,watch:function(e,t){if(!e)return t;if(!t)return e;const n=T(Object.create(null),e);for(const o in t)n[o]=lr(e[o],t[o]);return n},provide:sr,inject:function(e,t){return cr(ar(e),ar(t))}};function sr(e,t){return t?e?function(){return T(P(e)?e.call(this,this):e,P(t)?t.call(this,this):t)}:t:e}function ar(e){if(C(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function lr(e,t){return e?[...new Set([].concat(e,t))]:t}function cr(e,t){return e?T(T(Object.create(null),e),t):t}function ur(e,t,n,o=!1){const r={},i={};G(i,Wr,1),e.propsDefaults=Object.create(null),fr(e,t,r,i);for(const s in e.propsOptions[0])s in r||(r[s]=void 0);n?e.props=o?r:Ht(r,!1,pt,Dt,Ft):e.type.props?e.props=r:e.props=i,e.attrs=i}function fr(e,t,n,o){const[r,i]=e.propsOptions;let s,a=!1;if(t)for(let l in t){if(j(l))continue;const c=t[l];let u;r&&k(r,u=H(l))?i&&i.includes(u)?(s||(s={}))[u]=c:n[u]=c:Dn(e.emitsOptions,l)||l in o&&c===o[l]||(o[l]=c,a=!0)}if(i){const t=Xt(n),o=s||m;for(let s=0;s<i.length;s++){const a=i[s];n[a]=dr(r,t,a,o[a],e,!k(o,a))}}return a}function dr(e,t,n,o,r,i){const s=e[n];if(null!=s){const e=k(s,"default");if(e&&void 0===o){const e=s.default;if(s.type!==Function&&P(e)){const{propsDefaults:i}=r;n in i?o=i[n]:(ci(r),o=i[n]=e.call(null,t),ui())}else o=e}s[0]&&(i&&!e?o=!1:!s[1]||""!==o&&o!==W(n)||(o=!0))}return o}function hr(e,t,n=!1){const o=t.propsCache,r=o.get(e);if(r)return r;const i=e.props,s={},a=[];let l=!1;if(!P(e)){const o=e=>{l=!0;const[n,o]=hr(e,t,!0);T(s,n),o&&a.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!i&&!l)return D(e)&&o.set(e,v),v;if(C(i))for(let u=0;u<i.length;u++){const e=H(i[u]);pr(e)&&(s[e]=m)}else if(i)for(const u in i){const e=H(u);if(pr(e)){const t=i[u],n=s[e]=C(t)||P(t)?{type:t}:Object.assign({},t);if(n){const t=vr(Boolean,n.type),o=vr(String,n.type);n[0]=t>-1,n[1]=o<0||t<o,(t>-1||k(n,"default"))&&a.push(e)}}}const c=[s,a];return D(e)&&o.set(e,c),c}function pr(e){return"$"!==e[0]}function gr(e){const t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:null===e?"null":""}function mr(e,t){return gr(e)===gr(t)}function vr(e,t){return C(t)?t.findIndex((t=>mr(t,e))):P(t)&&mr(t,e)?0:-1}const yr=e=>"_"===e[0]||"$stable"===e,br=e=>C(e)?e.map(ei):[ei(e)],_r=(e,t,n)=>{if(t._n)return t;const o=Rn(((...e)=>br(t(...e))),n);return o._c=!1,o},wr=(e,t,n)=>{const o=e._ctx;for(const r in e){if(yr(r))continue;const n=e[r];if(P(n))t[r]=_r(0,n,o);else if(null!=n){const e=br(n);t[r]=()=>e}}},xr=(e,t)=>{const n=br(t);e.slots.default=()=>n};function Tr(){return{app:null,config:{isNativeTag:b,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Sr=0;function Er(e,t){return function(n,o=null){P(n)||(n=Object.assign({},n)),null==o||D(o)||(o=null);const r=Tr(),i=new Set;let s=!1;const a=r.app={_uid:Sr++,_component:n,_props:o,_container:null,_context:r,_instance:null,version:wi,get config(){return r.config},set config(e){},use:(e,...t)=>(i.has(e)||(e&&P(e.install)?(i.add(e),e.install(a,...t)):P(e)&&(i.add(e),e(a,...t))),a),mixin:e=>(r.mixins.includes(e)||r.mixins.push(e),a),component:(e,t)=>t?(r.components[e]=t,a):r.components[e],directive:(e,t)=>t?(r.directives[e]=t,a):r.directives[e],mount(i,l,c){if(!s){const u=Kr(n,o);return u.appContext=r,l&&t?t(u,i):e(u,i,c),s=!0,a._container=i,i.__vue_app__=a,a._instance=u.component,gi(u.component)||u.component.proxy}},unmount(){s&&(e(null,a._container),delete a._container.__vue_app__)},provide:(e,t)=>(r.provides[e]=t,a)};return a}}function kr(e,t,n,o,r=!1){if(C(e))return void e.forEach(((e,i)=>kr(e,t&&(C(t)?t[i]:t),n,o,r)));if(co(o)&&!r)return;const i=4&o.shapeFlag?gi(o.component)||o.component.proxy:o.el,s=r?null:i,{i:a,r:l}=e,c=t&&t.r,u=a.refs===m?a.refs={}:a.refs,f=a.setupState;if(null!=c&&c!==l&&(I(c)?(u[c]=null,k(f,c)&&(f[c]=null)):en(c)&&(c.value=null)),P(l))fn(l,a,12,[s,u]);else{const t=I(l),o=en(l);if(t||o){const a=()=>{if(e.f){const n=t?k(f,l)?f[l]:u[l]:l.value;r?C(n)&&S(n,i):C(n)?n.includes(i)||n.push(i):t?(u[l]=[i],k(f,l)&&(f[l]=u[l])):(l.value=[i],e.k&&(u[e.k]=l.value))}else t?(u[l]=s,k(f,l)&&(f[l]=s)):o&&(l.value=s,e.k&&(u[e.k]=s))};s?(a.id=-1,Cr(a,n)):a()}}}const Cr=function(e,t){var n;t&&t.pendingBranch?C(e)?t.effects.push(...e):t.effects.push(e):(C(n=e)?yn.push(...n):bn&&bn.includes(n,n.allowRecurse?_n+1:_n)||yn.push(n),En())};function Or(e){return function(e,t){(J||(J="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})).__VUE__=!0;const{insert:n,remove:o,patchProp:r,forcePatchProp:i,createElement:s,createText:a,createComment:l,setText:c,setElementText:u,parentNode:f,nextSibling:d,setScopeId:h=y,insertStaticContent:p}=e,g=(e,t,n,o=null,r=null,i=null,s=!1,a=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!Vr(e,t)&&(o=te(e),Y(e,r,i,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:f}=t;switch(c){case Ir:b(e,t,n,o);break;case $r:_(e,t,n,o);break;case Dr:null==e&&w(t,n,o,s);break;case Pr:$(e,t,n,o,r,i,s,a,l);break;default:1&f?E(e,t,n,o,r,i,s,a,l):6&f?D(e,t,n,o,r,i,s,a,l):(64&f||128&f)&&c.process(e,t,n,o,r,i,s,a,l,oe)}null!=u&&r&&kr(u,e&&e.ref,i,t||e,!t)},b=(e,t,o,r)=>{if(null==e)n(t.el=a(t.children),o,r);else{const n=t.el=e.el;t.children!==e.children&&c(n,t.children)}},_=(e,t,o,r)=>{null==e?n(t.el=l(t.children||""),o,r):t.el=e.el},w=(e,t,n,o)=>{[e.el,e.anchor]=p(e.children,t,n,o,e.el,e.anchor)},x=({el:e,anchor:t},o,r)=>{let i;for(;e&&e!==t;)i=d(e),n(e,o,r),e=i;n(t,o,r)},S=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=d(e),o(e),e=n;o(t)},E=(e,t,n,o,r,i,s,a,l)=>{s=s||"svg"===t.type,null==e?C(t,n,o,r,i,s,a,l):A(e,t,r,i,s,a,l)},C=(e,t,o,i,a,l,c,f)=>{let d,h;const{type:p,props:g,shapeFlag:m,transition:v,dirs:y}=e;if(d=e.el=s(e.type,l,g&&g.is,g),8&m?u(d,e.children):16&m&&M(e.children,d,null,i,a,l&&"foreignObject"!==p,c,f),y&&jo(e,null,i,"created"),O(d,e,e.scopeId,c,i),g){for(const t in g)"value"===t||j(t)||r(d,t,null,g[t],l,e.children,i,a,ee);"value"in g&&r(d,"value",null,g.value),(h=g.onVnodeBeforeMount)&&ri(h,i,e)}Object.defineProperty(d,"__vueParentComponent",{value:i,enumerable:!1}),y&&jo(e,null,i,"beforeMount");const b=(!a||a&&!a.pendingBranch)&&v&&!v.persisted;b&&v.beforeEnter(d),n(d,t,o),((h=g&&g.onVnodeMounted)||b||y)&&Cr((()=>{h&&ri(h,i,e),b&&v.enter(d),y&&jo(e,null,i,"mounted")}),a)},O=(e,t,n,o,r)=>{if(n&&h(e,n),o)for(let i=0;i<o.length;i++)h(e,o[i]);if(r){if(t===r.subTree){const t=r.vnode;O(e,t,t.scopeId,t.slotScopeIds,r.parent)}}},M=(e,t,n,o,r,i,s,a,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=a?ti(e[c]):ei(e[c]);g(null,l,t,n,o,r,i,s,a)}},A=(e,t,n,o,s,a,l)=>{const c=t.el=e.el;let{patchFlag:f,dynamicChildren:d,dirs:h}=t;f|=16&e.patchFlag;const p=e.props||m,g=t.props||m;let v;n&&Mr(n,!1),(v=g.onVnodeBeforeUpdate)&&ri(v,n,t,e),h&&jo(t,e,n,"beforeUpdate"),n&&Mr(n,!0);const y=s&&"foreignObject"!==t.type;if(d?P(e.dynamicChildren,d,c,n,o,y,a):l||q(e,t,c,null,n,o,y,a,!1),f>0){if(16&f)I(c,t,p,g,n,o,s);else if(2&f&&p.class!==g.class&&r(c,"class",null,g.class,s),4&f&&r(c,"style",p.style,g.style,s),8&f){const a=t.dynamicProps;for(let t=0;t<a.length;t++){const l=a[t],u=p[l],f=g[l];(f!==u||"value"===l||i&&i(c,l))&&r(c,l,u,f,s,e.children,n,o,ee)}}1&f&&e.children!==t.children&&u(c,t.children)}else l||null!=d||I(c,t,p,g,n,o,s);((v=g.onVnodeUpdated)||h)&&Cr((()=>{v&&ri(v,n,t,e),h&&jo(t,e,n,"updated")}),o)},P=(e,t,n,o,r,i,s)=>{for(let a=0;a<t.length;a++){const l=e[a],c=t[a],u=l.el&&(l.type===Pr||!Vr(l,c)||70&l.shapeFlag)?f(l.el):n;g(l,c,u,null,o,r,i,s,!0)}},I=(e,t,n,o,s,a,l)=>{if(n!==o){if(n!==m)for(const i in n)j(i)||i in o||r(e,i,n[i],null,l,t.children,s,a,ee);for(const c in o){if(j(c))continue;const u=o[c],f=n[c];(u!==f&&"value"!==c||i&&i(e,c))&&r(e,c,f,u,l,t.children,s,a,ee)}"value"in o&&r(e,"value",n.value,o.value)}},$=(e,t,o,r,i,s,l,c,u)=>{const f=t.el=e?e.el:a(""),d=t.anchor=e?e.anchor:a("");let{patchFlag:h,dynamicChildren:p,slotScopeIds:g}=t;g&&(c=c?c.concat(g):g),null==e?(n(f,o,r),n(d,o,r),M(t.children,o,d,i,s,l,c,u)):h>0&&64&h&&p&&e.dynamicChildren?(P(e.dynamicChildren,p,o,i,s,l,c),(null!=t.key||i&&t===i.subTree)&&Ar(e,t,!0)):q(e,t,o,d,i,s,l,c,u)},D=(e,t,n,o,r,i,s,a,l)=>{t.slotScopeIds=a,null==e?512&t.shapeFlag?r.ctx.activate(t,n,o,s,l):B(t,n,o,r,i,s,l):F(e,t,l)},B=(e,t,n,o,r,i,s)=>{const a=e.component=function(e,t,n){const o=e.type,r=(t?t.appContext:e.appContext)||ii,i={uid:si++,vnode:e,type:o,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,scope:new De(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:hr(o,r),emitsOptions:$n(o,r),emit:null,emitted:null,propsDefaults:m,inheritAttrs:o.inheritAttrs,ctx:m,data:m,props:m,attrs:m,slots:m,refs:m,setupState:m,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,bda:null,da:null,ba:null,a:null,rtg:null,rtc:null,ec:null,sp:null};i.ctx={_:i},i.root=t?t.root:i,i.emit=Pn.bind(null,i),i.$pageInstance=t&&t.$pageInstance,e.ce&&e.ce(i);return i}(e,o,r);if(ho(e)&&(a.ctx.renderer=oe),function(e,t=!1){di=t;const{props:n,children:o}=e.vnode,r=fi(e);ur(e,n,r,t),((e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=Xt(t),G(t,"_",n)):wr(t,e.slots={})}else e.slots={},t&&xr(e,t);G(e.slots,Wr,1)})(e,o);const i=r?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=Kt(new Proxy(e.ctx,Jo));const{setup:o}=n;if(o){const n=e.setupContext=o.length>1?function(e){const t=t=>{e.exposed=t||{}};let n;return{get attrs(){return n||(n=function(e){return new Proxy(e.attrs,{get:(t,n)=>(Ze(e,0,"$attrs"),t[n])})}(e))},slots:e.slots,emit:e.emit,expose:t}}(e):null;ci(e),Ke();const r=fn(o,e,0,[e.props,n]);if(Ge(),ui(),L(r)){if(r.then(ui,ui),t)return r.then((n=>{hi(e,n,t)})).catch((t=>{hn(t,e,0)}));e.asyncDep=r}else hi(e,r,t)}else pi(e,t)}(e,t):void 0;di=!1}(a),a.asyncDep){if(r&&r.registerDep(a,R),!e.el){const e=a.subTree=Kr($r);_(null,e,t,n)}}else R(a,e,t,n,r,i,s)},F=(e,t,n)=>{const o=t.component=e.component;if(function(e,t,n){const{props:o,children:r,component:i}=e,{props:s,children:a,patchFlag:l}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!r&&!a||a&&a.$stable)||o!==s&&(o?!s||zn(o,s,c):!!s);if(1024&l)return!0;if(16&l)return o?zn(o,s,c):!!s;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(s[n]!==o[n]&&!Dn(c,n))return!0}}return!1}(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void N(o,t,n);o.next=t,function(e){const t=mn.indexOf(e);t>vn&&mn.splice(t,1)}(o.update),o.update()}else t.el=e.el,o.vnode=t},R=(e,t,n,o,r,i,s)=>{const a=()=>{if(e.isMounted){let t,{next:n,bu:o,u:a,parent:l,vnode:c}=e,u=n;Mr(e,!1),n?(n.el=c.el,N(e,n,s)):n=c,o&&K(o),(t=n.props&&n.props.onVnodeBeforeUpdate)&&ri(t,l,n,c),Mr(e,!0);const d=Nn(e),h=e.subTree;e.subTree=d,g(h,d,f(h.el),te(h),e,r,i),n.el=d.el,null===u&&function({vnode:e,parent:t},n){for(;t&&t.subTree===e;)(e=t.vnode).el=n,t=t.parent}(e,d.el),a&&Cr(a,r),(t=n.props&&n.props.onVnodeUpdated)&&Cr((()=>ri(t,l,n,c)),r)}else{let s;const{el:a,props:l}=t,{bm:c,m:u,parent:f}=e,d=co(t);if(Mr(e,!1),c&&K(c),!d&&(s=l&&l.onVnodeBeforeMount)&&ri(s,f,t),Mr(e,!0),a&&ie){const n=()=>{e.subTree=Nn(e),ie(a,e.subTree,e,r,null)};d?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{const s=e.subTree=Nn(e);g(null,s,n,o,e,r,i),t.el=s.el}if(u&&Cr(u,r),!d&&(s=l&&l.onVnodeMounted)){const e=t;Cr((()=>ri(s,f,e)),r)}const{ba:h,a:p}=e;(256&t.shapeFlag||f&&co(f.vnode)&&256&f.vnode.shapeFlag)&&(h&&Eo(h),p&&Cr(p,r),h&&Cr((()=>ko(h)),r)),e.isMounted=!0,t=n=o=null}},l=e.effect=new We(a,(()=>Sn(c)),e.scope),c=e.update=()=>l.run();c.id=e.uid,Mr(e,!0),c()},N=(e,t,n)=>{t.component=e;const o=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:s}}=e,a=Xt(r),[l]=e.propsOptions;let c=!1;if(!(o||s>0)||16&s){let o;fr(e,t,r,i)&&(c=!0);for(const i in a)t&&(k(t,i)||(o=W(i))!==i&&k(t,o))||(l?!n||void 0===n[i]&&void 0===n[o]||(r[i]=dr(l,a,i,void 0,e,!0)):delete r[i]);if(i!==a)for(const e in i)t&&k(t,e)||(delete i[e],c=!0)}else if(8&s){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let s=n[o];if(Dn(e.emitsOptions,s))continue;const u=t[s];if(l)if(k(i,s))u!==i[s]&&(i[s]=u,c=!0);else{const t=H(s);r[t]=dr(l,a,t,u,e,!1)}else u!==i[s]&&(i[s]=u,c=!0)}}c&&Qe(e,"set","$attrs")}(e,t.props,o,n),((e,t,n)=>{const{vnode:o,slots:r}=e;let i=!0,s=m;if(32&o.shapeFlag){const e=t._;e?n&&1===e?i=!1:(T(r,t),n||1!==e||delete r._):(i=!t.$stable,wr(t,r)),s=t}else t&&(xr(e,t),s={default:1});if(i)for(const a in r)yr(a)||a in s||delete r[a]})(e,t.children,n),Ke(),kn(),Ge()},q=(e,t,n,o,r,i,s,a,l=!1)=>{const c=e&&e.children,f=e?e.shapeFlag:0,d=t.children,{patchFlag:h,shapeFlag:p}=t;if(h>0){if(128&h)return void V(c,d,n,o,r,i,s,a,l);if(256&h)return void z(c,d,n,o,r,i,s,a,l)}8&p?(16&f&&ee(c,r,i),d!==c&&u(n,d)):16&f?16&p?V(c,d,n,o,r,i,s,a,l):ee(c,r,i,!0):(8&f&&u(n,""),16&p&&M(d,n,o,r,i,s,a,l))},z=(e,t,n,o,r,i,s,a,l)=>{t=t||v;const c=(e=e||v).length,u=t.length,f=Math.min(c,u);let d;for(d=0;d<f;d++){const o=t[d]=l?ti(t[d]):ei(t[d]);g(e[d],o,n,null,r,i,s,a,l)}c>u?ee(e,r,i,!0,!1,f):M(t,n,o,r,i,s,a,l,f)},V=(e,t,n,o,r,i,s,a,l)=>{let c=0;const u=t.length;let f=e.length-1,d=u-1;for(;c<=f&&c<=d;){const o=e[c],u=t[c]=l?ti(t[c]):ei(t[c]);if(!Vr(o,u))break;g(o,u,n,null,r,i,s,a,l),c++}for(;c<=f&&c<=d;){const o=e[f],c=t[d]=l?ti(t[d]):ei(t[d]);if(!Vr(o,c))break;g(o,c,n,null,r,i,s,a,l),f--,d--}if(c>f){if(c<=d){const e=d+1,f=e<u?t[e].el:o;for(;c<=d;)g(null,t[c]=l?ti(t[c]):ei(t[c]),n,f,r,i,s,a,l),c++}}else if(c>d)for(;c<=f;)Y(e[c],r,i,!0),c++;else{const h=c,p=c,m=new Map;for(c=p;c<=d;c++){const e=t[c]=l?ti(t[c]):ei(t[c]);null!=e.key&&m.set(e.key,c)}let y,b=0;const _=d-p+1;let w=!1,x=0;const T=new Array(_);for(c=0;c<_;c++)T[c]=0;for(c=h;c<=f;c++){const o=e[c];if(b>=_){Y(o,r,i,!0);continue}let u;if(null!=o.key)u=m.get(o.key);else for(y=p;y<=d;y++)if(0===T[y-p]&&Vr(o,t[y])){u=y;break}void 0===u?Y(o,r,i,!0):(T[u-p]=c+1,u>=x?x=u:w=!0,g(o,t[u],n,null,r,i,s,a,l),b++)}const S=w?function(e){const t=e.slice(),n=[0];let o,r,i,s,a;const l=e.length;for(o=0;o<l;o++){const l=e[o];if(0!==l){if(r=n[n.length-1],e[r]<l){t[o]=r,n.push(o);continue}for(i=0,s=n.length-1;i<s;)a=i+s>>1,e[n[a]]<l?i=a+1:s=a;l<e[n[i]]&&(i>0&&(t[o]=n[i-1]),n[i]=o)}}i=n.length,s=n[i-1];for(;i-- >0;)n[i]=s,s=t[s];return n}(T):v;for(y=S.length-1,c=_-1;c>=0;c--){const e=p+c,f=t[e],d=e+1<u?t[e+1].el:o;0===T[c]?g(null,f,n,d,r,i,s,a,l):w&&(y<0||c!==S[y]?U(f,n,d,2):y--)}}},U=(e,t,o,r,i=null)=>{const{el:s,type:a,transition:l,children:c,shapeFlag:u}=e;if(6&u)return void U(e.component.subTree,t,o,r);if(128&u)return void e.suspense.move(t,o,r);if(64&u)return void a.move(e,t,o,oe);if(a===Pr){n(s,t,o);for(let e=0;e<c.length;e++)U(c[e],t,o,r);return void n(e.anchor,t,o)}if(a===Dr)return void x(e,t,o);if(2!==r&&1&u&&l)if(0===r)l.beforeEnter(s),n(s,t,o),Cr((()=>l.enter(s)),i);else{const{leave:e,delayLeave:r,afterLeave:i}=l,a=()=>n(s,t,o),c=()=>{e(s,(()=>{a(),i&&i()}))};r?r(s,a,c):c()}else n(s,t,o)},Y=(e,t,n,o=!1,r=!1)=>{const{type:i,props:s,ref:a,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:f,dirs:d}=e;if(null!=a&&kr(a,null,n,e,!0),256&u)return void t.ctx.deactivate(e);const h=1&u&&d,p=!co(e);let g;if(p&&(g=s&&s.onVnodeBeforeUnmount)&&ri(g,t,e),6&u)Q(e.component,n,o);else{if(128&u)return void e.suspense.unmount(n,o);h&&jo(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,r,oe,o):c&&(i!==Pr||f>0&&64&f)?ee(c,t,n,!1,!0):(i===Pr&&384&f||!r&&16&u)&&ee(l,t,n),o&&X(e)}(p&&(g=s&&s.onVnodeUnmounted)||h)&&Cr((()=>{g&&ri(g,t,e),h&&jo(e,null,t,"unmounted")}),n)},X=e=>{const{type:t,el:n,anchor:r,transition:i}=e;if(t===Pr)return void Z(n,r);if(t===Dr)return void S(e);const s=()=>{o(n),i&&!i.persisted&&i.afterLeave&&i.afterLeave()};if(1&e.shapeFlag&&i&&!i.persisted){const{leave:t,delayLeave:o}=i,r=()=>t(n,s);o?o(e.el,s,r):r()}else s()},Z=(e,t)=>{let n;for(;e!==t;)n=d(e),o(e),e=n;o(t)},Q=(e,t,n)=>{const{bum:o,scope:r,update:i,subTree:s,um:a}=e;o&&K(o),r.stop(),i&&(i.active=!1,Y(s,e,t,n)),a&&Cr(a,t),Cr((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},ee=(e,t,n,o=!1,r=!1,i=0)=>{for(let s=i;s<e.length;s++)Y(e[s],t,n,o,r)},te=e=>6&e.shapeFlag?te(e.component.subTree):128&e.shapeFlag?e.suspense.next():d(e.anchor||e.el),ne=(e,t,n)=>{null==e?t._vnode&&Y(t._vnode,null,null,!0):g(t._vnode||null,e,t,null,null,null,n),kn(),Cn(),t._vnode=e},oe={p:g,um:Y,m:U,r:X,mt:B,mc:M,pc:q,pbc:P,n:te,o:e};let re,ie;t&&([re,ie]=t(oe));return{render:ne,hydrate:re,createApp:Er(ne,re)}}(e)}function Mr({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Ar(e,t,n=!1){const o=e.children,r=t.children;if(C(o)&&C(r))for(let i=0;i<o.length;i++){const e=o[i];let t=r[i];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=r[i]=ti(r[i]),t.el=e.el),n||Ar(e,t)),t.type===Ir&&(t.el=e.el)}}const Pr=Symbol(void 0),Ir=Symbol(void 0),$r=Symbol(void 0),Dr=Symbol(void 0),Lr=[];let Br=null;function Fr(e=!1){Lr.push(Br=e?null:[])}let Rr=1;function Nr(e){Rr+=e}function jr(e){return e.dynamicChildren=Rr>0?Br||v:null,Lr.pop(),Br=Lr[Lr.length-1]||null,Rr>0&&Br&&Br.push(e),e}function qr(e,t,n,o,r,i){return jr(Xr(e,t,n,o,r,i,!0))}function zr(e,t,n,o,r){return jr(Kr(e,t,n,o,r,!0))}function Hr(e){return!!e&&!0===e.__v_isVNode}function Vr(e,t){return e.type===t.type&&e.key===t.key}const Wr="__vInternal",Ur=({key:e})=>null!=e?e:null,Yr=({ref:e,ref_key:t,ref_for:n})=>null!=e?I(e)||en(e)||P(e)?{i:Ln,r:e,k:t,f:!!n}:e:null;function Xr(e,t=null,n=null,o=0,r=null,i=(e===Pr?0:1),s=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Ur(t),ref:t&&Yr(t),scopeId:Bn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Ln};return a?(ni(l,n),128&i&&e.normalize(l)):n&&(l.shapeFlag|=I(n)?8:16),Rr>0&&!s&&Br&&(l.patchFlag>0||6&i)&&32!==l.patchFlag&&Br.push(l),l}const Kr=function(e,t=null,n=null,r=0,i=null,s=!1){e&&e!==zo||(e=$r);if(Hr(e)){const o=Zr(e,t,!0);return n&&ni(o,n),Rr>0&&!s&&Br&&(6&o.shapeFlag?Br[Br.indexOf(e)]=o:Br.push(o)),o.patchFlag|=-2,o}a=e,P(a)&&"__vccOpts"in a&&(e=e.__vccOpts);var a;if(t){t=Gr(t);let{class:e,style:n}=t;e&&!I(e)&&(t.class=l(e)),D(n)&&(Yt(n)&&!C(n)&&(n=T({},n)),t.style=o(n))}const c=I(e)?1:Hn(e)?128:(e=>e.__isTeleport)(e)?64:D(e)?4:P(e)?2:0;return Xr(e,t,n,r,i,c,s,!0)};function Gr(e){return e?Yt(e)||Wr in e?T({},e):e:null}function Zr(e,t,n=!1){const{props:o,ref:r,patchFlag:i,children:s}=e,a=t?oi(o||{},t):o;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&Ur(a),ref:t&&t.ref?n&&r?C(r)?r.concat(Yr(t)):[r,Yr(t)]:Yr(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:s,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Pr?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Zr(e.ssContent),ssFallback:e.ssFallback&&Zr(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function Jr(e=" ",t=0){return Kr(Ir,null,e,t)}function Qr(e="",t=!1){return t?(Fr(),zr($r,null,e)):Kr($r,null,e)}function ei(e){return null==e||"boolean"==typeof e?Kr($r):C(e)?Kr(Pr,null,e.slice()):"object"==typeof e?ti(e):Kr(Ir,null,String(e))}function ti(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:Zr(e)}function ni(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(C(t))n=16;else if("object"==typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),ni(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||Wr in t?3===o&&Ln&&(1===Ln.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=Ln}}else P(t)?(t={default:t,_ctx:Ln},n=32):(t=String(t),64&o?(n=16,t=[Jr(t)]):n=8);e.children=t,e.shapeFlag|=n}function oi(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const e in r)if("class"===e)t.class!==r.class&&(t.class=l([t.class,r.class]));else if("style"===e)t.style=o([t.style,r.style]);else if(w(e)){const n=t[e],o=r[e];!o||n===o||C(n)&&n.includes(o)||(t[e]=n?[].concat(n,o):o)}else""!==e&&(t[e]=r[e])}return t}function ri(e,t,n,o=null){dn(e,t,7,[n,o])}const ii=Tr();let si=0;let ai=null;const li=()=>ai||Ln,ci=e=>{ai=e,e.scope.on()},ui=()=>{ai&&ai.scope.off(),ai=null};function fi(e){return 4&e.vnode.shapeFlag}let di=!1;function hi(e,t,n){P(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:D(t)&&(e.setupState=ln(t)),pi(e,n)}function pi(e,t,n){const o=e.type;e.render||(e.render=o.render||y),ci(e),Ke(),er(e),Ge(),ui()}function gi(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(ln(Kt(e.exposed)),{get:(t,n)=>n in t?t[n]:n in Go?Go[n](e):void 0,has:(e,t)=>t in e||t in Go}))}function mi(e,t=!0){return P(e)?e.displayName||e.name:e.name||t&&e.__name}const vi=(e,t)=>function(e,t,n=!1){let o,r;const i=P(e);return i?(o=e,r=y):(o=e.get,r=e.set),new un(o,r,i||!r,n)}(e,0,di);function yi(e,t,n){const o=arguments.length;return 2===o?D(t)&&!C(t)?Hr(t)?Kr(e,null,[t]):Kr(e,t):Kr(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&Hr(n)&&(n=[n]),Kr(e,t,n))}const bi=Symbol(""),_i=()=>Wn(bi),wi="3.2.47",xi="undefined"!=typeof document?document:null,Ti=xi&&xi.createElement("template"),Si={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r=t?xi.createElementNS("http://www.w3.org/2000/svg",e):xi.createElement(e,n?{is:n}:void 0);return"select"===e&&o&&null!=o.multiple&&r.setAttribute("multiple",o.multiple),r},createText:e=>xi.createTextNode(e),createComment:e=>xi.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>xi.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,i){const s=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),r!==i&&(r=r.nextSibling););else{Ti.innerHTML=o?`<svg>${e}</svg>`:e;const r=Ti.content;if(o){const e=r.firstChild;for(;e.firstChild;)r.appendChild(e.firstChild);r.removeChild(e)}t.insertBefore(r,n)}return[s?s.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}};const Ei=/\s*!important$/;function ki(e,t,n){if(C(n))n.forEach((n=>ki(e,t,n)));else if(null==n&&(n=""),n=Bi(n),t.startsWith("--"))e.setProperty(t,n);else{const o=function(e,t){const n=Oi[t];if(n)return n;let o=H(t);if("filter"!==o&&o in e)return Oi[t]=o;o=U(o);for(let r=0;r<Ci.length;r++){const n=Ci[r]+o;if(n in e)return Oi[t]=n}return t}(e,t);Ei.test(n)?e.setProperty(W(o),n.replace(Ei,""),"important"):e[o]=n}}const Ci=["Webkit","Moz","ms"],Oi={};const{unit:Mi,unitRatio:Ai,unitPrecision:Pi}={unit:"rem",unitRatio:10/320,unitPrecision:5},Ii=($i=Mi,Di=Ai,Li=Pi,e=>e.replace(ge,((e,t)=>{if(!t)return e;if(1===Di)return`${t}${$i}`;const n=function(e,t){const n=Math.pow(10,t+1),o=Math.floor(e*n);return 10*Math.round(o/10)/n}(parseFloat(t)*Di,Li);return 0===n?"0":`${n}${$i}`})));var $i,Di,Li;const Bi=e=>I(e)?Ii(e):e,Fi="http://www.w3.org/1999/xlink";function Ri(e,t,n,o){e.addEventListener(t,n,o)}function Ni(e,t,n,o,r=null){const i=e._vei||(e._vei={}),s=i[t];if(o&&s)s.value=o;else{const[n,a]=function(e){let t;if(ji.test(e)){let n;for(t={};n=e.match(ji);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[":"===e[2]?e.slice(3):W(e.slice(2)),t]}(t);if(o){const s=i[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();const o=t&&t.proxy,r=o&&o.$nne,{value:i}=n;if(r&&C(i)){const n=Hi(e,i);for(let o=0;o<n.length;o++){const i=n[o];dn(i,t,5,i.__wwe?[e]:r(e))}}else dn(Hi(e,i),t,5,r&&!i.__wwe?r(e,i,t):[e])};return n.value=e,n.attached=(()=>qi||(zi.then((()=>qi=0)),qi=Date.now()))(),n}(o,r);Ri(e,n,s,a)}else s&&(!function(e,t,n,o){e.removeEventListener(t,n,o)}(e,n,s,a),i[t]=void 0)}}const ji=/(?:Once|Passive|Capture)$/;let qi=0;const zi=Promise.resolve();function Hi(e,t){if(C(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>{const t=t=>!t._stopped&&e&&e(t);return t.__wwe=e.__wwe,t}))}return t}const Vi=/^on[a-z]/;const Wi="transition",Ui=(e,{slots:t})=>yi(to,function(e){const t={};for(const T in e)T in Yi||(t[T]=e[T]);if(!1===e.css)return t;const{name:n="v",type:o,duration:r,enterFromClass:i=`${n}-enter-from`,enterActiveClass:s=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=i,appearActiveClass:c=s,appearToClass:u=a,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:d=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,p=function(e){if(null==e)return null;if(D(e))return[Gi(e.enter),Gi(e.leave)];{const t=Gi(e);return[t,t]}}(r),g=p&&p[0],m=p&&p[1],{onBeforeEnter:v,onEnter:y,onEnterCancelled:b,onLeave:_,onLeaveCancelled:w,onBeforeAppear:x=v,onAppear:S=y,onAppearCancelled:E=b}=t,k=(e,t,n)=>{Ji(e,t?u:a),Ji(e,t?c:s),n&&n()},C=(e,t)=>{e._isLeaving=!1,Ji(e,f),Ji(e,h),Ji(e,d),t&&t()},O=e=>(t,n)=>{const r=e?S:y,s=()=>k(t,e,n);Xi(r,[t,s]),Qi((()=>{Ji(t,e?l:i),Zi(t,e?u:a),Ki(r)||ts(t,o,g,s)}))};return T(t,{onBeforeEnter(e){Xi(v,[e]),Zi(e,i),Zi(e,s)},onBeforeAppear(e){Xi(x,[e]),Zi(e,l),Zi(e,c)},onEnter:O(!1),onAppear:O(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>C(e,t);Zi(e,f),document.body.offsetHeight,Zi(e,d),Qi((()=>{e._isLeaving&&(Ji(e,f),Zi(e,h),Ki(_)||ts(e,o,m,n))})),Xi(_,[e,n])},onEnterCancelled(e){k(e,!1),Xi(b,[e])},onAppearCancelled(e){k(e,!0),Xi(E,[e])},onLeaveCancelled(e){C(e),Xi(w,[e])}})}(e),t);Ui.displayName="Transition";const Yi={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};Ui.props=T({},eo,Yi);const Xi=(e,t=[])=>{C(e)?e.forEach((e=>e(...t))):e&&e(...t)},Ki=e=>!!e&&(C(e)?e.some((e=>e.length>1)):e.length>1);function Gi(e){const t=(e=>{const t=I(e)?Number(e):NaN;return isNaN(t)?e:t})(e);return t}function Zi(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e._vtc||(e._vtc=new Set)).add(t)}function Ji(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const{_vtc:n}=e;n&&(n.delete(t),n.size||(e._vtc=void 0))}function Qi(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let es=0;function ts(e,t,n,o){const r=e._endId=++es,i=()=>{r===e._endId&&o()};if(n)return setTimeout(i,n);const{type:s,timeout:a,propCount:l}=function(e,t){const n=window.getComputedStyle(e),o=e=>(n[e]||"").split(", "),r=o("transitionDelay"),i=o("transitionDuration"),s=ns(r,i),a=o("animationDelay"),l=o("animationDuration"),c=ns(a,l);let u=null,f=0,d=0;t===Wi?s>0&&(u=Wi,f=s,d=i.length):"animation"===t?c>0&&(u="animation",f=c,d=l.length):(f=Math.max(s,c),u=f>0?s>c?Wi:"animation":null,d=u?u===Wi?i.length:l.length:0);const h=u===Wi&&/\b(transform|all)(,|$)/.test(o("transitionProperty").toString());return{type:u,timeout:f,propCount:d,hasTransform:h}}(e,t);if(!s)return o();const c=s+"end";let u=0;const f=()=>{e.removeEventListener(c,d),i()},d=t=>{t.target===e&&++u>=l&&f()};setTimeout((()=>{u<l&&f()}),a+1),e.addEventListener(c,d)}function ns(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>os(t)+os(e[n]))))}function os(e){return 1e3*Number(e.slice(0,-1).replace(",","."))}const rs=e=>{const t=e.props["onUpdate:modelValue"]||!1;return C(t)?e=>K(t,e):t};function is(e){e.target.composing=!0}function ss(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const as={created(e,{modifiers:{lazy:t,trim:n,number:o}},r){e._assign=rs(r);const i=o||r.props&&"number"===r.props.type;Ri(e,t?"change":"input",(t=>{if(t.target.composing)return;let o=e.value;n&&(o=o.trim()),i&&(o=Z(o)),e._assign(o)})),n&&Ri(e,"change",(()=>{e.value=e.value.trim()})),t||(Ri(e,"compositionstart",is),Ri(e,"compositionend",ss),Ri(e,"change",ss))},mounted(e,{value:t}){e.value=null==t?"":t},beforeUpdate(e,{value:t,modifiers:{lazy:n,trim:o,number:r}},i){if(e._assign=rs(i),e.composing)return;if(document.activeElement===e&&"range"!==e.type){if(n)return;if(o&&e.value.trim()===t)return;if((r||"number"===e.type)&&Z(e.value)===t)return}const s=null==t?"":t;e.value!==s&&(e.value=s)}},ls={deep:!0,created(e,t,n){e._assign=rs(n),Ri(e,"change",(()=>{const t=e._modelValue,n=hs(e),o=e.checked,r=e._assign;if(C(t)){const e=h(t,n),i=-1!==e;if(o&&!i)r(t.concat(n));else if(!o&&i){const n=[...t];n.splice(e,1),r(n)}}else if(M(t)){const e=new Set(t);o?e.add(n):e.delete(n),r(e)}else r(ps(e,o))}))},mounted:cs,beforeUpdate(e,t,n){e._assign=rs(n),cs(e,t,n)}};function cs(e,{value:t,oldValue:n},o){e._modelValue=t,C(t)?e.checked=h(t,o.props.value)>-1:M(t)?e.checked=t.has(o.props.value):t!==n&&(e.checked=d(t,ps(e,!0)))}const us={created(e,{value:t},n){e.checked=d(t,n.props.value),e._assign=rs(n),Ri(e,"change",(()=>{e._assign(hs(e))}))},beforeUpdate(e,{value:t,oldValue:n},o){e._assign=rs(o),t!==n&&(e.checked=d(t,o.props.value))}},fs={deep:!0,created(e,{value:t,modifiers:{number:n}},o){const r=M(t);Ri(e,"change",(()=>{const t=Array.prototype.filter.call(e.options,(e=>e.selected)).map((e=>n?Z(hs(e)):hs(e)));e._assign(e.multiple?r?new Set(t):t:t[0])})),e._assign=rs(o)},mounted(e,{value:t}){ds(e,t)},beforeUpdate(e,t,n){e._assign=rs(n)},updated(e,{value:t}){ds(e,t)}};function ds(e,t){const n=e.multiple;if(!n||C(t)||M(t)){for(let o=0,r=e.options.length;o<r;o++){const r=e.options[o],i=hs(r);if(n)C(t)?r.selected=h(t,i)>-1:r.selected=t.has(i);else if(d(hs(r),t))return void(e.selectedIndex!==o&&(e.selectedIndex=o))}n||-1===e.selectedIndex||(e.selectedIndex=-1)}}function hs(e){return"_value"in e?e._value:e.value}function ps(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const gs={created(e,t,n){ms(e,t,n,null,"created")},mounted(e,t,n){ms(e,t,n,null,"mounted")},beforeUpdate(e,t,n,o){ms(e,t,n,o,"beforeUpdate")},updated(e,t,n,o){ms(e,t,n,o,"updated")}};function ms(e,t,n,o,r){const i=function(e,t){switch(e){case"SELECT":return fs;case"TEXTAREA":return as;default:switch(t){case"checkbox":return ls;case"radio":return us;default:return as}}}(e.tagName,n.props&&n.props.type)[r];i&&i(e,t,n,o)}const vs=["ctrl","shift","alt","meta"],ys={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>vs.some((n=>e[`${n}Key`]&&!t.includes(n)))},bs=(e,t)=>(n,...o)=>{for(let e=0;e<t.length;e++){const o=ys[t[e]];if(o&&o(n,t))return}return e(n,...o)},_s={beforeMount(e,{value:t},{transition:n}){e._vod="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):ws(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),ws(e,!0),o.enter(e)):o.leave(e,(()=>{ws(e,!1)})):ws(e,t))},beforeUnmount(e,{value:t}){ws(e,t)}};function ws(e,t){e.style.display=t?e._vod:"none"}const xs=T({patchProp:(e,t,n,o,r=!1,i,s,a,l)=>{if(0===t.indexOf("change:"))return function(e,t,n,o=null){if(!n||!o)return;const r=t.replace("change:",""),{attrs:i}=o,s=i[r],a=(e.__wxsProps||(e.__wxsProps={}))[r];if(a===s)return;e.__wxsProps[r]=s;const l=o.proxy;Tn((()=>{n(s,a,l.$gcd(l,!0),l.$gcd(l,!1))}))}(e,t,o,s);"class"===t?function(e,t,n){const{__wxsAddClass:o,__wxsRemoveClass:r}=e;r&&r.length&&(t=(t||"").split(/\s+/).filter((e=>-1===r.indexOf(e))).join(" "),r.length=0),o&&o.length&&(t=(t||"")+" "+o.join(" "));const i=e._vtc;i&&(t=(t?[t,...i]:[...i]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,o,r):"style"===t?function(e,t,n){const o=e.style,r=I(n);if(n&&!r){if(t&&!I(t))for(const e in t)null==n[e]&&ki(o,e,"");for(const e in n)ki(o,e,n[e])}else{const i=o.display;r?t!==n&&(o.cssText=n):t&&e.removeAttribute("style"),"_vod"in e&&(o.display=i)}const{__wxsStyle:i}=e;if(i)for(const s in i)ki(o,s,i[s])}(e,n,o):w(t)?x(t)||Ni(e,t,0,o,s):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,o){if(o)return"innerHTML"===t||"textContent"===t||!!(t in e&&Vi.test(t)&&P(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if(Vi.test(t)&&I(n))return!1;return t in e}(e,t,o,r))?function(e,t,n,o,r,i,s){if("innerHTML"===t||"textContent"===t)return o&&s(o,r,i),void(e[t]=null==n?"":n);if("value"===t&&"PROGRESS"!==e.tagName&&!e.tagName.includes("-")){e._value=n;const o=null==n?"":n;return e.value===o&&"OPTION"!==e.tagName||(e.value=o),void(null==n&&e.removeAttribute(t))}let a=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=f(n):null==n&&"string"===o?(n="",a=!0):"number"===o&&(n=0,a=!0)}try{e[t]=n}catch(l){}a&&e.removeAttribute(t)}(e,t,o,i,s,a,l):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),function(e,t,n,o,r){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(Fi,t.slice(6,t.length)):e.setAttributeNS(Fi,t,n);else{const o=u(t);null==n||o&&!f(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}(e,t,o,r))},forcePatchProp:(e,t)=>0===t.indexOf("change:")||("class"===t&&e.__wxsClassChanged?(e.__wxsClassChanged=!1,!0):!("style"!==t||!e.__wxsStyleChanged)&&(e.__wxsStyleChanged=!1,!0))},Si);let Ts;const Ss=(...e)=>{const t=(Ts||(Ts=Or(xs))).createApp(...e),{mount:n}=t;return t.mount=e=>{const o=function(e){if(I(e)){return document.querySelector(e)}return e}(e);if(!o)return;const r=t._component;P(r)||r.render||r.template||(r.template=o.innerHTML),o.innerHTML="";const i=n(o,!1,o instanceof SVGElement);return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};const Es=["{","}"];const ks=/^(?:\d)+/,Cs=/^(?:\w)+/;const Os=Object.prototype.hasOwnProperty,Ms=(e,t)=>Os.call(e,t),As=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=Es){if(!t)return[e];let o=this._caches[e];return o||(o=function(e,[t,n]){const o=[];let r=0,i="";for(;r<e.length;){let s=e[r++];if(s===t){i&&o.push({type:"text",value:i}),i="";let t="";for(s=e[r++];void 0!==s&&s!==n;)t+=s,s=e[r++];const a=s===n,l=ks.test(t)?"list":a&&Cs.test(t)?"named":"unknown";o.push({value:t,type:l})}else i+=s}return i&&o.push({type:"text",value:i}),o}(e,n),this._caches[e]=o),function(e,t){const n=[];let o=0;const r=Array.isArray(t)?"list":(i=t,null!==i&&"object"==typeof i?"named":"unknown");var i;if("unknown"===r)return n;for(;o<e.length;){const i=e[o];switch(i.type){case"text":n.push(i.value);break;case"list":n.push(t[parseInt(i.value,10)]);break;case"named":"named"===r&&n.push(t[i.value])}o++}return n}(o,t)}};function Ps(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1?"zh-Hant":(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?"zh-Hant":"zh-Hans");var n;let o=["en","fr","es"];t&&Object.keys(t).length>0&&(o=Object.keys(t));const r=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,o);return r||void 0}class Is{constructor({locale:e,fallbackLocale:t,messages:n,watcher:o,formater:r}){this.locale="en",this.fallbackLocale="en",this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=r||As,this.messages=n||{},this.setLocale(e||"en"),o&&this.watchLocale(o)}setLocale(e){const t=this.locale;this.locale=Ps(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const o=this.messages[e];o?n?Object.assign(o,t):Object.keys(t).forEach((e=>{Ms(o,e)||(o[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let o=this.message;return"string"==typeof t?(t=Ps(t,this.messages))&&(o=this.messages[t]):n=t,Ms(o,e)?this.formater.interpolate(o[e],n).join(""):(console.warn(`Cannot translate the value of keypath ${e}. Use the value of keypath as default.`),e)}}function $s(e,t={},n,o){"string"!=typeof e&&([e,t]=[t,e]),"string"!=typeof e&&(e="undefined"!=typeof uni&&vd?vd():"undefined"!=typeof global&&global.getLocale?global.getLocale():"en"),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||"en");const r=new Is({locale:e,fallbackLocale:n,messages:t,watcher:o});let i=(e,t)=>{{let e=!1;i=function(t,n){const o=Og().$vm;return o&&(o.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(o,r))),r.t(t,n)}}return i(e,t)};return{i18n:r,f:(e,t,n)=>r.f(e,t,n),t:(e,t)=>i(e,t),add:(e,t,n=!0)=>r.add(e,t,n),watch:e=>r.watchLocale(e),getLocale:()=>r.getLocale(),setLocale:e=>r.setLocale(e)}}function Ds(e,t){return e.indexOf(t[0])>-1}
/*!
  * vue-router v4.1.6
  * (c) 2022 Eduardo San Martin Morote
  * @license MIT
  */const Ls="undefined"!=typeof window;const Bs=Object.assign;function Fs(e,t){const n={};for(const o in t){const r=t[o];n[o]=Ns(r)?r.map(e):e(r)}return n}const Rs=()=>{},Ns=Array.isArray,js=/\/$/;function qs(e,t,n="/"){let o,r={},i="",s="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(o=t.slice(0,l),i=t.slice(l+1,a>-1?a:t.length),r=e(i)),a>-1&&(o=o||t.slice(0,a),s=t.slice(a,t.length)),o=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/");let r,i,s=n.length-1;for(r=0;r<o.length;r++)if(i=o[r],"."!==i){if(".."!==i)break;s>1&&s--}return n.slice(0,s).join("/")+"/"+o.slice(r-(r===o.length?1:0)).join("/")}(null!=o?o:t,n),{fullPath:o+(i&&"?")+i+s,path:o,query:r,hash:s}}function zs(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function Hs(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Vs(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Ws(e[n],t[n]))return!1;return!0}function Ws(e,t){return Ns(e)?Us(e,t):Ns(t)?Us(t,e):e===t}function Us(e,t){return Ns(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}var Ys,Xs,Ks,Gs;function Zs(e){if(!e)if(Ls){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(js,"")}(Xs=Ys||(Ys={})).pop="pop",Xs.push="push",(Gs=Ks||(Ks={})).back="back",Gs.forward="forward",Gs.unknown="";const Js=/^[^#]+#/;function Qs(e,t){return e.replace(Js,"#")+t}const ea=()=>({left:window.pageXOffset,top:window.pageYOffset});function ta(e){let t;if("el"in e){const n=e.el,o="string"==typeof n&&n.startsWith("#"),r="string"==typeof n?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.pageXOffset,null!=t.top?t.top:window.pageYOffset)}function na(e,t){return(history.state?history.state.position-t:-1)+e}const oa=new Map;function ra(e,t){const{pathname:n,search:o,hash:r}=t,i=e.indexOf("#");if(i>-1){let t=r.includes(e.slice(i))?e.slice(i).length:1,n=r.slice(t);return"/"!==n[0]&&(n="/"+n),zs(n,"")}return zs(n,e)+o+r}function ia(e,t,n,o=!1,r=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:r?ea():null}}function sa(e){const{history:t,location:n}=window,o={value:ra(e,n)},r={value:t.state};function i(o,i,s){const a=e.indexOf("#"),l=a>-1?(n.host&&document.querySelector("base")?e:e.slice(a))+o:location.protocol+"//"+location.host+e+o;try{t[s?"replaceState":"pushState"](i,"",l),r.value=i}catch(c){console.error(c),n[s?"replace":"assign"](l)}}return r.value||i(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:o,state:r,push:function(e,n){const s=Bs({},r.value,t.state,{forward:e,scroll:ea()});i(s.current,s,!0),i(e,Bs({},ia(o.value,e,null),{position:s.position+1},n),!1),o.value=e},replace:function(e,n){i(e,Bs({},t.state,ia(r.value.back,e,r.value.forward,!0),n,{position:r.value.position}),!0),o.value=e}}}function aa(e){const t=sa(e=Zs(e)),n=function(e,t,n,o){let r=[],i=[],s=null;const a=({state:i})=>{const a=ra(e,location),l=n.value,c=t.value;let u=0;if(i){if(n.value=a,t.value=i,s&&s===l)return void(s=null);u=c?i.position-c.position:0}else o(a);r.forEach((e=>{e(n.value,l,{delta:u,type:Ys.pop,direction:u?u>0?Ks.forward:Ks.back:Ks.unknown})}))};function l(){const{history:e}=window;e.state&&e.replaceState(Bs({},e.state,{scroll:ea()}),"")}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",l),{pauseListeners:function(){s=n.value},listen:function(e){r.push(e);const t=()=>{const t=r.indexOf(e);t>-1&&r.splice(t,1)};return i.push(t),t},destroy:function(){for(const e of i)e();i=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace);const o=Bs({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:Qs.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function la(e){return"string"==typeof e||"symbol"==typeof e}const ca={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},ua=Symbol("");var fa,da;function ha(e,t){return Bs(new Error,{type:e,[ua]:!0},t)}function pa(e,t){return e instanceof Error&&ua in e&&(null==t||!!(e.type&t))}(da=fa||(fa={}))[da.aborted=4]="aborted",da[da.cancelled=8]="cancelled",da[da.duplicated=16]="duplicated";const ga={sensitive:!1,strict:!1,start:!0,end:!0},ma=/[.+*?^${}()[\]/\\]/g;function va(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function ya(e,t){let n=0;const o=e.score,r=t.score;for(;n<o.length&&n<r.length;){const e=va(o[n],r[n]);if(e)return e;n++}if(1===Math.abs(r.length-o.length)){if(ba(o))return 1;if(ba(r))return-1}return r.length-o.length}function ba(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const _a={type:0,value:""},wa=/[a-zA-Z0-9_]/;function xa(e,t,n){const o=function(e,t){const n=Bs({},ga,t),o=[];let r=n.start?"^":"";const i=[];for(const l of e){const e=l.length?[]:[90];n.strict&&!l.length&&(r+="/");for(let t=0;t<l.length;t++){const o=l[t];let s=40+(n.sensitive?.25:0);if(0===o.type)t||(r+="/"),r+=o.value.replace(ma,"\\$&"),s+=40;else if(1===o.type){const{value:e,repeatable:n,optional:c,regexp:u}=o;i.push({name:e,repeatable:n,optional:c});const f=u||"[^/]+?";if("[^/]+?"!==f){s+=10;try{new RegExp(`(${f})`)}catch(a){throw new Error(`Invalid custom RegExp for param "${e}" (${f}): `+a.message)}}let d=n?`((?:${f})(?:/(?:${f}))*)`:`(${f})`;t||(d=c&&l.length<2?`(?:/${d})`:"/"+d),c&&(d+="?"),r+=d,s+=20,c&&(s+=-8),n&&(s+=-20),".*"===f&&(s+=-50)}e.push(s)}o.push(e)}if(n.strict&&n.end){const e=o.length-1;o[e][o[e].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&(r+="(?:/|$)");const s=new RegExp(r,n.sensitive?"":"i");return{re:s,score:o,keys:i,parse:function(e){const t=e.match(s),n={};if(!t)return null;for(let o=1;o<t.length;o++){const e=t[o]||"",r=i[o-1];n[r.name]=e&&r.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",o=!1;for(const r of e){o&&n.endsWith("/")||(n+="/"),o=!1;for(const e of r)if(0===e.type)n+=e.value;else if(1===e.type){const{value:i,repeatable:s,optional:a}=e,l=i in t?t[i]:"";if(Ns(l)&&!s)throw new Error(`Provided param "${i}" is an array but it is not repeatable (* or + modifiers)`);const c=Ns(l)?l.join("/"):l;if(!c){if(!a)throw new Error(`Missing required param "${i}"`);r.length<2&&(n.endsWith("/")?n=n.slice(0,-1):o=!0)}n+=c}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[_a]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,o=n;const r=[];let i;function s(){i&&r.push(i),i=[]}let a,l=0,c="",u="";function f(){c&&(0===n?i.push({type:0,value:c}):1===n||2===n||3===n?(i.length>1&&("*"===a||"+"===a)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:c,regexp:u,repeatable:"*"===a||"+"===a,optional:"*"===a||"?"===a})):t("Invalid state to consume buffer"),c="")}function d(){c+=a}for(;l<e.length;)if(a=e[l++],"\\"!==a||2===n)switch(n){case 0:"/"===a?(c&&f(),s()):":"===a?(f(),n=1):d();break;case 4:d(),n=o;break;case 1:"("===a?n=2:wa.test(a)?d():(f(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--);break;case 2:")"===a?"\\"==u[u.length-1]?u=u.slice(0,-1)+a:n=3:u+=a;break;case 3:f(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--,u="";break;default:t("Unknown state")}else o=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),f(),s(),r}(e.path),n),r=Bs(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function Ta(e,t){const n=[],o=new Map;function r(e,n,o){const a=!o,l=function(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:Ea(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}(e);l.aliasOf=o&&o.record;const c=Oa(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(Bs({},l,{components:o?o.record.components:l.components,path:e,aliasOf:o?o.record:l}))}let f,d;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,o="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&o+u)}if(f=xa(t,n,c),o?o.alias.push(f):(d=d||f,d!==f&&d.alias.push(f),a&&e.name&&!ka(f)&&i(e.name)),l.children){const e=l.children;for(let t=0;t<e.length;t++)r(e[t],f,o&&o.children[t])}o=o||f,(f.record.components&&Object.keys(f.record.components).length||f.record.name||f.record.redirect)&&s(f)}return d?()=>{i(d)}:Rs}function i(e){if(la(e)){const t=o.get(e);t&&(o.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(i),t.alias.forEach(i))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&o.delete(e.record.name),e.children.forEach(i),e.alias.forEach(i))}}function s(e){let t=0;for(;t<n.length&&ya(e,n[t])>=0&&(e.record.path!==n[t].record.path||!Ma(e,n[t]));)t++;n.splice(t,0,e),e.record.name&&!ka(e)&&o.set(e.record.name,e)}return t=Oa({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>r(e))),{addRoute:r,resolve:function(e,t){let r,i,s,a={};if("name"in e&&e.name){if(r=o.get(e.name),!r)throw ha(1,{location:e});s=r.record.name,a=Bs(Sa(t.params,r.keys.filter((e=>!e.optional)).map((e=>e.name))),e.params&&Sa(e.params,r.keys.map((e=>e.name)))),i=r.stringify(a)}else if("path"in e)i=e.path,r=n.find((e=>e.re.test(i))),r&&(a=r.parse(i),s=r.record.name);else{if(r=t.name?o.get(t.name):n.find((e=>e.re.test(t.path))),!r)throw ha(1,{location:e,currentLocation:t});s=r.record.name,a=Bs({},t.params,e.params),i=r.stringify(a)}const l=[];let c=r;for(;c;)l.unshift(c.record),c=c.parent;return{name:s,path:i,params:a,matched:l,meta:Ca(l)}},removeRoute:i,getRoutes:function(){return n},getRecordMatcher:function(e){return o.get(e)}}}function Sa(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function Ea(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]="boolean"==typeof n?n:n[o];return t}function ka(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Ca(e){return e.reduce(((e,t)=>Bs(e,t.meta)),{})}function Oa(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function Ma(e,t){return t.children.some((t=>t===e||Ma(e,t)))}const Aa=/#/g,Pa=/&/g,Ia=/\//g,$a=/=/g,Da=/\?/g,La=/\+/g,Ba=/%5B/g,Fa=/%5D/g,Ra=/%5E/g,Na=/%60/g,ja=/%7B/g,qa=/%7C/g,za=/%7D/g,Ha=/%20/g;function Va(e){return encodeURI(""+e).replace(qa,"|").replace(Ba,"[").replace(Fa,"]")}function Wa(e){return Va(e).replace(La,"%2B").replace(Ha,"+").replace(Aa,"%23").replace(Pa,"%26").replace(Na,"`").replace(ja,"{").replace(za,"}").replace(Ra,"^")}function Ua(e){return null==e?"":function(e){return Va(e).replace(Aa,"%23").replace(Da,"%3F")}(e).replace(Ia,"%2F")}function Ya(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function Xa(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(La," "),r=e.indexOf("="),i=Ya(r<0?e:e.slice(0,r)),s=r<0?null:Ya(e.slice(r+1));if(i in t){let e=t[i];Ns(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function Ka(e){let t="";for(let n in e){const o=e[n];if(n=Wa(n).replace($a,"%3D"),null==o){void 0!==o&&(t+=(t.length?"&":"")+n);continue}(Ns(o)?o.map((e=>e&&Wa(e))):[o&&Wa(o)]).forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function Ga(e){const t={};for(const n in e){const o=e[n];void 0!==o&&(t[n]=Ns(o)?o.map((e=>null==e?null:""+e)):null==o?o:""+o)}return t}const Za=Symbol(""),Ja=Symbol(""),Qa=Symbol(""),el=Symbol(""),tl=Symbol("");function nl(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e,reset:function(){e=[]}}}function ol(e,t,n,o,r){const i=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise(((s,a)=>{const l=e=>{var l;!1===e?a(ha(4,{from:n,to:t})):e instanceof Error?a(e):"string"==typeof(l=e)||l&&"object"==typeof l?a(ha(2,{from:t,to:e})):(i&&o.enterCallbacks[r]===i&&"function"==typeof e&&i.push(e),s())},c=e.call(o&&o.instances[r],t,n,l);let u=Promise.resolve(c);e.length<3&&(u=u.then(l)),u.catch((e=>a(e)))}))}function rl(e,t,n,o){const r=[];for(const s of e)for(const e in s.components){let a=s.components[e];if("beforeRouteEnter"===t||s.instances[e])if("object"==typeof(i=a)||"displayName"in i||"props"in i||"__vccOpts"in i){const i=(a.__vccOpts||a)[t];i&&r.push(ol(i,n,o,s,e))}else{let i=a();r.push((()=>i.then((r=>{if(!r)return Promise.reject(new Error(`Couldn't resolve component "${e}" at "${s.path}"`));const i=(a=r).__esModule||"Module"===a[Symbol.toStringTag]?r.default:r;var a;s.components[e]=i;const l=(i.__vccOpts||i)[t];return l&&ol(l,n,o,s,e)()}))))}}var i;return r}function il(e){const t=Wn(Qa),n=Wn(el),o=vi((()=>t.resolve(sn(e.to)))),r=vi((()=>{const{matched:e}=o.value,{length:t}=e,r=e[t-1],i=n.matched;if(!r||!i.length)return-1;const s=i.findIndex(Hs.bind(null,r));if(s>-1)return s;const a=ll(e[t-2]);return t>1&&ll(r)===a&&i[i.length-1].path!==a?i.findIndex(Hs.bind(null,e[t-2])):s})),i=vi((()=>r.value>-1&&function(e,t){for(const n in t){const o=t[n],r=e[n];if("string"==typeof o){if(o!==r)return!1}else if(!Ns(r)||r.length!==o.length||o.some(((e,t)=>e!==r[t])))return!1}return!0}(n.params,o.value.params))),s=vi((()=>r.value>-1&&r.value===n.matched.length-1&&Vs(n.params,o.value.params)));return{route:o,href:vi((()=>o.value.href)),isActive:i,isExactActive:s,navigate:function(n={}){return function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)?t[sn(e.replace)?"replace":"push"](sn(e.to)).catch(Rs):Promise.resolve()}}}const sl=lo({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:il,setup(e,{slots:t}){const n=qt(il(e)),{options:o}=Wn(Qa),r=vi((()=>({[cl(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[cl(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const o=t.default&&t.default(n);return e.custom?o:yi("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),al=sl;function ll(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const cl=(e,t,n)=>null!=e?e:null!=t?t:n;function ul(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const fl=lo({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=Wn(tl),r=vi((()=>e.route||o.value)),i=Wn(Ja,0),s=vi((()=>{let e=sn(i);const{matched:t}=r.value;let n;for(;(n=t[e])&&!n.components;)e++;return e})),a=vi((()=>r.value.matched[s.value]));Vn(Ja,vi((()=>s.value+1))),Vn(Za,a),Vn(tl,r);const l=tn();return Xn((()=>[l.value,a.value,e.name]),(([e,t,n],[o,r,i])=>{t&&(t.instances[n]=e,r&&r!==t&&e&&e===o&&(t.leaveGuards.size||(t.leaveGuards=r.leaveGuards),t.updateGuards.size||(t.updateGuards=r.updateGuards))),!e||!t||r&&Hs(t,r)&&o||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=r.value,i=e.name,s=a.value,c=s&&s.components[i];if(!c)return ul(n.default,{Component:c,route:o});const u=s.props[i],f=u?!0===u?o.params:"function"==typeof u?u(o):u:null,d=yi(c,Bs({},f,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(s.instances[i]=null)},ref:l}));return ul(n.default,{Component:d,route:o})||d}}});function dl(e){const t=Ta(e.routes,e),n=e.parseQuery||Xa,o=e.stringifyQuery||Ka,r=e.history,i=nl(),s=nl(),a=nl(),l=nn(ca);let c=ca;Ls&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Fs.bind(null,(e=>""+e)),f=Fs.bind(null,Ua),d=Fs.bind(null,Ya);function h(e,i){if(i=Bs({},i||l.value),"string"==typeof e){const o=qs(n,e,i.path),s=t.resolve({path:o.path},i),a=r.createHref(o.fullPath);return Bs(o,s,{params:d(s.params),hash:Ya(o.hash),redirectedFrom:void 0,href:a})}let s;if("path"in e)s=Bs({},e,{path:qs(n,e.path,i.path).path});else{const t=Bs({},e.params);for(const e in t)null==t[e]&&delete t[e];s=Bs({},e,{params:f(e.params)}),i.params=f(i.params)}const a=t.resolve(s,i),c=e.hash||"";a.params=u(d(a.params));const h=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(o,Bs({},e,{hash:(p=c,Va(p).replace(ja,"{").replace(za,"}").replace(Ra,"^")),path:a.path}));var p;const g=r.createHref(h);return Bs({fullPath:h,hash:c,query:o===Ka?Ga(e.query):e.query||{}},a,{redirectedFrom:void 0,href:g})}function p(e){return"string"==typeof e?qs(n,e,l.value.path):Bs({},e)}function g(e,t){if(c!==e)return ha(8,{from:t,to:e})}function m(e){return y(e)}function v(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let o="function"==typeof n?n(e):n;return"string"==typeof o&&(o=o.includes("?")||o.includes("#")?o=p(o):{path:o},o.params={}),Bs({query:e.query,hash:e.hash,params:"path"in o?{}:e.params},o)}}function y(e,t){const n=c=h(e),r=l.value,i=e.state,s=e.force,a=!0===e.replace,u=v(n);if(u)return y(Bs(p(u),{state:"object"==typeof u?Bs({},i,u.state):i,force:s,replace:a}),t||n);const f=n;let d;return f.redirectedFrom=t,!s&&function(e,t,n){const o=t.matched.length-1,r=n.matched.length-1;return o>-1&&o===r&&Hs(t.matched[o],n.matched[r])&&Vs(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(o,r,n)&&(d=ha(16,{to:f,from:r}),A(r,r,!0,!1)),(d?Promise.resolve(d):_(f,r)).catch((e=>pa(e)?pa(e,2)?e:M(e):O(e,f,r))).then((e=>{if(e){if(pa(e,2))return y(Bs({replace:a},p(e.to),{state:"object"==typeof e.to?Bs({},i,e.to.state):i,force:s}),t||f)}else e=x(f,r,!0,a,i);return w(f,r,e),e}))}function b(e,t){const n=g(e,t);return n?Promise.reject(n):Promise.resolve()}function _(e,t){let n;const[o,r,a]=function(e,t){const n=[],o=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let s=0;s<i;s++){const i=t.matched[s];i&&(e.matched.find((e=>Hs(e,i)))?o.push(i):n.push(i));const a=e.matched[s];a&&(t.matched.find((e=>Hs(e,a)))||r.push(a))}return[n,o,r]}(e,t);n=rl(o.reverse(),"beforeRouteLeave",e,t);for(const i of o)i.leaveGuards.forEach((o=>{n.push(ol(o,e,t))}));const l=b.bind(null,e,t);return n.push(l),hl(n).then((()=>{n=[];for(const o of i.list())n.push(ol(o,e,t));return n.push(l),hl(n)})).then((()=>{n=rl(r,"beforeRouteUpdate",e,t);for(const o of r)o.updateGuards.forEach((o=>{n.push(ol(o,e,t))}));return n.push(l),hl(n)})).then((()=>{n=[];for(const o of e.matched)if(o.beforeEnter&&!t.matched.includes(o))if(Ns(o.beforeEnter))for(const r of o.beforeEnter)n.push(ol(r,e,t));else n.push(ol(o.beforeEnter,e,t));return n.push(l),hl(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=rl(a,"beforeRouteEnter",e,t),n.push(l),hl(n)))).then((()=>{n=[];for(const o of s.list())n.push(ol(o,e,t));return n.push(l),hl(n)})).catch((e=>pa(e,8)?e:Promise.reject(e)))}function w(e,t,n){for(const o of a.list())o(e,t,n)}function x(e,t,n,o,i){const s=g(e,t);if(s)return s;const a=t===ca,c=Ls?history.state:{};n&&(o||a?r.replace(e.fullPath,Bs({scroll:a&&c&&c.scroll},i)):r.push(e.fullPath,i)),l.value=e,A(e,t,n,a),M()}let T;function S(){T||(T=r.listen(((e,t,n)=>{if(!D.listening)return;const o=h(e),i=v(o);if(i)return void y(Bs(i,{replace:!0}),o).catch(Rs);c=o;const s=l.value;var a,u;Ls&&(a=na(s.fullPath,n.delta),u=ea(),oa.set(a,u)),_(o,s).catch((e=>pa(e,12)?e:pa(e,2)?(y(e.to,o).then((e=>{pa(e,20)&&!n.delta&&n.type===Ys.pop&&r.go(-1,!1)})).catch(Rs),Promise.reject()):(n.delta&&r.go(-n.delta,!1),O(e,o,s)))).then((e=>{(e=e||x(o,s,!1))&&(n.delta&&!pa(e,8)?r.go(-n.delta,!1):n.type===Ys.pop&&pa(e,20)&&r.go(-1,!1)),w(o,s,e)})).catch(Rs)})))}let E,k=nl(),C=nl();function O(e,t,n){M(e);const o=C.list();return o.length?o.forEach((o=>o(e,t,n))):console.error(e),Promise.reject(e)}function M(e){return E||(E=!e,S(),k.list().forEach((([t,n])=>e?n(e):t())),k.reset()),e}function A(t,n,o,r){const{scrollBehavior:i}=e;if(!Ls||!i)return Promise.resolve();const s=!o&&function(e){const t=oa.get(e);return oa.delete(e),t}(na(t.fullPath,0))||(r||!o)&&history.state&&history.state.scroll||null;return Tn().then((()=>i(t,n,s))).then((e=>e&&ta(e))).catch((e=>O(e,t,n)))}const P=e=>r.go(e);let I;const $=new Set,D={currentRoute:l,listening:!0,addRoute:function(e,n){let o,r;return la(e)?(o=t.getRecordMatcher(e),r=n):r=e,t.addRoute(r,o)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map((e=>e.record))},resolve:h,options:e,push:m,replace:function(e){return m(Bs(p(e),{replace:!0}))},go:P,back:()=>P(-1),forward:()=>P(1),beforeEach:i.add,beforeResolve:s.add,afterEach:a.add,onError:C.add,isReady:function(){return E&&l.value!==ca?Promise.resolve():new Promise(((e,t)=>{k.add([e,t])}))},install(e){e.component("RouterLink",al),e.component("RouterView",fl),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>sn(l)}),Ls&&!I&&l.value===ca&&(I=!0,m(r.location).catch((e=>{})));const t={};for(const o in ca)t[o]=vi((()=>l.value[o]));e.provide(Qa,this),e.provide(el,qt(t)),e.provide(tl,l);const n=e.unmount;$.add(e),e.unmount=function(){$.delete(e),$.size<1&&(c=ca,T&&T(),T=null,l.value=ca,I=!1,E=!1),n()}}};return D}function hl(e){return e.reduce(((e,t)=>e.then((()=>t()))),Promise.resolve())}function pl(){return Wn(el)}const gl=ce((()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length));let ml;function vl(e){return Ds(e,ee)?_l().f(e,function(){const e=vd(),t=__uniConfig.locales;return t[e]||t[__uniConfig.fallbackLocale]||t.en||{}}(),ee):e}function yl(e,t){if(1===t.length){if(e){const n=e=>I(e)&&Ds(e,ee),o=t[0];let r=[];if(C(e)&&(r=e.filter((e=>n(e[o])))).length)return r;const i=e[t[0]];if(n(i))return e}return}const n=t.shift();return yl(e&&e[n],t)}function bl(e,t){const n=yl(e,t);if(!n)return!1;const o=t[t.length-1];if(C(n))n.forEach((e=>bl(e,[o])));else{let e=n[o];Object.defineProperty(n,o,{get:()=>vl(e),set(t){e=t}})}return!0}function _l(){if(!ml){let e;if(e=navigator.cookieEnabled&&window.localStorage&&localStorage.UNI_LOCALE||__uniConfig.locale||navigator.language,ml=$s(e),gl()){const t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach((e=>ml.add(e,__uniConfig.locales[e]))),ml.setLocale(e)}}return ml}function wl(e,t,n){return t.reduce(((t,o,r)=>(t[e+o]=n[r],t)),{})}const xl=ce((()=>{const e="uni.async.",t=["error"];_l().add("en",wl(e,t,["The connection timed out, click the screen to try again."]),!1),_l().add("es",wl(e,t,["Se agotó el tiempo de conexión, haga clic en la pantalla para volver a intentarlo."]),!1),_l().add("fr",wl(e,t,["La connexion a expiré, cliquez sur l'écran pour réessayer."]),!1),_l().add("zh-Hans",wl(e,t,["连接服务器超时，点击屏幕重试"]),!1),_l().add("zh-Hant",wl(e,t,["連接服務器超時，點擊屏幕重試"]),!1)})),Tl=ce((()=>{const e="uni.showToast.",t=["unpaired"];_l().add("en",wl(e,t,["Please note showToast must be paired with hideToast"]),!1),_l().add("es",wl(e,t,["Tenga en cuenta que showToast debe estar emparejado con hideToast"]),!1),_l().add("fr",wl(e,t,["Veuillez noter que showToast doit être associé à hideToast"]),!1),_l().add("zh-Hans",wl(e,t,["请注意 showToast 与 hideToast 必须配对使用"]),!1),_l().add("zh-Hant",wl(e,t,["請注意 showToast 與 hideToast 必須配對使用"]),!1)})),Sl=ce((()=>{const e="uni.showLoading.",t=["unpaired"];_l().add("en",wl(e,t,["Please note showLoading must be paired with hideLoading"]),!1),_l().add("es",wl(e,t,["Tenga en cuenta que showLoading debe estar emparejado con hideLoading"]),!1),_l().add("fr",wl(e,t,["Veuillez noter que showLoading doit être associé à hideLoading"]),!1),_l().add("zh-Hans",wl(e,t,["请注意 showLoading 与 hideLoading 必须配对使用"]),!1),_l().add("zh-Hant",wl(e,t,["請注意 showLoading 與 hideLoading 必須配對使用"]),!1)})),El=ce((()=>{const e="uni.showModal.",t=["cancel","confirm"];_l().add("en",wl(e,t,["Cancel","OK"]),!1),_l().add("es",wl(e,t,["Cancelar","OK"]),!1),_l().add("fr",wl(e,t,["Annuler","OK"]),!1),_l().add("zh-Hans",wl(e,t,["取消","确定"]),!1),_l().add("zh-Hant",wl(e,t,["取消","確定"]),!1)})),kl=ce((()=>{const e="uni.chooseFile.",t=["notUserActivation"];_l().add("en",wl(e,t,["File chooser dialog can only be shown with a user activation"]),!1),_l().add("es",wl(e,t,["El cuadro de diálogo del selector de archivos solo se puede mostrar con la activación del usuario"]),!1),_l().add("fr",wl(e,t,["La boîte de dialogue du sélecteur de fichier ne peut être affichée qu'avec une activation par l'utilisateur"]),!1),_l().add("zh-Hans",wl(e,t,["文件选择器对话框只能在由用户激活时显示"]),!1),_l().add("zh-Hant",wl(e,t,["文件選擇器對話框只能在由用戶激活時顯示"]),!1)})),Cl=ce((()=>{const e="uni.picker.",t=["done","cancel"];_l().add("en",wl(e,t,["Done","Cancel"]),!1),_l().add("es",wl(e,t,["OK","Cancelar"]),!1),_l().add("fr",wl(e,t,["OK","Annuler"]),!1),_l().add("zh-Hans",wl(e,t,["完成","取消"]),!1),_l().add("zh-Hant",wl(e,t,["完成","取消"]),!1)})),Ol=ce((()=>{const e="uni.video.",t=["danmu","volume"];_l().add("en",wl(e,t,["Danmu","Volume"]),!1),_l().add("es",wl(e,t,["Danmu","Volumen"]),!1),_l().add("fr",wl(e,t,["Danmu","Le Volume"]),!1),_l().add("zh-Hans",wl(e,t,["弹幕","音量"]),!1),_l().add("zh-Hant",wl(e,t,["彈幕","音量"]),!1)}));function Ml(e){const t=new Ae;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit:(e,...n)=>t.emit(e,...n),subscribe(n,o,r=!1){t[r?"once":"on"](`${e}.${n}`,o)},unsubscribe(n,o){t.off(`${e}.${n}`,o)},subscribeHandler(n,o,r){t.emit(`${e}.${n}`,o,r)}}}let Al=1;const Pl=Object.create(null);function Il(e,t){return e+"."+t}function $l(e,t,n){t=Il(e,t),Pl[t]||(Pl[t]=n)}function Dl({id:e,name:t,args:n},o){t=Il(o,t);const r=t=>{e&&Av.publishHandler("invokeViewApi."+e,t)},i=Pl[t];i?i(n,r):r({})}const Ll=T(Ml("service"),{invokeServiceMethod:(e,t,n)=>{const{subscribe:o,publishHandler:r}=Av,i=n?Al++:0;n&&o("invokeServiceApi."+i,n,!0),r("invokeServiceApi",{id:i,name:e,args:t})}}),Bl=me(!0);let Fl;function Rl(){Fl&&(clearTimeout(Fl),Fl=null)}let Nl=0,jl=0;function ql(e){if(Rl(),1!==e.touches.length)return;const{pageX:t,pageY:n}=e.touches[0];Nl=t,jl=n,Fl=setTimeout((function(){const t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)}),350)}function zl(e){if(!Fl)return;if(1!==e.touches.length)return Rl();const{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-Nl)>10||Math.abs(n-jl)>10?Rl():void 0}function Hl(e,t){const n=Number(e);return isNaN(n)?t:n}function Vl(){const e=__uniConfig.globalStyle||{},t=Hl(e.rpxCalcMaxDeviceWidth,960),n=Hl(e.rpxCalcBaseDeviceWidth,375);function o(){let e=function(){const e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,t=e&&90===Math.abs(window.orientation);var n=e?Math[t?"max":"min"](screen.width,screen.height):screen.width;return Math.min(window.innerWidth,document.documentElement.clientWidth,n)||n}();e=e<=t?e:n,document.documentElement.style.fontSize=e/23.4375+"px"}o(),document.addEventListener("DOMContentLoaded",o),window.addEventListener("load",o),window.addEventListener("resize",o)}function Wl(){Vl(),he(),window.addEventListener("touchstart",ql,Bl),window.addEventListener("touchmove",zl,Bl),window.addEventListener("touchend",Rl,Bl),window.addEventListener("touchcancel",Rl,Bl)}function Ul(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Yl,Xl,Kl=["top","left","right","bottom"],Gl={};function Zl(){return Xl="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function Jl(){if(Xl="string"==typeof Xl?Xl:Zl()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(a){}var o=document.createElement("div");r(o,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),Kl.forEach((function(e){s(o,e)})),document.body.appendChild(o),i(),Yl=!0}else Kl.forEach((function(e){Gl[e]=0}));function r(e,t){var n=e.style;Object.keys(t).forEach((function(e){var o=t[e];n[e]=o}))}function i(t){t?e.push(t):e.forEach((function(e){e()}))}function s(e,n){var o=document.createElement("div"),s=document.createElement("div"),a=document.createElement("div"),l=document.createElement("div"),c={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:Xl+"(safe-area-inset-"+n+")"};r(o,c),r(s,c),r(a,{transition:"0s",animation:"none",width:"400px",height:"400px"}),r(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),o.appendChild(a),s.appendChild(l),e.appendChild(o),e.appendChild(s),i((function(){o.scrollTop=s.scrollTop=1e4;var e=o.scrollTop,r=s.scrollTop;function i(){this.scrollTop!==(this===o?e:r)&&(o.scrollTop=s.scrollTop=1e4,e=o.scrollTop,r=s.scrollTop,function(e){ec.length||setTimeout((function(){var e={};ec.forEach((function(t){e[t]=Gl[t]})),ec.length=0,tc.forEach((function(t){t(e)}))}),0);ec.push(e)}(n))}o.addEventListener("scroll",i,t),s.addEventListener("scroll",i,t)}));var u=getComputedStyle(o);Object.defineProperty(Gl,n,{configurable:!0,get:function(){return parseFloat(u.paddingBottom)}})}}function Ql(e){return Yl||Jl(),Gl[e]}var ec=[];var tc=[];const nc=Ul({get support(){return 0!=("string"==typeof Xl?Xl:Zl()).length},get top(){return Ql("top")},get left(){return Ql("left")},get right(){return Ql("right")},get bottom(){return Ql("bottom")},onChange:function(e){Zl()&&(Yl||Jl(),"function"==typeof e&&tc.push(e))},offChange:function(e){var t=tc.indexOf(e);t>=0&&tc.splice(t,1)}}),oc=bs((()=>{}),["prevent"]),rc=bs((()=>{}),["stop"]);function ic(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function sc(){const e=ic(document.documentElement.style,"--window-top");return e?e+nc.top:0}function ac(){const e=document.documentElement.style,t=sc(),n=ic(e,"--window-bottom"),o=ic(e,"--window-left"),r=ic(e,"--window-right"),i=ic(e,"--top-window-height");return{top:t,bottom:n?n+nc.bottom:0,left:o?o+nc.left:0,right:r?r+nc.right:0,topWindowHeight:i||0}}function lc(e){const t=document.documentElement.style;Object.keys(e).forEach((n=>{t.setProperty(n,e[n])}))}function cc(e){return lc(e)}function uc(e){return Symbol(e)}function fc(e){return-1!==(e+="").indexOf("rpx")||-1!==e.indexOf("upx")}function dc(e,t=!1){if(t)return function(e){if(!fc(e))return e;return e.replace(/(\d+(\.\d+)?)[ru]px/g,((e,t)=>kf(parseFloat(t))+"px"))}(e);if(I(e)){const t=parseInt(e)||0;return fc(e)?kf(t):t}return e}const hc="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z",pc="M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z";function gc(e,t="#000",n=27){return Kr("svg",{width:n,height:n,viewBox:"0 0 32 32"},[Kr("path",{d:e,fill:t},null,8,["d","fill"])],8,["width","height"])}function mc(){{const{$pageInstance:e}=li();return e&&e.proxy.$page.id}}function vc(e){const t=oe(e);if(t.$page)return t.$page.id;if(t.$){const{$pageInstance:e}=t.$;return e&&e.proxy.$page.id}}function yc(){const e=rg(),t=e.length;if(t)return e[t-1]}function bc(){const e=yc();if(e)return e.$page.meta}function _c(){const e=bc();return e?e.id:-1}function wc(){const e=yc();if(e)return e.$vm}const xc=["navigationBar","pullToRefresh"];function Tc(e,t){const n=JSON.parse(JSON.stringify(__uniConfig.globalStyle||{})),o=T({id:t},n,e);xc.forEach((t=>{o[t]=T({},n[t],e[t])}));const{navigationBar:r}=o;return r.titleText&&r.titleImage&&(r.titleText=""),o}function Sc(e,t,n){if(I(e))n=t,t=e,e=wc();else if("number"==typeof e){const t=rg().find((t=>t.$page.id===e));e=t?t.$vm:wc()}if(!e)return;const o=e.$[t];return o&&((e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n})(o,n)}function Ec(e){e.preventDefault()}let kc,Cc=0;function Oc({onPageScroll:e,onReachBottom:t,onReachBottomDistance:n}){let o=!1,r=!1,i=!0;const s=()=>{function s(){if((()=>{const{scrollHeight:e}=document.documentElement,t=window.innerHeight,o=window.scrollY,i=o>0&&e>t&&o+t+n>=e,s=Math.abs(e-Cc)>n;return!i||r&&!s?(!i&&r&&(r=!1),!1):(Cc=e,r=!0,!0)})())return t&&t(),i=!1,setTimeout((function(){i=!0}),350),!0}e&&e(window.pageYOffset),t&&i&&(s()||(kc=setTimeout(s,300))),o=!1};return function(){clearTimeout(kc),o||requestAnimationFrame(s),o=!0}}function Mc(e,t){if(0===t.indexOf("/"))return t;if(0===t.indexOf("./"))return Mc(e,t.slice(2));const n=t.split("/"),o=n.length;let r=0;for(;r<o&&".."===n[r];r++);n.splice(0,r),t=n.join("/");const i=e.length>0?e.split("/"):[];return i.splice(i.length-r-1,r+1),ae(i.concat(n).join("/"))}function Ac(e,t=!1){return t?__uniRoutes.find((t=>t.path===e||t.alias===e)):__uniRoutes.find((t=>t.path===e))}class Pc{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=function(e,t=!1){const{vnode:n}=e;if(ie(n.el))return t?n.el?[n.el]:[]:n.el;const{subTree:o}=e;if(16&o.shapeFlag){const e=o.children.filter((e=>e.el&&ie(e.el)));if(e.length>0)return t?e.map((e=>e.el)):e[0].el}return t?n.el?[n.el]:[]:n.el}(e.$),this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(!this.$el||!e)return;const t=Lc(this.$el.querySelector(e));return t?Ic(t,!1):void 0}selectAllComponents(e){if(!this.$el||!e)return[];const t=[],n=this.$el.querySelectorAll(e);for(let o=0;o<n.length;o++){const e=Lc(n[o]);e&&t.push(Ic(e,!1))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){const{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){const{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){let t="";if(!e||I(e))return t;for(const n in e){const o=e[n],r=n.startsWith("--")?n:W(n);(I(o)||"number"==typeof o)&&(t+=`${r}:${o};`)}return t}(e))}setStyle(e){return this.$el&&e?(I(e)&&(e=a(e)),R(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;const t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;const{__wxsAddClass:t}=this.$el;if(t){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const n=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===n.indexOf(e)&&(n.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e,t={}){const n=this.$vm[e];P(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&Av.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e,t={}){return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){const t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce(((e,n)=>(e[n]=t[n],e)),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function Ic(e,t=!0){if(t&&e&&(e=re(e.$)),e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new Pc(e)),e.$el.__wxsComponentDescriptor}function $c(e,t){return Ic(e,t)}function Dc(e,t,n,o=!0){if(t){e.__instance||(e.__instance=!0,Object.defineProperty(e,"instance",{get:()=>$c(n.proxy,!1)}));const r=function(e,t,n=!0){if(!t)return!1;if(n&&e.length<2)return!1;const o=re(t);if(!o)return!1;const r=o.$.type;return!(!r.$wxs&&!r.$renderjs)&&o}(t,n,o);if(r)return[e,$c(r,!1)]}}function Lc(e){if(e)return e.__vueParentComponent&&e.__vueParentComponent.proxy}function Bc(e){for(;e&&0!==e.tagName.indexOf("UNI-");)e=e.parentElement;return e}function Fc(e,t=!1){const{type:n,timeStamp:o,target:r,currentTarget:i}=e,s={type:n,timeStamp:o,target:ve(t?r:Bc(r)),detail:{},currentTarget:ve(i)};return e._stopped&&(s._stopped=!0),e.type.startsWith("touch")&&(s.touches=e.touches,s.changedTouches=e.changedTouches),function(e,t){T(e,{preventDefault:()=>t.preventDefault(),stopPropagation:()=>t.stopPropagation()})}(s,e),s}function Rc(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function Nc(e,t){const n=[];for(let o=0;o<e.length;o++){const{identifier:r,pageX:i,pageY:s,clientX:a,clientY:l,force:c}=e[o];n.push({identifier:r,pageX:i,pageY:s-t,clientX:a,clientY:l-t,force:c||0})}return n}const jc=Object.defineProperty({__proto__:null,$nne:function(e,t,n){const{currentTarget:o}=e;if(!(e instanceof Event&&o instanceof HTMLElement))return[e];const r=0!==o.tagName.indexOf("UNI-");if(r)return Dc(e,t,n,!1)||[e];const i=Fc(e,r);if("click"===e.type)!function(e,t){const{x:n,y:o}=t,r=sc();e.detail={x:n,y:o-r},e.touches=e.changedTouches=[Rc(t,r)]}(i,e);else if((e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type))(e))!function(e,t){const n=sc();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[Rc(t,n)]}(i,e);else if((e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch")||["longpress"].indexOf(e.type)>=0)(e)){const t=sc();i.touches=Nc(e.touches,t),i.changedTouches=Nc(e.changedTouches,t)}else if((e=>!e.type.indexOf("key")&&e instanceof KeyboardEvent)(e)){["key","code"].forEach((t=>{Object.defineProperty(i,t,{get:()=>e[t]})}))}return Dc(i,t,n)||[i]},createNativeEvent:Fc},Symbol.toStringTag,{value:"Module"});function qc(e){!function(e){const t=e.globalProperties;T(t,jc),t.$gcd=$c}(e._context.config)}let zc=1;function Hc(e){return(e||_c())+".invokeViewApi"}const Vc=T(Ml("view"),{invokeOnCallback:(e,t)=>Pv.emit("api."+e,t),invokeViewMethod:(e,t,n,o)=>{const{subscribe:r,publishHandler:i}=Pv,s=o?zc++:0;o&&r("invokeViewApi."+s,o,!0),i(Hc(n),{id:s,name:e,args:t},n)},invokeViewMethodKeepAlive:(e,t,n,o)=>{const{subscribe:r,unsubscribe:i,publishHandler:s}=Pv,a=zc++,l="invokeViewApi."+a;return r(l,n),s(Hc(o),{id:a,name:e,args:t},o),()=>{i(l)}}});function Wc(e){Sc(yc(),"onResize",e),Pv.invokeOnCallback("onWindowResize",e)}function Uc(e){const t=yc();Sc(Og(),"onShow",e),Sc(t,"onShow")}function Yc(){Sc(Og(),"onHide"),Sc(yc(),"onHide")}const Xc=["onPageScroll","onReachBottom"];function Kc(){Xc.forEach((e=>Pv.subscribe(e,function(e){return(t,n)=>{Sc(parseInt(n),e,t)}}(e))))}function Gc(){!function(){const{on:e}=Pv;e("onResize",Wc),e("onAppEnterForeground",Uc),e("onAppEnterBackground",Yc)}(),Kc()}function Zc(){if(this.$route){const e=this.$route.meta;return e.eventChannel||(e.eventChannel=new Te(this.$page.id)),e.eventChannel}}function Jc(e){e._context.config.globalProperties.getOpenerEventChannel=Zc}function Qc(){return{path:"",query:{},scene:1001,referrerInfo:{appId:"",extraData:{}}}}function eu(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,((e,t)=>`${kf(parseFloat(t))}px`)):/^-?[\d\.]+$/.test(e)?`${e}px`:e||""}function tu(e){const t=e.animation;if(!t||!t.actions||!t.actions.length)return;let n=0;const o=t.actions,r=t.actions.length;function i(){const t=o[n],s=t.option.transition,a=function(e){const t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],o=["opacity","background-color"],r=["width","height","left","right","top","bottom"],i=e.animates,s=e.option,a=s.transition,l={},c=[];return i.forEach((e=>{let i=e.type,s=[...e.args];if(t.concat(n).includes(i))i.startsWith("rotate")||i.startsWith("skew")?s=s.map((e=>parseFloat(e)+"deg")):i.startsWith("translate")&&(s=s.map(eu)),n.indexOf(i)>=0&&(s.length=1),c.push(`${i}(${s.join(",")})`);else if(o.concat(r).includes(s[0])){i=s[0];const e=s[1];l[i]=r.includes(i)?eu(e):e}})),l.transform=l.webkitTransform=c.join(" "),l.transition=l.webkitTransition=Object.keys(l).map((e=>`${function(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`)).replace("webkit","-webkit")}(e)} ${a.duration}ms ${a.timingFunction} ${a.delay}ms`)).join(","),l.transformOrigin=l.webkitTransformOrigin=s.transformOrigin,l}(t);Object.keys(a).forEach((t=>{e.$el.style[t]=a[t]})),n+=1,n<r&&setTimeout(i,s.duration+s.delay)}setTimeout((()=>{i()}),0)}const nu={props:["animation"],watch:{animation:{deep:!0,handler(){tu(this)}}},mounted(){tu(this)}},ou=e=>{e.__reserved=!0;const{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push(nu),ru(e)},ru=e=>(e.__reserved=!0,e.compatConfig={MODE:3},lo(e)),iu={hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}};function su(e){const t=tn(!1);let n,o,r=!1;function i(){requestAnimationFrame((()=>{clearTimeout(o),o=setTimeout((()=>{t.value=!1}),parseInt(e.hoverStayTime))}))}function s(o){o._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(o._hoverPropagationStopped=!0),r=!0,n=setTimeout((()=>{t.value=!0,r||i()}),parseInt(e.hoverStartTime)))}function a(){r=!1,t.value&&i()}function l(){a(),window.removeEventListener("mouseup",l)}return{hovering:t,binding:{onTouchstartPassive:function(e){e.touches.length>1||s(e)},onMousedown:function(e){r||(s(e),window.addEventListener("mouseup",l))},onTouchend:function(){a()},onMouseup:function(){r&&l()},onTouchcancel:function(){r=!1,t.value=!1,clearTimeout(n)}}}}function au(e,t){return I(t)&&(t=[t]),t.reduce(((t,n)=>(e[n]&&(t[n]=!0),t)),Object.create(null))}function lu(e){return e.__wwe=!0,e}function cu(e,t){return(n,o,r)=>{e.value&&t(n,function(e,t,n,o){const r=ve(n);return{type:o.type||e,timeStamp:t.timeStamp||0,target:r,currentTarget:r,detail:o}}(n,o,e.value,r||{}))}}const uu=uc("uf"),fu=uc("ul");function du(e,t){hu(e.id,t),Xn((()=>e.id),((e,n)=>{pu(n,t,!0),hu(e,t,!0)})),Do((()=>{pu(e.id,t)}))}function hu(e,t,n){const o=mc();n&&!e||R(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&Av.on(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?Av.on(r,t[r]):e&&Av.on(`uni-${r}-${o}-${e}`,t[r])}))}function pu(e,t,n){const o=mc();n&&!e||R(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&Av.off(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?Av.off(r,t[r]):e&&Av.off(`uni-${r}-${o}-${e}`,t[r])}))}const gu=ou({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=tn(null),o=Wn(uu,!1),{hovering:r,binding:i}=su(e);_l();const s=lu(((t,r)=>{if(e.disabled)return t.stopImmediatePropagation();r&&n.value.click();const i=e.formType;if(i){if(!o)return;"submit"===i?o.submit(t):"reset"===i&&o.reset(t)}else;})),a=Wn(fu,!1);return a&&(a.addHandler(s),$o((()=>{a.removeHandler(s)}))),du(e,{"label-click":s}),()=>{const o=e.hoverClass,a=au(e,"disabled"),l=au(e,"loading"),c=au(e,"plain"),u=o&&"none"!==o;return Kr("uni-button",oi({ref:n,onClick:s,class:u&&r.value?o:""},u&&i,a,l,c),[t.default&&t.default()],16,["onClick"])}}});function mu(e){const{base:t}=__uniConfig.router;return 0===ae(e).indexOf(t)?ae(e):t+e}function vu(e){const{base:t,assets:n}=__uniConfig.router;if("./"===t&&(0===e.indexOf("./static/")||n&&0===e.indexOf("./"+n+"/"))&&(e=e.slice(1)),0===e.indexOf("/")){if(0!==e.indexOf("//"))return mu(e.slice(1));e="https:"+e}if(te.test(e)||ne.test(e)||0===e.indexOf("blob:"))return e;const o=rg();return o.length?mu(Mc(o[o.length-1].$page.route,e).slice(1)):e}const yu=navigator.userAgent,bu=/android/i.test(yu),_u=/iphone|ipad|ipod/i.test(yu),wu=yu.match(/Windows NT ([\d|\d.\d]*)/i),xu=/Macintosh|Mac/i.test(yu),Tu=/Linux|X11/i.test(yu),Su=xu&&navigator.maxTouchPoints>0;function Eu(){return/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation}function ku(e){return e&&90===Math.abs(window.orientation)}function Cu(e,t){return e?Math[t?"max":"min"](screen.width,screen.height):screen.width}function Ou(e){return Math.min(window.innerWidth,document.documentElement.clientWidth,e)||e}function Mu(e,t,n,o){Pv.invokeViewMethod("video."+e,{videoId:e,type:n,data:o},t)}function Au(e,t){const n={},{top:o,topWindowHeight:r}=ac();if(t.id&&(n.id=e.id),t.dataset&&(n.dataset=pe(e)),t.rect||t.size){const i=e.getBoundingClientRect();t.rect&&(n.left=i.left,n.right=i.right,n.top=i.top-o-r,n.bottom=i.bottom-o-r),t.size&&(n.width=i.width,n.height=i.height)}if(C(t.properties)&&t.properties.forEach((e=>{e=e.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}))})),t.scrollOffset)if("UNI-SCROLL-VIEW"===e.tagName){const t=e.children[0].children[0];n.scrollLeft=t.scrollLeft,n.scrollTop=t.scrollTop,n.scrollHeight=t.scrollHeight,n.scrollWidth=t.scrollWidth}else n.scrollLeft=0,n.scrollTop=0,n.scrollHeight=0,n.scrollWidth=0;if(C(t.computedStyle)){const o=getComputedStyle(e);t.computedStyle.forEach((e=>{n[e]=o[e]}))}return t.context&&(n.contextInfo=function(e){return e.__uniContextInfo}(e)),n}function Pu(e,t){const n=e.matches||e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector||function(e){const t=this.parentElement.querySelectorAll(e);let n=t.length;for(;--n>=0&&t.item(n)!==this;);return n>-1};return n.call(e,t)}function Iu(e,t,n){const o=[];t.forEach((({component:t,selector:n,single:r,fields:i})=>{null===t?o.push(function(e){const t={};if(e.id&&(t.id=""),e.dataset&&(t.dataset={}),e.rect&&(t.left=0,t.right=0,t.top=0,t.bottom=0),e.size&&(t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight),e.scrollOffset){const e=document.documentElement,n=document.body;t.scrollLeft=e.scrollLeft||n.scrollLeft||0,t.scrollTop=e.scrollTop||n.scrollTop||0,t.scrollHeight=e.scrollHeight||n.scrollHeight||0,t.scrollWidth=e.scrollWidth||n.scrollWidth||0}return t}(i)):o.push(function(e,t,n,o,r){const i=function(e,t){return e?e.$el:t.$el}(t,e),s=i.parentElement;if(!s)return o?null:[];const{nodeType:a}=i,l=3===a||8===a;if(o){const e=l?s.querySelector(n):Pu(i,n)?i:i.querySelector(n);return e?Au(e,r):null}{let e=[];const t=(l?s:i).querySelectorAll(n);return t&&t.length&&[].forEach.call(t,(t=>{e.push(Au(t,r))})),!l&&Pu(i,n)&&e.unshift(Au(i,r)),e}}(e,t,n,r,i))})),n(o)}const $u=["original","compressed"],Du=["album","camera"],Lu=["GET","OPTIONS","HEAD","POST","PUT","DELETE","TRACE","CONNECT","PATCH"];function Bu(e,t){return e&&-1!==t.indexOf(e)?e:t[0]}function Fu(e,t){return!C(e)||0===e.length||e.find((e=>-1===t.indexOf(e)))?t:e}function Ru(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}let Nu=1;const ju={};function qu(e,t,n,o=!1){return ju[e]={name:t,keepAlive:o,callback:n},e}function zu(e,t,n){if("number"==typeof e){const o=ju[e];if(o)return o.keepAlive||delete ju[e],o.callback(t,n)}return t}function Hu(e){for(const t in ju)if(ju[t].name===e)return!0;return!1}const Vu="success",Wu="fail",Uu="complete";function Yu(e,t={},{beforeAll:n,beforeSuccess:o}={}){R(t)||(t={});const{success:r,fail:i,complete:s}=function(e){const t={};for(const n in e){const o=e[n];P(o)&&(t[n]=Ru(o),delete e[n])}return t}(t),a=P(r),l=P(i),c=P(s),u=Nu++;return qu(u,e,(u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),P(n)&&n(u),u.errMsg===e+":ok"?(P(o)&&o(u,t),a&&r(u)):l&&i(u),c&&s(u)})),u}const Xu="success",Ku="fail",Gu="complete",Zu={},Ju={};function Qu(e,t){return function(n){return e(n,t)||n}}function ef(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const i=e[r];if(o)o=Promise.resolve(Qu(i,n));else{const e=i(t,n);if(L(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function tf(e,t={}){return[Xu,Ku,Gu].forEach((n=>{const o=e[n];if(!C(o))return;const r=t[n];t[n]=function(e){ef(o,e,t).then((e=>P(r)&&r(e)||e))}})),t}function nf(e,t){const n=[];C(Zu.returnValue)&&n.push(...Zu.returnValue);const o=Ju[e];return o&&C(o.returnValue)&&n.push(...o.returnValue),n.forEach((e=>{t=e(t)||t})),t}function of(e){const t=Object.create(null);Object.keys(Zu).forEach((e=>{"returnValue"!==e&&(t[e]=Zu[e].slice())}));const n=Ju[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function rf(e,t,n,o){const r=of(e);if(r&&Object.keys(r).length){if(C(r.invoke)){return ef(r.invoke,n).then((n=>t(tf(of(e),n),...o)))}return t(tf(r,n),...o)}return t(n,...o)}function sf(e,t){return(n={},...o)=>function(e){return!(!R(e)||![Vu,Wu,Uu].find((t=>P(e[t]))))}(n)?nf(e,rf(e,t,n,o)):nf(e,new Promise(((r,i)=>{rf(e,t,T(n,{success:r,fail:i}),o)})))}function af(e,t,n,o){return zu(e,T({errMsg:t+":fail"+(n?" "+n:"")},o))}function lf(e,t,n,o){if(o&&o.beforeInvoke){const e=o.beforeInvoke(t);if(I(e))return e}const r=function(e,t){const n=e[0];if(!t||!R(t.formatArgs)&&R(n))return;const o=t.formatArgs,r=Object.keys(o);for(let i=0;i<r.length;i++){const t=r[i],s=o[t];if(P(s)){const o=s(e[0][t],n);if(I(o))return o}else k(n,t)||(n[t]=s)}}(t,o);if(r)return r}function cf(e){if(!P(e))throw new Error('Invalid args: type check failed for args "callback". Expected Function')}function uf(e,t,n){return o=>{cf(o);const r=lf(0,[o],0,n);if(r)throw new Error(r);const i=!Hu(e);!function(e,t){qu(Nu++,e,t,!0)}(e,o),i&&(!function(e){Pv.on("api."+e,(t=>{for(const n in ju){const o=ju[n];o.name===e&&o.callback(t)}}))}(e),t())}}function ff(e,t,n){return o=>{cf(o);const r=lf(0,[o],0,n);if(r)throw new Error(r);!function(e,t){for(const n in ju){const o=ju[n];o.callback===t&&o.name===e&&delete ju[n]}}(e=e.replace("off","on"),o);Hu(e)||(!function(e){Pv.off("api."+e)}(e),t())}}function df(e,t,n,o){return n=>{const r=Yu(e,n,o),i=lf(0,[n],0,o);return i?af(r,e,i):t(n,{resolve:t=>function(e,t,n){return zu(e,T(n||{},{errMsg:t+":ok"}))}(r,e,t),reject:(t,n)=>af(r,e,function(e){return!e||I(e)?e:e.stack?(console.error(e.message+"\n"+e.stack),e.message):e}(t),n)})}}function hf(e,t,n){return uf(e,t,n)}function pf(e,t,n){return ff(e,t,n)}function gf(e,t,n,o){return sf(e,df(e,t,0,o))}function mf(e,t,n,o){return function(e,t,n,o){return(...e)=>{const n=lf(0,e,0,o);if(n)throw new Error(n);return t.apply(null,e)}}(0,t,0,o)}function vf(e,t,n,o){return sf(e,function(e,t,n,o){return df(e,t,0,o)}(e,t,0,o))}let yf=!1,bf=0,_f=0,wf=960,xf=375,Tf=750;function Sf(){const{platform:e,pixelRatio:t,windowWidth:n}=function(){const e=Eu(),t=Ou(Cu(e,ku(e)));return{platform:_u?"ios":"other",pixelRatio:window.devicePixelRatio,windowWidth:t}}();bf=n,_f=t,yf="ios"===e}function Ef(e,t){const n=Number(e);return isNaN(n)?t:n}const kf=mf(0,((e,t)=>{if(0===bf&&(Sf(),function(){const e=__uniConfig.globalStyle||{};wf=Ef(e.rpxCalcMaxDeviceWidth,960),xf=Ef(e.rpxCalcBaseDeviceWidth,375),Tf=Ef(e.rpxCalcBaseDeviceWidth,750)}()),0===(e=Number(e)))return 0;let n=t||bf;n=e===Tf||n<=wf?n:xf;let o=e/750*n;return o<0&&(o=-o),o=Math.floor(o+1e-4),0===o&&(o=1!==_f&&yf?.5:1),e<0?-o:o}));function Cf(e,t){Object.keys(t).forEach((n=>{P(t[n])&&(e[n]=function(e,t){const n=t?e?e.concat(t):C(t)?t:[t]:e;return n?function(e){const t=[];for(let n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n):n}(e[n],t[n]))}))}const Of=mf(0,((e,t)=>{I(e)&&R(t)?Cf(Ju[e]||(Ju[e]={}),t):R(e)&&Cf(Zu,e)})),Mf=new Ae,Af=mf(0,((e,t)=>(Mf.on(e,t),()=>Mf.off(e,t)))),Pf=mf(0,((e,t)=>(Mf.once(e,t),()=>Mf.off(e,t)))),If=mf(0,((e,t)=>{e?(C(e)||(e=[e]),e.forEach((e=>Mf.off(e,t)))):Mf.e={}})),$f=mf(0,((e,...t)=>{Mf.emit(e,...t)})),Df=[.5,.8,1,1.25,1.5,2];class Lf{constructor(e,t){this.id=e,this.pageId=t}play(){Mu(this.id,this.pageId,"play")}pause(){Mu(this.id,this.pageId,"pause")}stop(){Mu(this.id,this.pageId,"stop")}seek(e){Mu(this.id,this.pageId,"seek",{position:e})}sendDanmu(e){Mu(this.id,this.pageId,"sendDanmu",e)}playbackRate(e){~Df.indexOf(e)||(e=1),Mu(this.id,this.pageId,"playbackRate",{rate:e})}requestFullScreen(e={}){Mu(this.id,this.pageId,"requestFullScreen",e)}exitFullScreen(){Mu(this.id,this.pageId,"exitFullScreen")}showStatusBar(){Mu(this.id,this.pageId,"showStatusBar")}hideStatusBar(){Mu(this.id,this.pageId,"hideStatusBar")}}const Bf=mf(0,((e,t)=>new Lf(e,vc(t||wc())))),Ff=(e,t,n,o)=>{!function(e,t,n,o,r){Pv.invokeViewMethod("map."+e,{type:n,data:o},t,r)}(e,t,n,o,(e=>{o&&((e,t)=>{const n=t.errMsg||"";new RegExp("\\:\\s*fail").test(n)?e.fail&&e.fail(t):e.success&&e.success(t),e.complete&&e.complete(t)})(o,e)}))};function Rf(e,t){return function(n,o){n?o[e]=Math.round(n):void 0!==t&&(o[e]=t)}}const Nf=Rf("width"),jf=Rf("height"),qf={PNG:"png",JPG:"jpg",JPEG:"jpg"},zf={formatArgs:{x:Rf("x",0),y:Rf("y",0),width:Nf,height:jf,destWidth:Rf("destWidth"),destHeight:Rf("destHeight"),fileType(e,t){e=(e||"").toUpperCase();let n=qf[e];n||(n=qf.PNG),t.fileType=n},quality(e,t){t.quality=e&&e>0&&e<1?e:1}}};function Hf(e,t,n,o,r){Pv.invokeViewMethod(`canvas.${e}`,{type:n,data:o},t,(e=>{r&&r(e)}))}var Vf=["scale","rotate","translate","setTransform","transform"],Wf=["drawImage","fillText","fill","stroke","fillRect","strokeRect","clearRect","strokeText"],Uf=["setFillStyle","setTextAlign","setStrokeStyle","setGlobalAlpha","setShadow","setFontSize","setLineCap","setLineJoin","setLineWidth","setMiterLimit","setTextBaseline","setLineDash"];const Yf={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgrey:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32",transparent:"#00000000"};function Xf(e){var t=null;if(null!=(t=/^#([0-9|A-F|a-f]{6})$/.exec(e=e||"#000000"))){return[parseInt(t[1].slice(0,2),16),parseInt(t[1].slice(2,4),16),parseInt(t[1].slice(4),16),255]}if(null!=(t=/^#([0-9|A-F|a-f]{3})$/.exec(e))){let e=t[1].slice(0,1),n=t[1].slice(1,2),o=t[1].slice(2,3);return e=parseInt(e+e,16),n=parseInt(n+n,16),o=parseInt(o+o,16),[e,n,o,255]}if(null!=(t=/^rgb\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e){return Math.min(255,parseInt(e.trim()))})).concat(255);if(null!=(t=/^rgba\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e,t){return 3===t?Math.floor(255*parseFloat(e.trim())):Math.min(255,parseInt(e.trim()))}));var n=e.toLowerCase();if(k(Yf,n)){t=/^#([0-9|A-F|a-f]{6,8})$/.exec(Yf[n]);const e=parseInt(t[1].slice(0,2),16),o=parseInt(t[1].slice(2,4),16),r=parseInt(t[1].slice(4,6),16);let i=parseInt(t[1].slice(6,8),16);return i=i>=0?i:255,[e,o,r,i]}return console.error("unsupported color:"+e),[0,0,0,255]}class Kf{constructor(e,t){this.type=e,this.data=t,this.colorStop=[]}addColorStop(e,t){this.colorStop.push([e,Xf(t)])}}class Gf{constructor(e,t){this.type="pattern",this.data=e,this.colorStop=t}}class Zf{constructor(e){this.width=e}}class Jf{constructor(e,t){this.id=e,this.pageId=t,this.actions=[],this.path=[],this.subpath=[],this.drawingState=[],this.state={lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}draw(e=!1,t){var n=[...this.actions];this.actions=[],this.path=[],Hf(this.id,this.pageId,"actionsChanged",{actions:n,reserve:e},t)}createLinearGradient(e,t,n,o){return new Kf("linear",[e,t,n,o])}createCircularGradient(e,t,n){return new Kf("radial",[e,t,n])}createPattern(e,t){if(void 0===t)console.error("Failed to execute 'createPattern' on 'CanvasContext': 2 arguments required, but only 1 present.");else{if(!(["repeat","repeat-x","repeat-y","no-repeat"].indexOf(t)<0))return new Gf(e,t);console.error("Failed to execute 'createPattern' on 'CanvasContext': The provided type ('"+t+"') is not one of 'repeat', 'no-repeat', 'repeat-x', or 'repeat-y'.")}}measureText(e){let t=0;return t=function(e,t){const n=document.createElement("canvas").getContext("2d");return n.font=t,n.measureText(e).width||0}(e,this.state.font),new Zf(t)}save(){this.actions.push({method:"save",data:[]}),this.drawingState.push(this.state)}restore(){this.actions.push({method:"restore",data:[]}),this.state=this.drawingState.pop()||{lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}beginPath(){this.path=[],this.subpath=[],this.path.push({method:"beginPath",data:[]})}moveTo(e,t){this.path.push({method:"moveTo",data:[e,t]}),this.subpath=[[e,t]]}lineTo(e,t){0===this.path.length&&0===this.subpath.length?this.path.push({method:"moveTo",data:[e,t]}):this.path.push({method:"lineTo",data:[e,t]}),this.subpath.push([e,t])}quadraticCurveTo(e,t,n,o){this.path.push({method:"quadraticCurveTo",data:[e,t,n,o]}),this.subpath.push([n,o])}bezierCurveTo(e,t,n,o,r,i){this.path.push({method:"bezierCurveTo",data:[e,t,n,o,r,i]}),this.subpath.push([r,i])}arc(e,t,n,o,r,i=!1){this.path.push({method:"arc",data:[e,t,n,o,r,i]}),this.subpath.push([e,t])}rect(e,t,n,o){this.path.push({method:"rect",data:[e,t,n,o]}),this.subpath=[[e,t]]}arcTo(e,t,n,o,r){this.path.push({method:"arcTo",data:[e,t,n,o,r]}),this.subpath.push([n,o])}clip(){this.actions.push({method:"clip",data:[...this.path]})}closePath(){this.path.push({method:"closePath",data:[]}),this.subpath.length&&(this.subpath=[this.subpath.shift()])}clearActions(){this.actions=[],this.path=[],this.subpath=[]}getActions(){var e=[...this.actions];return this.clearActions(),e}set lineDashOffset(e){this.actions.push({method:"setLineDashOffset",data:[e]})}set globalCompositeOperation(e){this.actions.push({method:"setGlobalCompositeOperation",data:[e]})}set shadowBlur(e){this.actions.push({method:"setShadowBlur",data:[e]})}set shadowColor(e){this.actions.push({method:"setShadowColor",data:[e]})}set shadowOffsetX(e){this.actions.push({method:"setShadowOffsetX",data:[e]})}set shadowOffsetY(e){this.actions.push({method:"setShadowOffsetY",data:[e]})}set font(e){var t=this;this.state.font=e;var n=e.match(/^(([\w\-]+\s)*)(\d+r?px)(\/(\d+\.?\d*(r?px)?))?\s+(.*)/);if(n){var o=n[1].trim().split(/\s/),r=parseFloat(n[3]),i=n[7],s=[];o.forEach((function(e,n){["italic","oblique","normal"].indexOf(e)>-1?(s.push({method:"setFontStyle",data:[e]}),t.state.fontStyle=e):["bold","normal"].indexOf(e)>-1?(s.push({method:"setFontWeight",data:[e]}),t.state.fontWeight=e):0===n?(s.push({method:"setFontStyle",data:["normal"]}),t.state.fontStyle="normal"):1===n&&a()})),1===o.length&&a(),o=s.map((function(e){return e.data[0]})).join(" "),this.state.fontSize=r,this.state.fontFamily=i,this.actions.push({method:"setFont",data:[`${o} ${r}px ${i}`]})}else console.warn("Failed to set 'font' on 'CanvasContext': invalid format.");function a(){s.push({method:"setFontWeight",data:["normal"]}),t.state.fontWeight="normal"}}get font(){return this.state.font}set fillStyle(e){this.setFillStyle(e)}set strokeStyle(e){this.setStrokeStyle(e)}set globalAlpha(e){e=Math.floor(255*parseFloat(e)),this.actions.push({method:"setGlobalAlpha",data:[e]})}set textAlign(e){this.actions.push({method:"setTextAlign",data:[e]})}set lineCap(e){this.actions.push({method:"setLineCap",data:[e]})}set lineJoin(e){this.actions.push({method:"setLineJoin",data:[e]})}set lineWidth(e){this.actions.push({method:"setLineWidth",data:[e]})}set miterLimit(e){this.actions.push({method:"setMiterLimit",data:[e]})}set textBaseline(e){this.actions.push({method:"setTextBaseline",data:[e]})}}const Qf=ce((()=>{[...Vf,...Wf].forEach((function(e){Jf.prototype[e]=function(e){switch(e){case"fill":case"stroke":return function(){this.actions.push({method:e+"Path",data:[...this.path]})};case"fillRect":return function(e,t,n,o){this.actions.push({method:"fillPath",data:[{method:"rect",data:[e,t,n,o]}]})};case"strokeRect":return function(e,t,n,o){this.actions.push({method:"strokePath",data:[{method:"rect",data:[e,t,n,o]}]})};case"fillText":case"strokeText":return function(t,n,o,r){var i=[t.toString(),n,o];"number"==typeof r&&i.push(r),this.actions.push({method:e,data:i})};case"drawImage":return function(t,n,o,r,i,s,a,l,c){var u;function f(e){return"number"==typeof e}void 0===c&&(s=n,a=o,l=r,c=i,n=void 0,o=void 0,r=void 0,i=void 0),u=f(n)&&f(o)&&f(r)&&f(i)?[t,s,a,l,c,n,o,r,i]:f(l)&&f(c)?[t,s,a,l,c]:[t,s,a],this.actions.push({method:e,data:u})};default:return function(...t){this.actions.push({method:e,data:t})}}}(e)})),Uf.forEach((function(e){Jf.prototype[e]=function(e){switch(e){case"setFillStyle":case"setStrokeStyle":return function(t){"object"!=typeof t?this.actions.push({method:e,data:["normal",Xf(t)]}):this.actions.push({method:e,data:[t.type,t.data,t.colorStop]})};case"setGlobalAlpha":return function(t){t=Math.floor(255*parseFloat(t)),this.actions.push({method:e,data:[t]})};case"setShadow":return function(t,n,o,r){r=Xf(r),this.actions.push({method:e,data:[t,n,o,r]}),this.state.shadowBlur=o,this.state.shadowColor=r,this.state.shadowOffsetX=t,this.state.shadowOffsetY=n};case"setLineDash":return function(t,n){t=t||[0,0],n=n||0,this.actions.push({method:e,data:[t,n]}),this.state.lineDash=t};case"setFontSize":return function(t){this.state.font=this.state.font.replace(/\d+\.?\d*px/,t+"px"),this.state.fontSize=t,this.actions.push({method:e,data:[t]})};default:return function(...t){this.actions.push({method:e,data:t})}}}(e)}))})),ed=mf(0,((e,t)=>{if(Qf(),t)return new Jf(e,vc(t));const n=vc(wc());if(n)return new Jf(e,n);Pv.emit("onError","createCanvasContext:fail")})),td=vf("canvasToTempFilePath",(({x:e=0,y:t=0,width:n,height:o,destWidth:r,destHeight:i,canvasId:s,fileType:a,quality:l},{resolve:c,reject:u})=>{var f=vc(wc());if(!f)return void u();Hf(s,f,"toTempFilePath",{x:e,y:t,width:n,height:o,destWidth:r,destHeight:i,fileType:a,quality:l,dirname:`${Jd}/canvas`},(e=>{e.errMsg&&-1!==e.errMsg.indexOf("fail")?u("",e):c(e)}))}),0,zf);let nd=0,od={};function rd(e,t,n,o){const r={options:o},i=o&&("success"in o||"fail"in o||"complete"in o);if(i){const e=String(nd++);r.callbackId=e,od[e]=o}Pv.invokeViewMethod(`editor.${e}`,{type:n,data:r},t,(({callbackId:e,data:t})=>{i&&(!function(e,t){e=e||{},I(t)&&(t={errMsg:t}),/:ok$/.test(t.errMsg)?P(e.success)&&e.success(t):P(e.fail)&&e.fail(t),P(e.complete)&&e.complete(t)}(od[e],t),delete od[e])}))}const id={canvas:Jf,map:class{constructor(e,t){this.id=e,this.pageId=t}getCenterLocation(e){Ff(this.id,this.pageId,"getCenterLocation",e)}moveToLocation(e){Ff(this.id,this.pageId,"moveToLocation",e)}getScale(e){Ff(this.id,this.pageId,"getScale",e)}getRegion(e){Ff(this.id,this.pageId,"getRegion",e)}includePoints(e){Ff(this.id,this.pageId,"includePoints",e)}translateMarker(e){Ff(this.id,this.pageId,"translateMarker",e)}$getAppMap(){}addCustomLayer(e){Ff(this.id,this.pageId,"addCustomLayer",e)}removeCustomLayer(e){Ff(this.id,this.pageId,"removeCustomLayer",e)}addGroundOverlay(e){Ff(this.id,this.pageId,"addGroundOverlay",e)}removeGroundOverlay(e){Ff(this.id,this.pageId,"removeGroundOverlay",e)}updateGroundOverlay(e){Ff(this.id,this.pageId,"updateGroundOverlay",e)}initMarkerCluster(e){Ff(this.id,this.pageId,"initMarkerCluster",e)}addMarkers(e){Ff(this.id,this.pageId,"addMarkers",e)}removeMarkers(e){Ff(this.id,this.pageId,"removeMarkers",e)}moveAlong(e){Ff(this.id,this.pageId,"moveAlong",e)}setLocMarkerIcon(e){Ff(this.id,this.pageId,"setLocMarkerIcon",e)}openMapApp(e){Ff(this.id,this.pageId,"openMapApp",e)}on(e,t){Ff(this.id,this.pageId,"on",{name:e,callback:t})}},video:Lf,editor:class{constructor(e,t){this.id=e,this.pageId=t}format(e,t){this._exec("format",{name:e,value:t})}insertDivider(){this._exec("insertDivider")}insertImage(e){this._exec("insertImage",e)}insertText(e){this._exec("insertText",e)}setContents(e){this._exec("setContents",e)}getContents(e){this._exec("getContents",e)}clear(e){this._exec("clear",e)}removeFormat(e){this._exec("removeFormat",e)}undo(e){this._exec("undo",e)}redo(e){this._exec("redo",e)}blur(e){this._exec("blur",e)}getSelectionText(e){this._exec("getSelectionText",e)}scrollIntoView(e){this._exec("scrollIntoView",e)}_exec(e,t){rd(this.id,this.pageId,e,t)}}};function sd(e){if(e&&e.contextInfo){const{id:t,type:n,page:o}=e.contextInfo,r=id[n];e.context=new r(t,o),delete e.contextInfo}}class ad{constructor(e,t,n,o){this._selectorQuery=e,this._component=t,this._selector=n,this._single=o}boundingClientRect(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,rect:!0,size:!0},e),this._selectorQuery}fields(e,t){return this._selectorQuery._push(this._selector,this._component,this._single,e,t),this._selectorQuery}scrollOffset(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,scrollOffset:!0},e),this._selectorQuery}context(e){return this._selectorQuery._push(this._selector,this._component,this._single,{context:!0},e),this._selectorQuery}node(e){return this._selectorQuery}}class ld{constructor(e){this._component=void 0,this._page=e,this._queue=[],this._queueCb=[]}exec(e){return Iu(this._page,this._queue,(t=>{const n=this._queueCb;t.forEach(((e,t)=>{C(e)?e.forEach(sd):sd(e);const o=n[t];P(o)&&o.call(this,e)})),P(e)&&e.call(this,t)})),this._nodesRef}in(e){return this._component=oe(e),this}select(e){return this._nodesRef=new ad(this,this._component,e,!0)}selectAll(e){return this._nodesRef=new ad(this,this._component,e,!1)}selectViewport(){return this._nodesRef=new ad(this,null,"",!0)}_push(e,t,n,o,r){this._queue.push({component:t,selector:e,single:n,fields:o}),this._queueCb.push(r)}}const cd=mf(0,(e=>((e=oe(e))&&!vc(e)&&(e=null),new ld(e||wc())))),ud={formatArgs:{}},fd={duration:400,timingFunction:"linear",delay:0,transformOrigin:"50% 50% 0"};class dd{constructor(e){this.actions=[],this.currentTransform={},this.currentStepAnimates=[],this.option=T({},fd,e)}_getOption(e){const t={transition:T({},this.option,e),transformOrigin:""};return t.transformOrigin=t.transition.transformOrigin,delete t.transition.transformOrigin,t}_pushAnimates(e,t){this.currentStepAnimates.push({type:e,args:t})}_converType(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))}_getValue(e){return"number"==typeof e?`${e}px`:e}export(){const e=this.actions;return this.actions=[],{actions:e}}step(e){return this.currentStepAnimates.forEach((e=>{"style"!==e.type?this.currentTransform[e.type]=e:this.currentTransform[`${e.type}.${e.args[0]}`]=e})),this.actions.push({animates:Object.values(this.currentTransform),option:this._getOption(e)}),this.currentStepAnimates=[],this}}const hd=ce((()=>{const e=["opacity","backgroundColor"],t=["width","height","left","right","top","bottom"];["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"].concat(e,t).forEach((n=>{dd.prototype[n]=function(...o){return e.concat(t).includes(n)?this._pushAnimates("style",[this._converType(n),t.includes(n)?this._getValue(o[0]):o[0]]):this._pushAnimates(n,o),this}}))})),pd=mf(0,(e=>(hd(),new dd(e))),0,ud),gd=hf("onWindowResize",(()=>{})),md=pf("offWindowResize",(()=>{})),vd=mf(0,(()=>{const e=Og();return e&&e.$vm?e.$vm.$locale:_l().getLocale()})),yd={onUnhandledRejection:[],onPageNotFound:[],onError:[],onShow:[],onHide:[]};const bd=mf(0,(()=>T({},rh)));let _d,wd,xd;const Td=[];const Sd=vf("getPushClientId",((e,{resolve:t,reject:n})=>{Promise.resolve().then((()=>{var e,o;void 0===xd&&(xd=!1,_d="",wd="uniPush is not enabled"),Td.push(((e,o)=>{e?t({cid:e}):n(o)})),void 0!==_d&&(e=_d,o=wd,Td.forEach((t=>{t(e,o)})),Td.length=0)}))})),Ed=e=>{},kd=e=>{},Cd={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=9)},sizeType(e,t){t.sizeType=Fu(e,$u)},sourceType(e,t){t.sourceType=Fu(e,Du)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},Od={formatArgs:{sourceType(e,t){t.sourceType=Fu(e,Du)},compressed:!0,maxDuration:60,camera:"back",extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},Md=(Boolean,["all","image","video"]),Ad={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=100)},sourceType(e,t){t.sourceType=Fu(e,Du)},type(e,t){t.type=Bu(e,Md)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=[""])}}},Pd="json",Id=["text","arraybuffer"],$d=encodeURIComponent;ArrayBuffer,Boolean;const Dd={formatArgs:{method(e,t){t.method=Bu((e||"").toUpperCase(),Lu)},data(e,t){t.data=e||""},url(e,t){t.method===Lu[0]&&R(t.data)&&Object.keys(t.data).length&&(t.url=function(e,t){let n=e.split("#");const o=n[1]||"";n=n[0].split("?");let r=n[1]||"";e=n[0];const i=r.split("&").filter((e=>e)),s={};i.forEach((e=>{const t=e.split("=");s[t[0]]=t[1]}));for(const a in t)if(k(t,a)){let e=t[a];null==e?e="":R(e)&&(e=JSON.stringify(e)),s[$d(a)]=$d(e)}return r=Object.keys(s).map((e=>`${e}=${s[e]}`)).join("&"),e+(r?"?"+r:"")+(o?"#"+o:"")}(e,t.data))},header(e,t){const n=t.header=e||{};t.method!==Lu[0]&&(Object.keys(n).find((e=>"content-type"===e.toLowerCase()))||(n["Content-Type"]="application/json"))},dataType(e,t){t.dataType=(e||Pd).toLowerCase()},responseType(e,t){t.responseType=(e||"").toLowerCase(),-1===Id.indexOf(t.responseType)&&(t.responseType="text")}}},Ld={formatArgs:{header(e,t){t.header=e||{}}}},Bd={formatArgs:{filePath(e,t){e&&(t.filePath=vu(e))},header(e,t){t.header=e||{}},formData(e,t){t.formData=e||{}}}};const Fd={url:{type:String,required:!0}},Rd=(Hd(["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"]),Hd(["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"]),Ud("navigateTo")),Nd=Ud("redirectTo"),jd=Ud("reLaunch"),qd=Ud("switchTab"),zd={formatArgs:{delta(e,t){e=parseInt(e+"")||1,t.delta=Math.min(rg().length-1,e)}}};function Hd(e){return{animationType:{type:String,validator(t){if(t&&-1===e.indexOf(t))return"`"+t+"` is not supported for `animationType` (supported values are: `"+e.join("`|`")+"`)"}},animationDuration:{type:Number}}}let Vd;function Wd(){Vd=""}function Ud(e){return{formatArgs:{url:Yd(e)},beforeAll:Wd}}function Yd(e){return function(t,n){if(!t)return'Missing required args: "url"';const o=(t=function(e){if(0===e.indexOf("/"))return e;let t="";const n=rg();return n.length&&(t=n[n.length-1].$page.route),Mc(t,e)}(t)).split("?")[0],r=Ac(o,!0);if(!r)return"page `"+t+"` is not found";if("navigateTo"===e||"redirectTo"===e){if(r.meta.isTabBar)return`can not ${e} a tabbar page`}else if("switchTab"===e&&!r.meta.isTabBar)return"can not switch to no-tabBar page";if("switchTab"!==e&&"preloadPage"!==e||!r.meta.isTabBar||"appLaunch"===n.openType||(t=o),r.meta.isEntry&&(t=t.replace(r.alias,"/")),n.url=function(e){if(!I(e))return e;const t=e.indexOf("?");if(-1===t)return e;const n=e.slice(t+1).trim().replace(/^(\?|#|&)/,"");if(!n)return e;e=e.slice(0,t);const o=[];return n.split("&").forEach((e=>{const t=e.replace(/\+/g," ").split("="),n=t.shift(),r=t.length>0?t.join("="):"";o.push(n+"="+encodeURIComponent(r))})),o.length?e+"?"+o.join("&"):e}(t),"unPreloadPage"!==e)if("preloadPage"!==e){if(Vd===t&&"appLaunch"!==n.openType)return`${Vd} locked`;__uniConfig.ready&&(Vd=t)}else if(r.meta.isTabBar){const e=rg(),t=r.path.slice(1);if(e.find((e=>e.route===t)))return"tabBar page `"+t+"` already exists"}}}Boolean;const Xd={formatArgs:{title:"",mask:!1}},Kd=(Boolean,{beforeInvoke(){El()},formatArgs:{title:"",content:"",placeholderText:"",showCancel:!0,editable:!1,cancelText(e,t){if(!k(t,"cancelText")){const{t:e}=_l();t.cancelText=e("uni.showModal.cancel")}},cancelColor:"#000",confirmText(e,t){if(!k(t,"confirmText")){const{t:e}=_l();t.confirmText=e("uni.showModal.confirm")}},confirmColor:"#007aff"}}),Gd=["success","loading","none","error"],Zd=(Boolean,{formatArgs:{title:"",icon(e,t){t.icon=Bu(e,Gd)},image(e,t){t.image=e?vu(e):""},duration:1500,mask:!1}});const Jd="",Qd={};function eh(e,t){const n=Qd[e];return n?Promise.resolve(n):/^data:[a-z-]+\/[a-z-]+;base64,/.test(e)?Promise.resolve(function(e){const t=e.split(","),n=t[0].match(/:(.*?);/),o=n?n[1]:"",r=atob(t[1]);let i=r.length;const s=new Uint8Array(i);for(;i--;)s[i]=r.charCodeAt(i);return th(s,o)}(e)):t?Promise.reject(new Error("not find")):new Promise(((t,n)=>{const o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="blob",o.onload=function(){t(this.response)},o.onerror=n,o.send()}))}function th(e,t){let n;if(e instanceof File)n=e;else{t=t||e.type||"";const r=`${Date.now()}${function(e){const t=e.split("/")[1];return t?`.${t}`:""}(t)}`;try{n=new File([e],r,{type:t})}catch(o){n=e=e instanceof Blob?e:new Blob([e],{type:t}),n.name=n.name||r}}return n}function nh(e){for(const n in Qd)if(k(Qd,n)){if(Qd[n]===e)return n}var t=(window.URL||window.webkitURL).createObjectURL(e);return Qd[t]=e,t}function oh(e){(window.URL||window.webkitURL).revokeObjectURL(e),delete Qd[e]}const rh=Qc(),ih=Qc();const sh=ou({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,{emit:t}){const n=tn(null),o=function(e){return()=>{const{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}(n),r=function(e,t,n){const o=qt({width:-1,height:-1});return Xn((()=>T({},o)),(e=>t("resize",e))),()=>{const t=e.value;o.width=t.offsetWidth,o.height=t.offsetHeight,n()}}(n,t,o);return function(e,t,n,o){yo(o),Ao((()=>{t.initial&&Tn(n);const r=e.value;r.offsetParent!==r.parentElement&&(r.parentElement.style.position="relative"),"AnimationEvent"in window||o()}))}(n,e,r,o),()=>Kr("uni-resize-sensor",{ref:n,onAnimationstartOnce:r},[Kr("div",{onScroll:r},[Kr("div",null,null)],40,["onScroll"]),Kr("div",{onScroll:r},[Kr("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});const ah=function(){if(navigator.userAgent.includes("jsdom"))return 1;const e=document.createElement("canvas");e.height=e.width=0;const t=e.getContext("2d"),n=t.backingStorePixelRatio||t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1;return(window.devicePixelRatio||1)/n}();function lh(e,t=!0){e.width=e.offsetWidth*(t?ah:1),e.height=e.offsetHeight*(t?ah:1),e.getContext("2d").__hidpi__=t}let ch=!1;function uh(){if(ch)return;ch=!0;const e={fillRect:"all",clearRect:"all",strokeRect:"all",moveTo:"all",lineTo:"all",arc:[0,1,2],arcTo:"all",bezierCurveTo:"all",isPointinPath:"all",isPointinStroke:"all",quadraticCurveTo:"all",rect:"all",translate:"all",createRadialGradient:"all",createLinearGradient:"all",transform:[4,5],setTransform:[4,5]},t=CanvasRenderingContext2D.prototype;var n;t.drawImageByCanvas=(n=t.drawImage,function(e,t,o,r,i,s,a,l,c,u){if(!this.__hidpi__)return n.apply(this,arguments);t*=ah,o*=ah,r*=ah,i*=ah,s*=ah,a*=ah,l=u?l*ah:l,c=u?c*ah:c,n.call(this,e,t,o,r,i,s,a,l,c)}),1!==ah&&(!function(e,t){for(const n in e)k(e,n)&&t(e[n],n)}(e,(function(e,n){t[n]=function(t){return function(){if(!this.__hidpi__)return t.apply(this,arguments);let n=Array.prototype.slice.call(arguments);if("all"===e)n=n.map((function(e){return e*ah}));else if(Array.isArray(e))for(let t=0;t<e.length;t++)n[e[t]]*=ah;return t.apply(this,n)}}(t[n])})),t.stroke=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);this.lineWidth*=ah,e.apply(this,arguments),this.lineWidth/=ah}}(t.stroke),t.fillText=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);const t=Array.prototype.slice.call(arguments);t[1]*=ah,t[2]*=ah,t[3]&&"number"==typeof t[3]&&(t[3]*=ah);var n=this.font;this.font=n.replace(/(\d+\.?\d*)(px|em|rem|pt)/g,(function(e,t,n){return t*ah+n})),e.apply(this,t),this.font=n}}(t.fillText),t.strokeText=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);var t=Array.prototype.slice.call(arguments);t[1]*=ah,t[2]*=ah,t[3]&&"number"==typeof t[3]&&(t[3]*=ah);var n=this.font;this.font=n.replace(/(\d+\.?\d*)(px|em|rem|pt)/g,(function(e,t,n){return t*ah+n})),e.apply(this,t),this.font=n}}(t.strokeText),t.drawImage=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);this.scale(ah,ah),e.apply(this,arguments),this.scale(1/ah,1/ah)}}(t.drawImage))}const fh=ce((()=>uh()));function dh(e){return e?vu(e):e}function hh(e){return(e=e.slice(0))[3]=e[3]/255,"rgba("+e.join(",")+")"}function ph(e,t){Array.from(t).forEach((t=>{t.x=t.clientX-e.left,t.y=t.clientY-e.top}))}let gh;function mh(e=0,t=0){return gh||(gh=document.createElement("canvas")),gh.width=e,gh.height=t,gh}const vh=ou({inheritAttrs:!1,name:"Canvas",compatConfig:{MODE:3},props:{canvasId:{type:String,default:""},disableScroll:{type:[Boolean,String],default:!1},hidpi:{type:Boolean,default:!0}},computed:{id(){return this.canvasId}},setup(e,{emit:t,slots:n}){fh();const o=tn(null),r=tn(null),i=tn(!1),s=function(e){return(t,n)=>{e(t,Fc(n))}}(t),{$attrs:a,$excludeAttrs:l,$listeners:c}=Xh({excludeListeners:!0}),{_listeners:u}=function(e,t,n){const o=vi((()=>{let o=["onTouchstart","onTouchmove","onTouchend"],r=t.value,i=T({},(()=>{let e={};for(const t in r)if(k(r,t)){const n=r[t];e[t]=n}return e})());return o.forEach((t=>{let o=[];i[t]&&o.push(lu((e=>{const o=e.currentTarget.getBoundingClientRect();ph(o,e.touches),ph(o,e.changedTouches),n(t.replace("on","").toLocaleLowerCase(),e)}))),e.disableScroll&&"onTouchmove"===t&&o.push(oc),i[t]=o})),i}));return{_listeners:o}}(e,c,s),{_handleSubscribe:f,_resize:d}=function(e,t,n){let o=[],r={};const i=vi((()=>e.hidpi?ah:1));function s(n){let o=t.value;if(!n||o.width!==Math.floor(n.width*i.value)||o.height!==Math.floor(n.height*i.value))if(o.width>0&&o.height>0){let t=o.getContext("2d"),n=t.getImageData(0,0,o.width,o.height);lh(o,e.hidpi),t.putImageData(n,0,0)}else lh(o,e.hidpi)}function a({actions:e,reserve:i},s){if(!e)return;if(n.value)return void o.push([e,i]);let a=t.value,u=a.getContext("2d");i||(u.fillStyle="#000000",u.strokeStyle="#000000",u.shadowColor="#000000",u.shadowBlur=0,u.shadowOffsetX=0,u.shadowOffsetY=0,u.setTransform(1,0,0,1,0,0),u.clearRect(0,0,a.width,a.height)),l(e);for(let t=0;t<e.length;t++){const n=e[t];let o=n.method;const i=n.data,a=i[0];if(/^set/.test(o)&&"setTransform"!==o){const n=o[3].toLowerCase()+o.slice(4);let r;if("fillStyle"===n||"strokeStyle"===n){if("normal"===a)r=hh(i[1]);else if("linear"===a){const e=u.createLinearGradient(...i[1]);i[2].forEach((function(t){const n=t[0],o=hh(t[1]);e.addColorStop(n,o)})),r=e}else if("radial"===a){let e=i[1];const t=e[0],n=e[1],o=e[2],s=u.createRadialGradient(t,n,0,t,n,o);i[2].forEach((function(e){const t=e[0],n=hh(e[1]);s.addColorStop(t,n)})),r=s}else if("pattern"===a){if(!c(i[1],e.slice(t+1),s,(function(e){e&&(u[n]=u.createPattern(e,i[2]))})))break;continue}u[n]=r}else if("globalAlpha"===n)u[n]=Number(a)/255;else if("shadow"===n){let e=["shadowOffsetX","shadowOffsetY","shadowBlur","shadowColor"];i.forEach((function(t,n){u[e[n]]="shadowColor"===e[n]?hh(t):t}))}else if("fontSize"===n){const e=u.__font__||u.font;u.__font__=u.font=e.replace(/\d+\.?\d*px/,a+"px")}else"lineDash"===n?(u.setLineDash(a),u.lineDashOffset=i[1]||0):"textBaseline"===n?("normal"===a&&(i[0]="alphabetic"),u[n]=a):"font"===n?u.__font__=u.font=a:u[n]=a}else if("fillPath"===o||"strokePath"===o)o=o.replace(/Path/,""),u.beginPath(),i.forEach((function(e){u[e.method].apply(u,e.data)})),u[o]();else if("fillText"===o)u.fillText.apply(u,i);else if("drawImage"===o){if("break"===function(){let n=[...i],o=n[0],a=n.slice(1);if(r=r||{},!c(o,e.slice(t+1),s,(function(e){e&&u.drawImage.apply(u,[e].concat([...a.slice(4,8)],[...a.slice(0,4)]))})))return"break"}())break}else"clip"===o?(i.forEach((function(e){u[e.method].apply(u,e.data)})),u.clip()):u[o].apply(u,i)}n.value||s({errMsg:"drawCanvas:ok"})}function l(e){e.forEach((function(e){let t=e.method,n=e.data,o="";function i(){const e=r[o]=new Image;e.onload=function(){e.ready=!0},function(e){const t=document.createElement("a");return t.href=e,t.origin===location.origin?Promise.resolve(e):eh(e).then(nh)}(o).then((t=>{e.src=t})).catch((()=>{e.src=o}))}"drawImage"===t?(o=n[0],o=dh(o),n[0]=o):"setFillStyle"===t&&"pattern"===n[0]&&(o=n[1],o=dh(o),n[1]=o),o&&!r[o]&&i()}))}function c(e,t,i,s){let l=r[e];return l.ready?(s(l),!0):(o.unshift([t,!0]),n.value=!0,l.onload=function(){l.ready=!0,s(l),n.value=!1;let e=o.slice(0);o=[];for(let t=e.shift();t;)a({actions:t[0],reserve:t[1]},i),t=e.shift()},!1)}function u({x:e=0,y:n=0,width:o,height:r,destWidth:s,destHeight:a,hidpi:l=!0,dataType:c,quality:u=1,type:f="png"},d){const h=t.value;let p;const g=h.offsetWidth-e;o=o?Math.min(o,g):g;const m=h.offsetHeight-n;r=r?Math.min(r,m):m,l?(s=o,a=r):s||a?s?a||(a=Math.round(r/o*s)):s=Math.round(o/r*a):(s=Math.round(o*i.value),a=Math.round(r*i.value));const v=mh(s,a),y=v.getContext("2d");let b;"jpeg"!==f&&"jpg"!==f||(f="jpeg",y.fillStyle="#fff",y.fillRect(0,0,s,a)),y.__hidpi__=!0,y.drawImageByCanvas(h,e,n,o,r,0,0,s,a,!1);try{let e;if("base64"===c)p=v.toDataURL(`image/${f}`,u);else{const e=y.getImageData(0,0,s,a);p=Array.prototype.slice.call(e.data)}b={data:p,compressed:e,width:s,height:a}}catch(_){b={errMsg:`canvasGetImageData:fail ${_}`}}if(v.height=v.width=0,y.__hidpi__=!1,!d)return b;d(b)}function f({data:e,x:n,y:o,width:r,height:i,compressed:s},a){try{0,i||(i=Math.round(e.length/4/r));const s=mh(r,i);s.getContext("2d").putImageData(new ImageData(new Uint8ClampedArray(e),r,i),0,0),t.value.getContext("2d").drawImage(s,n,o,r,i),s.height=s.width=0}catch(l){return void a({errMsg:"canvasPutImageData:fail"})}a({errMsg:"canvasPutImageData:ok"})}function d({x:e=0,y:t=0,width:n,height:o,destWidth:r,destHeight:i,fileType:s,quality:a,dirname:l},c){const f=u({x:e,y:t,width:n,height:o,destWidth:r,destHeight:i,hidpi:!1,dataType:"base64",type:s,quality:a});var d;f.data&&f.data.length?(d=f.data,((e,t)=>{let n="toTempFilePath:"+(e?"fail":"ok");e&&(n+=` ${e.message}`),c({errMsg:n,tempFilePath:t})})(null,d)):c({errMsg:f.errMsg.replace("canvasPutImageData","toTempFilePath")})}const h={actionsChanged:a,getImageData:u,putImageData:f,toTempFilePath:d};function p(e,t,n){let o=h[e];0!==e.indexOf("_")&&P(o)&&o(t,n)}return T(h,{_resize:s,_handleSubscribe:p})}(e,o,i);return Pp(f,$p(e.canvasId),!0),Ao((()=>{d()})),()=>{const{canvasId:t,disableScroll:i}=e;return Kr("uni-canvas",oi({"canvas-id":t,"disable-scroll":i},a.value,l.value,u.value),[Kr("canvas",{ref:o,class:"uni-canvas-canvas",width:"300",height:"150"},null,512),Kr("div",{style:"position: absolute;top: 0;left: 0;width: 100%;height: 100%;overflow: hidden;"},[n.default&&n.default()]),Kr(sh,{ref:r,onResize:d},null,8,["onResize"])],16,["canvas-id","disable-scroll"])}}});function yh(){}const bh={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}};function _h(e,t,n){function o(e){const t=vi((()=>0===String(navigator.vendor).indexOf("Apple")));e.addEventListener("focus",(()=>{clearTimeout(undefined),document.addEventListener("click",yh,!1)}));e.addEventListener("blur",(()=>{t.value&&e.blur(),document.removeEventListener("click",yh,!1),t.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)}))}Xn((()=>t.value),(e=>e&&o(e)))}const wh={success:{d:"M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM24.832 11.328l-11.264 11.104q-0.032 0.032-0.112 0.032t-0.112-0.032l-5.216-5.376q-0.096-0.128 0-0.288l0.704-0.96q0.032-0.064 0.112-0.064t0.112 0.032l4.256 3.264q0.064 0.032 0.144 0.032t0.112-0.032l10.336-8.608q0.064-0.064 0.144-0.064t0.112 0.064l0.672 0.672q0.128 0.128 0 0.224z",c:"#007aff"},success_no_circle:{d:hc,c:"#007aff"},info:{d:"M15.808 0.128q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.176 3.776-2.176 8.16 0 4.224 2.176 7.872 2.080 3.552 5.632 5.632 3.648 2.176 7.872 2.176 4.384 0 8.16-2.176 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.416-2.176-8.16-2.112-3.616-5.728-5.728-3.744-2.176-8.16-2.176zM16.864 23.776q0 0.064-0.064 0.064h-1.568q-0.096 0-0.096-0.064l-0.256-11.328q0-0.064 0.064-0.064h2.112q0.096 0 0.064 0.064l-0.256 11.328zM16 10.88q-0.576 0-0.976-0.4t-0.4-0.96 0.4-0.96 0.976-0.4 0.976 0.4 0.4 0.96-0.4 0.96-0.976 0.4z",c:"#10aeff"},warn:{d:pc,c:"#f76260"},waiting:{d:"M15.84 0.096q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM23.008 21.92l-0.512 0.896q-0.096 0.128-0.224 0.064l-8-3.808q-0.096-0.064-0.16-0.128-0.128-0.096-0.128-0.288l0.512-12.096q0-0.064 0.048-0.112t0.112-0.048h1.376q0.064 0 0.112 0.048t0.048 0.112l0.448 10.848 6.304 4.256q0.064 0.064 0.080 0.128t-0.016 0.128z",c:"#10aeff"},cancel:{d:"M20.928 10.176l-4.928 4.928-4.928-4.928-0.896 0.896 4.928 4.928-4.928 4.928 0.896 0.896 4.928-4.928 4.928 4.928 0.896-0.896-4.928-4.928 4.928-4.928-0.896-0.896zM16 2.080q-3.776 0-7.040 1.888-3.136 1.856-4.992 4.992-1.888 3.264-1.888 7.040t1.888 7.040q1.856 3.136 4.992 4.992 3.264 1.888 7.040 1.888t7.040-1.888q3.136-1.856 4.992-4.992 1.888-3.264 1.888-7.040t-1.888-7.040q-1.856-3.136-4.992-4.992-3.264-1.888-7.040-1.888zM16 28.64q-3.424 0-6.4-1.728-2.848-1.664-4.512-4.512-1.728-2.976-1.728-6.4t1.728-6.4q1.664-2.848 4.512-4.512 2.976-1.728 6.4-1.728t6.4 1.728q2.848 1.664 4.512 4.512 1.728 2.976 1.728 6.4t-1.728 6.4q-1.664 2.848-4.512 4.512-2.976 1.728-6.4 1.728z",c:"#f43530"},download:{d:"M15.808 1.696q-3.776 0-7.072 1.984-3.2 1.888-5.088 5.152-1.952 3.392-1.952 7.36 0 3.776 1.952 7.072 1.888 3.2 5.088 5.088 3.296 1.952 7.072 1.952 3.968 0 7.36-1.952 3.264-1.888 5.152-5.088 1.984-3.296 1.984-7.072 0-4-1.984-7.36-1.888-3.264-5.152-5.152-3.36-1.984-7.36-1.984zM20.864 18.592l-3.776 4.928q-0.448 0.576-1.088 0.576t-1.088-0.576l-3.776-4.928q-0.448-0.576-0.24-0.992t0.944-0.416h2.976v-8.928q0-0.256 0.176-0.432t0.4-0.176h1.216q0.224 0 0.4 0.176t0.176 0.432v8.928h2.976q0.736 0 0.944 0.416t-0.24 0.992z",c:"#007aff"},search:{d:"M20.928 22.688q-1.696 1.376-3.744 2.112-2.112 0.768-4.384 0.768-3.488 0-6.464-1.728-2.88-1.696-4.576-4.608-1.76-2.976-1.76-6.464t1.76-6.464q1.696-2.88 4.576-4.576 2.976-1.76 6.464-1.76t6.464 1.76q2.912 1.696 4.608 4.576 1.728 2.976 1.728 6.464 0 2.272-0.768 4.384-0.736 2.048-2.112 3.744l9.312 9.28-1.824 1.824-9.28-9.312zM12.8 23.008q2.784 0 5.184-1.376 2.304-1.376 3.68-3.68 1.376-2.4 1.376-5.184t-1.376-5.152q-1.376-2.336-3.68-3.68-2.4-1.408-5.184-1.408t-5.152 1.408q-2.336 1.344-3.68 3.68-1.408 2.368-1.408 5.152t1.408 5.184q1.344 2.304 3.68 3.68 2.368 1.376 5.152 1.376zM12.8 23.008v0z",c:"#b2b2b2"},clear:{d:"M16 0q-4.352 0-8.064 2.176-3.616 2.144-5.76 5.76-2.176 3.712-2.176 8.064t2.176 8.064q2.144 3.616 5.76 5.76 3.712 2.176 8.064 2.176t8.064-2.176q3.616-2.144 5.76-5.76 2.176-3.712 2.176-8.064t-2.176-8.064q-2.144-3.616-5.76-5.76-3.712-2.176-8.064-2.176zM22.688 21.408q0.32 0.32 0.304 0.752t-0.336 0.736-0.752 0.304-0.752-0.32l-5.184-5.376-5.376 5.184q-0.32 0.32-0.752 0.304t-0.736-0.336-0.304-0.752 0.32-0.752l5.376-5.184-5.184-5.376q-0.32-0.32-0.304-0.752t0.336-0.752 0.752-0.304 0.752 0.336l5.184 5.376 5.376-5.184q0.32-0.32 0.752-0.304t0.752 0.336 0.304 0.752-0.336 0.752l-5.376 5.184 5.184 5.376z",c:"#b2b2b2"}},xh=ou({name:"Icon",props:{type:{type:String,required:!0,default:""},size:{type:[String,Number],default:23},color:{type:String,default:""}},setup(e){const t=vi((()=>wh[e.type]));return()=>{const{value:n}=t;return Kr("uni-icon",null,[n&&n.d&&gc(n.d,e.color||n.c,dc(e.size))])}}}),Th={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},Sh={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},Eh={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]},kh=ou({name:"Image",props:Th,setup(e,{emit:t}){const n=tn(null),o=function(e,t){const n=tn(""),o=vi((()=>{let e="auto",o="";const r=Eh[t.mode];return r?(r[0]&&(o=r[0]),r[1]&&(e=r[1])):(o="0% 0%",e="100% 100%"),`background-image:${n.value?'url("'+n.value+'")':"none"};background-position:${o};background-size:${e};`})),r=qt({rootEl:e,src:vi((()=>t.src?vu(t.src):"")),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:o,imgSrc:n});return Ao((()=>{const t=e.value.style;r.origWidth=Number(t.width)||0,r.origHeight=Number(t.height)||0})),r}(n,e),r=cu(n,t),{fixSize:i}=function(e,t,n){const o=()=>{const{mode:o}=t,r=Sh[o];if(!r)return;const{origWidth:i,origHeight:s}=n,a=i&&s?i/s:0;if(!a)return;const l=e.value,c=l[r[0]];c&&(l.style[r[1]]=function(e){Ch&&e>10&&(e=2*Math.round(e/2));return e}(r[2](c,a))+"px")},r=()=>{const{style:t}=e.value,{origStyle:{width:o,height:r}}=n;t.width=o,t.height=r};return Xn((()=>t.mode),((e,t)=>{Sh[t]&&r(),Sh[e]&&o()})),{fixSize:o,resetSize:r}}(n,e,o);return function(e,t,n,o,r){let i,s;const a=(t=0,n=0,o="")=>{e.origWidth=t,e.origHeight=n,e.imgSrc=o},l=l=>{if(!l)return c(),void a();i=i||new Image,i.onload=e=>{const{width:u,height:f}=i;a(u,f,l),o(),i.draggable=t.draggable,s&&s.remove(),s=i,n.value.appendChild(i),c(),r("load",e,{width:u,height:f})},i.onerror=t=>{a(),c(),r("error",t,{errMsg:`GET ${e.src} 404 (Not Found)`})},i.src=l},c=()=>{i&&(i.onload=null,i.onerror=null,i=null)};Xn((()=>e.src),(e=>l(e))),Xn((()=>e.imgSrc),(e=>{!e&&s&&(s.remove(),s=null)})),Ao((()=>l(e.src))),$o((()=>c()))}(o,e,n,i,r),()=>Kr("uni-image",{ref:n},[Kr("div",{style:o.modeStyle},null,4),Sh[e.mode]?Kr(sh,{onResize:i},null,8,["onResize"]):Kr("span",null,null)],512)}});const Ch="Google Inc."===navigator.vendor;const Oh=me(!0),Mh=[];let Ah,Ph=0;const Ih=e=>Mh.forEach((t=>t.userAction=e));function $h(e={userAction:!1}){if(!Ah){["touchstart","touchmove","touchend","mousedown","mouseup"].forEach((e=>{document.addEventListener(e,(function(){!Ph&&Ih(!0),Ph++,setTimeout((()=>{!--Ph&&Ih(!1)}),0)}),Oh)})),Ah=!0}Mh.push(e)}const Dh=()=>!!Ph;function Lh(){const e=qt({userAction:!1});return Ao((()=>{$h(e)})),$o((()=>{!function(e){const t=Mh.indexOf(e);t>=0&&Mh.splice(t,1)}(e)})),{state:e}}function Bh(){const e=qt({attrs:{}});return Ao((()=>{let t=li();for(;t;){const n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&"page"===t.proxy.$mpType?null:t.parent}})),{state:e}}function Fh(e,t){const n=document.activeElement;if(!n)return t({});const o={};["input","textarea"].includes(n.tagName.toLowerCase())&&(o.start=n.selectionStart,o.end=n.selectionEnd),t(o)}function Rh(e,t){return"number"===t&&isNaN(Number(e))&&(e=""),null===e?"":String(e)}const Nh=["none","text","decimal","numeric","tel","search","email","url"],jh=T({},{name:{type:String,default:""},modelValue:{type:[String,Number],default:""},value:{type:[String,Number],default:""},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~Nh.indexOf(e)}},bh),qh=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend","keyboardheightchange"];function zh(e,t,n,o){const r=xe((n=>{t.value=Rh(n,e.type)}),100,{setTimeout:setTimeout,clearTimeout:clearTimeout});Xn((()=>e.modelValue),r),Xn((()=>e.value),r);const i=function(e,t){let n,o,r=0;const i=function(...i){const s=Date.now();clearTimeout(n),o=()=>{o=null,r=s,e.apply(this,i)},s-r<t?n=setTimeout(o,t-(s-r)):o()};return i.cancel=function(){clearTimeout(n),o=null},i.flush=function(){clearTimeout(n),o&&o()},i}(((e,t)=>{r.cancel(),n("update:modelValue",t.value),n("update:value",t.value),o("input",e,t)}),100);return Mo((()=>{r.cancel(),i.cancel()})),{trigger:o,triggerInput:(e,t,n)=>{r.cancel(),i(e,t),n&&i.flush()}}}function Hh(e,t){Lh();const n=vi((()=>e.autoFocus||e.focus));function o(){if(!n.value)return;const e=t.value;e?e.focus():setTimeout(o,100)}Xn((()=>e.focus),(e=>{e?o():function(){const e=t.value;e&&e.blur()}()})),Ao((()=>{n.value&&Tn(o)}))}function Vh(e,t,n,o){$l(_c(),"getSelectedTextRange",Fh);const{fieldRef:r,state:i,trigger:s}=function(e,t,n){const o=tn(null),r=cu(t,n),i=vi((()=>{const t=Number(e.selectionStart);return isNaN(t)?-1:t})),s=vi((()=>{const t=Number(e.selectionEnd);return isNaN(t)?-1:t})),a=vi((()=>{const t=Number(e.cursor);return isNaN(t)?-1:t})),l=vi((()=>{var t=Number(e.maxlength);return isNaN(t)?140:t})),c=Rh(e.modelValue,e.type)||Rh(e.value,e.type),u=qt({value:c,valueOrigin:c,maxlength:l,focus:e.focus,composing:!1,selectionStart:i,selectionEnd:s,cursor:a});return Xn((()=>u.focus),(e=>n("update:focus",e))),Xn((()=>u.maxlength),(e=>u.value=u.value.slice(0,e))),{fieldRef:o,state:u,trigger:r}}(e,t,n),{triggerInput:a}=zh(e,i,n,s);Hh(e,r),_h(0,r);const{state:l}=Bh();!function(e,t){const n=Wn(uu,!1);if(!n)return;const o=li(),r={submit(){const n=o.proxy;return[n[e],I(t)?n[t]:t.value]},reset(){I(t)?o.proxy[t]="":t.value=""}};n.addField(r),$o((()=>{n.removeField(r)}))}("name",i),function(e,t,n,o,r,i){function s(){const n=e.value;n&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&"number"!==n.type&&(n.selectionStart=t.selectionStart,n.selectionEnd=t.selectionEnd)}function a(){const n=e.value;n&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&"number"!==n.type&&(n.selectionEnd=n.selectionStart=t.cursor)}function l(e){return"number"===e.type?null:e.selectionEnd}Xn([()=>t.selectionStart,()=>t.selectionEnd],s),Xn((()=>t.cursor),a),Xn((()=>e.value),(function(){const c=e.value;if(!c)return;const u=function(e,o){e.stopPropagation(),P(i)&&!1===i(e,t)||(t.value=c.value,t.composing&&n.ignoreCompositionEvent||r(e,{value:c.value,cursor:l(c)},o))};function f(e){n.ignoreCompositionEvent||o(e.type,e,{value:e.data})}c.addEventListener("change",(e=>e.stopPropagation())),c.addEventListener("focus",(function(e){t.focus=!0,o("focus",e,{value:t.value}),s(),a()})),c.addEventListener("blur",(function(e){t.composing&&(t.composing=!1,u(e,!0)),t.focus=!1,o("blur",e,{value:t.value,cursor:l(e.target)})})),c.addEventListener("input",u),c.addEventListener("compositionstart",(e=>{e.stopPropagation(),t.composing=!0,f(e)})),c.addEventListener("compositionend",(e=>{e.stopPropagation(),t.composing&&(t.composing=!1,u(e)),f(e)})),c.addEventListener("compositionupdate",f)}))}(r,i,e,s,a,o);return{fieldRef:r,state:i,scopedAttrsState:l,fixDisabledColor:0===String(navigator.vendor).indexOf("Apple")&&CSS.supports("image-orientation:from-image"),trigger:s}}const Wh=ou({name:"Input",props:T({},jh,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),emits:["confirm",...qh],setup(e,{emit:t}){const n=["text","number","idcard","digit","password","tel"],o=["off","one-time-code"],r=vi((()=>{let t="";switch(e.type){case"text":"search"===e.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=~n.includes(e.type)?e.type:"text"}return e.password?"password":t})),i=vi((()=>{const t=o.indexOf(e.textContentType),n=o.indexOf(W(e.textContentType));return o[-1!==t?t:-1!==n?n:0]}));let s,a=tn("");const l=tn(null),{fieldRef:c,state:u,scopedAttrsState:f,fixDisabledColor:d,trigger:h}=Vh(e,l,t,((e,t)=>{const n=e.target;if("number"===r.value){if(s&&(n.removeEventListener("blur",s),s=null),n.validity&&!n.validity.valid){if((!a.value||!n.value)&&"-"===e.data||"-"===a.value[0]&&"deleteContentBackward"===e.inputType)return a.value="-",t.value="",s=()=>{a.value=n.value=""},n.addEventListener("blur",s),!1;if(a.value)if(-1!==a.value.indexOf(".")){if("."!==e.data&&"deleteContentBackward"===e.inputType){const e=a.value.indexOf(".");return a.value=n.value=t.value=a.value.slice(0,e),!0}}else if("."===e.data)return a.value+=".",s=()=>{a.value=n.value=a.value.slice(0,-1)},n.addEventListener("blur",s),!1;return a.value=t.value=n.value="-"===a.value?"":a.value,!1}a.value=n.value;const o=t.maxlength;if(o>0&&n.value.length>o)return n.value=n.value.slice(0,o),t.value=n.value,!1}}));Xn((()=>u.value),(t=>{"number"!==e.type||"-"===a.value&&""===t||(a.value=t)}));const p=["number","digit"],g=vi((()=>p.includes(e.type)?e.step:""));function m(t){if("Enter"!==t.key)return;const n=t.target;t.stopPropagation(),h("confirm",t,{value:n.value}),!e.confirmHold&&n.blur()}return()=>{let t=e.disabled&&d?Kr("input",{key:"disabled-input",ref:c,value:u.value,tabindex:"-1",readonly:!!e.disabled,type:r.value,maxlength:u.maxlength,step:g.value,class:"uni-input-input",onFocus:e=>e.target.blur()},null,40,["value","readonly","type","maxlength","step","onFocus"]):No(Kr("input",{key:"input",ref:c,"onUpdate:modelValue":e=>u.value=e,disabled:!!e.disabled,type:r.value,maxlength:u.maxlength,step:g.value,enterkeyhint:e.confirmType,pattern:"number"===e.type?"[0-9]*":void 0,class:"uni-input-input",autocomplete:i.value,onKeyup:m,inputmode:e.inputmode},null,40,["onUpdate:modelValue","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]),[[gs,u.value]]);return Kr("uni-input",{ref:l},[Kr("div",{class:"uni-input-wrapper"},[No(Kr("div",oi(f.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[_s,!(u.value.length||"-"===a.value)]]),"search"===e.confirmType?Kr("form",{action:"",onSubmit:e=>e.preventDefault(),class:"uni-input-form"},[t],40,["onSubmit"]):t])],512)}}});const Uh=["class","style"],Yh=/^on[A-Z]+/,Xh=(e={})=>{const{excludeListeners:t=!1,excludeKeys:n=[]}=e,o=li(),r=nn({}),i=nn({}),s=nn({}),a=n.concat(Uh);return o.attrs=qt(o.attrs),Un((()=>{const e=(n=o.attrs,Object.keys(n).map((e=>[e,n[e]]))).reduce(((e,[n,o])=>(a.includes(n)?e.exclude[n]=o:Yh.test(n)?(t||(e.attrs[n]=o),e.listeners[n]=o):e.attrs[n]=o,e)),{exclude:{},attrs:{},listeners:{}});var n;r.value=e.attrs,i.value=e.listeners,s.value=e.exclude})),{$attrs:r,$listeners:i,$excludeAttrs:s}};function Kh(e){const t=[];return C(e)&&e.forEach((e=>{Hr(e)?e.type===Pr?t.push(...Kh(e.children)):t.push(e):C(e)&&t.push(...Kh(e))})),t}const Gh=function(e,t,n,o){e.addEventListener(t,(e=>{P(n)&&!1===n(e)&&((void 0===e.cancelable||e.cancelable)&&e.preventDefault(),e.stopPropagation())}),{passive:!1})};let Zh,Jh;function Qh(e,t,n){$o((()=>{document.removeEventListener("mousemove",Zh),document.removeEventListener("mouseup",Jh)}));let o=0,r=0,i=0,s=0;const a=function(e,n,a,l){if(!1===t({cancelable:e.cancelable,target:e.target,currentTarget:e.currentTarget,preventDefault:e.preventDefault.bind(e),stopPropagation:e.stopPropagation.bind(e),touches:e.touches,changedTouches:e.changedTouches,detail:{state:n,x:a,y:l,dx:a-o,dy:l-r,ddx:a-i,ddy:l-s,timeStamp:e.timeStamp}}))return!1};let l,c,u=null;Gh(e,"touchstart",(function(e){if(l=!0,1===e.touches.length&&!u)return u=e,o=i=e.touches[0].pageX,r=s=e.touches[0].pageY,a(e,"start",o,r)})),Gh(e,"mousedown",(function(e){if(c=!0,!l&&!u)return u=e,o=i=e.pageX,r=s=e.pageY,a(e,"start",o,r)})),Gh(e,"touchmove",(function(e){if(1===e.touches.length&&u){const t=a(e,"move",e.touches[0].pageX,e.touches[0].pageY);return i=e.touches[0].pageX,s=e.touches[0].pageY,t}}));const f=Zh=function(e){if(!l&&c&&u){const t=a(e,"move",e.pageX,e.pageY);return i=e.pageX,s=e.pageY,t}};document.addEventListener("mousemove",f),Gh(e,"touchend",(function(e){if(0===e.touches.length&&u)return l=!1,u=null,a(e,"end",e.changedTouches[0].pageX,e.changedTouches[0].pageY)}));const d=Jh=function(e){if(c=!1,!l&&u)return u=null,a(e,"end",e.pageX,e.pageY)};document.addEventListener("mouseup",d),Gh(e,"touchcancel",(function(e){if(u){l=!1;const t=u;return u=null,a(e,n?"cancel":"end",t.touches[0].pageX,t.touches[0].pageY)}}))}const ep=["navigate","redirect","switchTab","reLaunch","navigateBack"],tp=["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"],np=["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"],op={hoverClass:{type:String,default:"navigator-hover"},url:{type:String,default:""},openType:{type:String,default:"navigate",validator:e=>Boolean(~ep.indexOf(e))},delta:{type:Number,default:1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:600},exists:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},animationType:{type:String,default:"",validator:e=>!e||tp.concat(np).includes(e)},animationDuration:{type:[String,Number],default:300}};T({},op,{renderLink:{type:Boolean,default:!0}});const rp=ou({name:"PickerView",props:{value:{type:Array,default:()=>[],validator:function(e){return C(e)&&e.filter((e=>"number"==typeof e)).length===e.length}},indicatorStyle:{type:String,default:""},indicatorClass:{type:String,default:""},maskStyle:{type:String,default:""},maskClass:{type:String,default:""}},emits:["change","pickstart","pickend","update:value"],setup(e,{slots:t,emit:n}){const o=tn(null),r=tn(null),i=cu(o,n),s=function(e){const t=qt([...e.value]),n=qt({value:t,height:34});return Xn((()=>e.value),((e,t)=>{n.value.length=e.length,e.forEach(((e,t)=>{e!==n.value[t]&&n.value.splice(t,1,e)}))})),n}(e),a=tn(null);Ao((()=>{const e=a.value;e&&(s.height=e.$el.offsetHeight)}));let l=tn([]),c=tn([]);function u(e){let t=c.value;t=t.filter((e=>e.type!==$r));let n=t.indexOf(e);return-1!==n?n:l.value.indexOf(e)}return Vn("getPickerViewColumn",(function(e){return vi({get(){const t=u(e.vnode);return s.value[t]||0},set(t){const o=u(e.vnode);if(o<0)return;if(s.value[o]!==t){s.value[o]=t;const e=s.value.map((e=>e));n("update:value",e),i("change",{},{value:e})}}})})),Vn("pickerViewProps",e),Vn("pickerViewState",s),()=>{const e=t.default&&t.default();{const t=Kh(e);l.value=t,Tn((()=>{c.value=t}))}return Kr("uni-picker-view",{ref:o},[Kr(sh,{ref:a,onResize:({height:e})=>s.height=e},null,8,["onResize"]),Kr("div",{ref:r,class:"uni-picker-view-wrapper"},[e],512)],512)}}});class ip{constructor(e){this._drag=e,this._dragLog=Math.log(e),this._x=0,this._v=0,this._startTime=0}set(e,t){this._x=e,this._v=t,this._startTime=(new Date).getTime()}setVelocityByEnd(e){this._v=(e-this._x)*this._dragLog/(Math.pow(this._drag,100)-1)}x(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._x+this._v*t/this._dragLog-this._v/this._dragLog}dx(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._v*t}done(){return Math.abs(this.dx())<3}reconfigure(e){const t=this.x(),n=this.dx();this._drag=e,this._dragLog=Math.log(e),this.set(t,n)}configuration(){const e=this;return[{label:"Friction",read:function(){return e._drag},write:function(t){e.reconfigure(t)},min:.001,max:.1,step:.001}]}}function sp(e,t,n){return e>t-n&&e<t+n}function ap(e,t){return sp(e,0,t)}class lp{constructor(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}_solve(e,t){const n=this._c,o=this._m,r=this._k,i=n*n-4*o*r;if(0===i){const r=-n/(2*o),i=e,s=t/(r*e);return{x:function(e){return(i+s*e)*Math.pow(Math.E,r*e)},dx:function(e){const t=Math.pow(Math.E,r*e);return r*(i+s*e)*t+s*t}}}if(i>0){const r=(-n-Math.sqrt(i))/(2*o),s=(-n+Math.sqrt(i))/(2*o),a=(t-r*e)/(s-r),l=e-a;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*t+a*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*r*t+a*s*n}}}const s=Math.sqrt(4*o*r-n*n)/(2*o),a=-n/2*o,l=e,c=(t-a*e)/s;return{x:function(e){return Math.pow(Math.E,a*e)*(l*Math.cos(s*e)+c*Math.sin(s*e))},dx:function(e){const t=Math.pow(Math.E,a*e),n=Math.cos(s*e),o=Math.sin(s*e);return t*(c*s*n-l*s*o)+a*t*(c*o+l*n)}}}x(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0}dx(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0}setEnd(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!ap(t,.4)){t=t||0;let o=this._endPosition;this._solution&&(ap(t,.4)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),ap(t,.4)&&(t=0),ap(o,.4)&&(o=0),o+=this._endPosition),this._solution&&ap(o-e,.4)&&ap(t,.4)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}}snap(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}}done(e){return e||(e=(new Date).getTime()),sp(this.x(),this._endPosition,.4)&&ap(this.dx(),.4)}reconfigure(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())}springConstant(){return this._k}damping(){return this._c}configuration(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]}}class cp{constructor(e,t,n){this._extent=e,this._friction=t||new ip(.01),this._spring=n||new lp(1,90,20),this._startTime=0,this._springing=!1,this._springOffset=0}snap(e,t){this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(t)}set(e,t){this._friction.set(e,t),e>0&&t>=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(0)):e<-this._extent&&t<=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(-this._extent)):this._springing=!1,this._startTime=(new Date).getTime()}x(e){if(!this._startTime)return 0;if(e||(e=((new Date).getTime()-this._startTime)/1e3),this._springing)return this._spring.x()+this._springOffset;let t=this._friction.x(e),n=this.dx(e);return(t>0&&n>=0||t<-this._extent&&n<=0)&&(this._springing=!0,this._spring.setEnd(0,n),t<-this._extent?this._springOffset=-this._extent:this._springOffset=0,t=this._spring.x()+this._springOffset),t}dx(e){let t;return t=this._lastTime===e?this._lastDx:this._springing?this._spring.dx(e):this._friction.dx(e),this._lastTime=e,this._lastDx=t,t}done(){return this._springing?this._spring.done():this._friction.done()}setVelocityByEnd(e){this._friction.setVelocityByEnd(e)}configuration(){const e=this._friction.configuration();return e.push.apply(e,this._spring.configuration()),e}}class up{constructor(e,t){t=t||{},this._element=e,this._options=t,this._enableSnap=t.enableSnap||!1,this._itemSize=t.itemSize||0,this._enableX=t.enableX||!1,this._enableY=t.enableY||!1,this._shouldDispatchScrollEvent=!!t.onScroll,this._enableX?(this._extent=(t.scrollWidth||this._element.offsetWidth)-this._element.parentElement.offsetWidth,this._scrollWidth=t.scrollWidth):(this._extent=(t.scrollHeight||this._element.offsetHeight)-this._element.parentElement.offsetHeight,this._scrollHeight=t.scrollHeight),this._position=0,this._scroll=new cp(this._extent,t.friction,t.spring),this._onTransitionEnd=this.onTransitionEnd.bind(this),this.updatePosition()}onTouchStart(){this._startPosition=this._position,this._lastChangePos=this._startPosition,this._startPosition>0?this._startPosition/=.5:this._startPosition<-this._extent&&(this._startPosition=(this._startPosition+this._extent)/.5-this._extent),this._animation&&(this._animation.cancel(),this._scrolling=!1),this.updatePosition()}onTouchMove(e,t){let n=this._startPosition;this._enableX?n+=e:this._enableY&&(n+=t),n>0?n*=.5:n<-this._extent&&(n=.5*(n+this._extent)-this._extent),this._position=n,this.updatePosition(),this.dispatchScroll()}onTouchEnd(e,t,n){if(this._enableSnap&&this._position>-this._extent&&this._position<0){if(this._enableY&&(Math.abs(t)<this._itemSize&&Math.abs(n.y)<300||Math.abs(n.y)<150))return void this.snap();if(this._enableX&&(Math.abs(e)<this._itemSize&&Math.abs(n.x)<300||Math.abs(n.x)<150))return void this.snap()}let o;if(this._enableX?this._scroll.set(this._position,n.x):this._enableY&&this._scroll.set(this._position,n.y),this._enableSnap){const e=this._scroll._friction.x(100),t=e%this._itemSize;o=Math.abs(t)>this._itemSize/2?e-(this._itemSize-Math.abs(t)):e-t,o<=0&&o>=-this._extent&&this._scroll.setVelocityByEnd(o)}this._lastTime=Date.now(),this._lastDelay=0,this._scrolling=!0,this._lastChangePos=this._position,this._lastIdx=Math.floor(Math.abs(this._position/this._itemSize)),this._animation=function(e,t,n){const o={id:0,cancelled:!1};return function e(t,n,o,r){if(!t||!t.cancelled){o(n);const i=n.done();i||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,r))),i&&r&&r(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}(this._scroll,(()=>{const e=Date.now(),t=(e-this._scroll._startTime)/1e3,n=this._scroll.x(t);this._position=n,this.updatePosition();const o=this._scroll.dx(t);this._shouldDispatchScrollEvent&&e-this._lastTime>this._lastDelay&&(this.dispatchScroll(),this._lastDelay=Math.abs(2e3/o),this._lastTime=e)}),(()=>{this._enableSnap&&(o<=0&&o>=-this._extent&&(this._position=o,this.updatePosition()),P(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._shouldDispatchScrollEvent&&this.dispatchScroll(),this._scrolling=!1}))}onTransitionEnd(){this._element.style.webkitTransition="",this._element.style.transition="",this._element.removeEventListener("transitionend",this._onTransitionEnd),this._snapping&&(this._snapping=!1),this.dispatchScroll()}snap(){const e=this._itemSize,t=this._position%e,n=Math.abs(t)>this._itemSize/2?this._position-(e-Math.abs(t)):this._position-t;this._position!==n&&(this._snapping=!0,this.scrollTo(-n),P(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize)))}scrollTo(e,t){this._animation&&(this._animation.cancel(),this._scrolling=!1),"number"==typeof e&&(this._position=-e),this._position<-this._extent?this._position=-this._extent:this._position>0&&(this._position=0);const n="transform "+(t||.2)+"s ease-out";this._element.style.webkitTransition="-webkit-"+n,this._element.style.transition=n,this.updatePosition(),this._element.addEventListener("transitionend",this._onTransitionEnd)}dispatchScroll(){if(P(this._options.onScroll)&&Math.round(Number(this._lastPos))!==Math.round(this._position)){this._lastPos=this._position;const e={target:{scrollLeft:this._enableX?-this._position:0,scrollTop:this._enableY?-this._position:0,scrollHeight:this._scrollHeight||this._element.offsetHeight,scrollWidth:this._scrollWidth||this._element.offsetWidth,offsetHeight:this._element.parentElement.offsetHeight,offsetWidth:this._element.parentElement.offsetWidth}};this._options.onScroll(e)}}update(e,t,n){let o=0;const r=this._position;this._enableX?(o=this._element.childNodes.length?(t||this._element.offsetWidth)-this._element.parentElement.offsetWidth:0,this._scrollWidth=t):(o=this._element.childNodes.length?(t||this._element.offsetHeight)-this._element.parentElement.offsetHeight:0,this._scrollHeight=t),"number"==typeof e&&(this._position=-e),this._position<-o?this._position=-o:this._position>0&&(this._position=0),this._itemSize=n||this._itemSize,this.updatePosition(),r!==this._position&&(this.dispatchScroll(),P(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._extent=o,this._scroll._extent=o}updatePosition(){let e="";this._enableX?e="translateX("+this._position+"px) translateZ(0)":this._enableY&&(e="translateY("+this._position+"px) translateZ(0)"),this._element.style.webkitTransform=e,this._element.style.transform=e}isScrolling(){return this._scrolling||this._snapping}}let fp=0;const dp=ou({name:"PickerViewColumn",setup(e,{slots:t,emit:n}){const o=tn(null),r=tn(null),i=Wn("getPickerViewColumn"),s=li(),a=i?i(s):tn(0),l=Wn("pickerViewProps"),c=Wn("pickerViewState"),u=tn(34),f=tn(null);Ao((()=>{const e=f.value;u.value=e.$el.offsetHeight}));const d=vi((()=>(c.height-u.value)/2)),{state:h}=Bh(),p=function(e){const t="uni-picker-view-content-"+fp++;return Xn((()=>e.value),(function(){const n=document.createElement("style");n.innerText=`.uni-picker-view-content.${t}>*{height: ${e.value}px;overflow: hidden;}`,document.head.appendChild(n)})),t}(u);let g;const m=qt({current:a.value,length:0});let v;function y(){g&&!v&&(v=!0,Tn((()=>{v=!1;let e=Math.min(m.current,m.length-1);e=Math.max(e,0),g.update(e*u.value,void 0,u.value)})))}Xn((()=>a.value),(e=>{e!==m.current&&(m.current=e,y())})),Xn((()=>m.current),(e=>a.value=e)),Xn([()=>u.value,()=>m.length,()=>c.height],y);let b=0;function _(e){const t=b+e.deltaY;if(Math.abs(t)>10){b=0;let e=Math.min(m.current+(t<0?-1:1),m.length-1);m.current=e=Math.max(e,0),g.scrollTo(e*u.value)}else b=t;e.preventDefault()}function w({clientY:e}){const t=o.value;if(!g.isScrolling()){const n=e-t.getBoundingClientRect().top-c.height/2,o=u.value/2;if(!(Math.abs(n)<=o)){const e=Math.ceil((Math.abs(n)-o)/u.value),t=n<0?-e:e;let r=Math.min(m.current+t,m.length-1);m.current=r=Math.max(r,0),g.scrollTo(r*u.value)}}}const x=()=>{const e=o.value,t=r.value,{scroller:n,handleTouchStart:i,handleTouchMove:s,handleTouchEnd:a}=function(e,t){const n={trackingID:-1,maxDy:0,maxDx:0},o=new up(e,t);function r(e){const t=e,o=e;return"move"===t.detail.state||"end"===t.detail.state?{x:t.detail.dx,y:t.detail.dy}:{x:o.screenX-n.x,y:o.screenY-n.y}}return{scroller:o,handleTouchStart:function(e){const t=e,r=e;"start"===t.detail.state?(n.trackingID="touch",n.x=t.detail.x,n.y=t.detail.y):(n.trackingID="mouse",n.x=r.screenX,n.y=r.screenY),n.maxDx=0,n.maxDy=0,n.historyX=[0],n.historyY=[0],n.historyTime=[t.detail.timeStamp||r.timeStamp],n.listener=o,o.onTouchStart&&o.onTouchStart(),("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault()},handleTouchMove:function(e){const t=e,o=e;if(-1!==n.trackingID){("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault();const i=r(e);if(i){for(n.maxDy=Math.max(n.maxDy,Math.abs(i.y)),n.maxDx=Math.max(n.maxDx,Math.abs(i.x)),n.historyX.push(i.x),n.historyY.push(i.y),n.historyTime.push(t.detail.timeStamp||o.timeStamp);n.historyTime.length>10;)n.historyTime.shift(),n.historyX.shift(),n.historyY.shift();n.listener&&n.listener.onTouchMove&&n.listener.onTouchMove(i.x,i.y)}}},handleTouchEnd:function(e){if(-1!==n.trackingID){e.preventDefault();const t=r(e);if(t){const e=n.listener;n.trackingID=-1,n.listener=null;const o={x:0,y:0};if(n.historyTime.length>2)for(let t=n.historyTime.length-1,r=n.historyTime[t],i=n.historyX[t],s=n.historyY[t];t>0;){t--;const e=r-n.historyTime[t];if(e>30&&e<50){o.x=(i-n.historyX[t])/(e/1e3),o.y=(s-n.historyY[t])/(e/1e3);break}}n.historyTime=[],n.historyX=[],n.historyY=[],e&&e.onTouchEnd&&e.onTouchEnd(t.x,t.y,o)}}}}}(t,{enableY:!0,enableX:!1,enableSnap:!0,itemSize:u.value,friction:new ip(1e-4),spring:new lp(2,90,20),onSnap:e=>{isNaN(e)||e===m.current||(m.current=e)}});g=n,Qh(e,(e=>{switch(e.detail.state){case"start":i(e);break;case"move":s(e),e.stopPropagation();break;case"end":case"cancel":a(e)}}),!0),function(e){let t=0,n=0;e.addEventListener("touchstart",(e=>{const o=e.changedTouches[0];t=o.clientX,n=o.clientY})),e.addEventListener("touchend",(e=>{const o=e.changedTouches[0];if(Math.abs(o.clientX-t)<20&&Math.abs(o.clientY-n)<20){const t={bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget},n=new CustomEvent("click",t);["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((e=>{n[e]=o[e]})),e.target.dispatchEvent(n)}}))}(e),y()};return Ao(x),()=>{const e=t.default&&t.default();m.length=Kh(e).length;const n=`${d.value}px 0`;return Kr("uni-picker-view-column",{ref:o},[Kr("div",{onWheel:_,onClick:w,class:"uni-picker-view-group"},[Kr("div",oi(h.attrs,{class:["uni-picker-view-mask",l.maskClass],style:`background-size: 100% ${d.value}px;${l.maskStyle}`}),null,16),Kr("div",oi(h.attrs,{class:["uni-picker-view-indicator",l.indicatorClass],style:l.indicatorStyle}),[Kr(sh,{ref:f,onResize:({height:e})=>u.value=e},null,8,["onResize"])],16),Kr("div",{ref:r,class:["uni-picker-view-content",p],style:{padding:n}},[e],6)],40,["onWheel","onClick"])],512)}}}),hp=me(!0),pp=ou({name:"ScrollView",compatConfig:{MODE:3},props:{scrollX:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},upperThreshold:{type:[Number,String],default:50},lowerThreshold:{type:[Number,String],default:50},scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},scrollIntoView:{type:String,default:""},scrollWithAnimation:{type:[Boolean,String],default:!1},enableBackToTop:{type:[Boolean,String],default:!1},refresherEnabled:{type:[Boolean,String],default:!1},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"back"},refresherBackground:{type:String,default:"#fff"},refresherTriggered:{type:[Boolean,String],default:!1}},emits:["scroll","scrolltoupper","scrolltolower","refresherrefresh","refresherrestore","refresherpulling","refresherabort","update:refresherTriggered"],setup(e,{emit:t,slots:n}){const o=tn(null),r=tn(null),i=tn(null),s=tn(null),a=tn(null),l=cu(o,t),{state:c,scrollTopNumber:u,scrollLeftNumber:f}=function(e){const t=vi((()=>Number(e.scrollTop)||0)),n=vi((()=>Number(e.scrollLeft)||0));return{state:qt({lastScrollTop:t.value,lastScrollLeft:n.value,lastScrollToUpperTime:0,lastScrollToLowerTime:0,refresherHeight:0,refreshRotate:0,refreshState:""}),scrollTopNumber:t,scrollLeftNumber:n}}(e);!function(e,t,n,o,r,i,s,a,l){let c=!1,u=0,f=!1,d=()=>{};const h=vi((()=>{let t=Number(e.upperThreshold);return isNaN(t)?50:t})),p=vi((()=>{let t=Number(e.lowerThreshold);return isNaN(t)?50:t}));function g(e,t){const n=s.value;let o=0,r="";if(e<0?e=0:"x"===t&&e>n.scrollWidth-n.offsetWidth?e=n.scrollWidth-n.offsetWidth:"y"===t&&e>n.scrollHeight-n.offsetHeight&&(e=n.scrollHeight-n.offsetHeight),"x"===t?o=n.scrollLeft-e:"y"===t&&(o=n.scrollTop-e),0===o)return;let i=a.value;i.style.transition="transform .3s ease-out",i.style.webkitTransition="-webkit-transform .3s ease-out","x"===t?r="translateX("+o+"px) translateZ(0)":"y"===t&&(r="translateY("+o+"px) translateZ(0)"),i.removeEventListener("transitionend",d),i.removeEventListener("webkitTransitionEnd",d),d=()=>_(e,t),i.addEventListener("transitionend",d),i.addEventListener("webkitTransitionEnd",d),"x"===t?n.style.overflowX="hidden":"y"===t&&(n.style.overflowY="hidden"),i.style.transform=r,i.style.webkitTransform=r}function m(n){const o=n.target;r("scroll",n,{scrollLeft:o.scrollLeft,scrollTop:o.scrollTop,scrollHeight:o.scrollHeight,scrollWidth:o.scrollWidth,deltaX:t.lastScrollLeft-o.scrollLeft,deltaY:t.lastScrollTop-o.scrollTop}),e.scrollY&&(o.scrollTop<=h.value&&t.lastScrollTop-o.scrollTop>0&&n.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",n,{direction:"top"}),t.lastScrollToUpperTime=n.timeStamp),o.scrollTop+o.offsetHeight+p.value>=o.scrollHeight&&t.lastScrollTop-o.scrollTop<0&&n.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",n,{direction:"bottom"}),t.lastScrollToLowerTime=n.timeStamp)),e.scrollX&&(o.scrollLeft<=h.value&&t.lastScrollLeft-o.scrollLeft>0&&n.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",n,{direction:"left"}),t.lastScrollToUpperTime=n.timeStamp),o.scrollLeft+o.offsetWidth+p.value>=o.scrollWidth&&t.lastScrollLeft-o.scrollLeft<0&&n.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",n,{direction:"right"}),t.lastScrollToLowerTime=n.timeStamp)),t.lastScrollTop=o.scrollTop,t.lastScrollLeft=o.scrollLeft}function v(t){e.scrollY&&(e.scrollWithAnimation?g(t,"y"):s.value.scrollTop=t)}function y(t){e.scrollX&&(e.scrollWithAnimation?g(t,"x"):s.value.scrollLeft=t)}function b(t){if(t){if(!/^[_a-zA-Z][-_a-zA-Z0-9:]*$/.test(t))return void console.error(`id error: scroll-into-view=${t}`);let n=i.value.querySelector("#"+t);if(n){let t=s.value.getBoundingClientRect(),o=n.getBoundingClientRect();if(e.scrollX){let n=o.left-t.left,r=s.value.scrollLeft+n;e.scrollWithAnimation?g(r,"x"):s.value.scrollLeft=r}if(e.scrollY){let n=o.top-t.top,r=s.value.scrollTop+n;e.scrollWithAnimation?g(r,"y"):s.value.scrollTop=r}}}}function _(t,n){a.value.style.transition="",a.value.style.webkitTransition="",a.value.style.transform="",a.value.style.webkitTransform="";let o=s.value;"x"===n?(o.style.overflowX=e.scrollX?"auto":"hidden",o.scrollLeft=t):"y"===n&&(o.style.overflowY=e.scrollY?"auto":"hidden",o.scrollTop=t),a.value.removeEventListener("transitionend",d),a.value.removeEventListener("webkitTransitionEnd",d)}function w(n){if(e.refresherEnabled){switch(n){case"refreshing":t.refresherHeight=e.refresherThreshold,c||(c=!0,r("refresherrefresh",{},{}),l("update:refresherTriggered",!0));break;case"restore":case"refresherabort":c=!1,t.refresherHeight=u=0,"restore"===n&&(f=!1,r("refresherrestore",{},{})),"refresherabort"===n&&f&&(f=!1,r("refresherabort",{},{}))}t.refreshState=n}}Ao((()=>{Tn((()=>{v(n.value),y(o.value)})),b(e.scrollIntoView);let i=function(e){e.preventDefault(),e.stopPropagation(),m(e)},a={x:0,y:0},l=null,d=function(n){if(null===a)return;let o=n.touches[0].pageX,i=n.touches[0].pageY,d=s.value;if(Math.abs(o-a.x)>Math.abs(i-a.y))if(e.scrollX){if(0===d.scrollLeft&&o>a.x)return void(l=!1);if(d.scrollWidth===d.offsetWidth+d.scrollLeft&&o<a.x)return void(l=!1);l=!0}else l=!1;else if(e.scrollY)if(0===d.scrollTop&&i>a.y)l=!1,e.refresherEnabled&&!1!==n.cancelable&&n.preventDefault();else{if(d.scrollHeight===d.offsetHeight+d.scrollTop&&i<a.y)return void(l=!1);l=!0}else l=!1;if(l&&n.stopPropagation(),0===d.scrollTop&&1===n.touches.length&&w("pulling"),e.refresherEnabled&&"pulling"===t.refreshState){const o=i-a.y;0===u&&(u=i),c?(t.refresherHeight=o+e.refresherThreshold,f=!1):(t.refresherHeight=i-u,t.refresherHeight>0&&(f=!0,r("refresherpulling",n,{deltaY:o})));const s=t.refresherHeight/e.refresherThreshold;t.refreshRotate=360*(s>1?1:s)}},h=function(e){1===e.touches.length&&(a={x:e.touches[0].pageX,y:e.touches[0].pageY})},p=function(n){a=null,t.refresherHeight>=e.refresherThreshold?w("refreshing"):w("refresherabort")};s.value.addEventListener("touchstart",h,hp),s.value.addEventListener("touchmove",d,me(!1)),s.value.addEventListener("scroll",i,me(!1)),s.value.addEventListener("touchend",p,hp),$o((()=>{s.value.removeEventListener("touchstart",h),s.value.removeEventListener("touchmove",d),s.value.removeEventListener("scroll",i),s.value.removeEventListener("touchend",p)}))})),yo((()=>{e.scrollY&&(s.value.scrollTop=t.lastScrollTop),e.scrollX&&(s.value.scrollLeft=t.lastScrollLeft)})),Xn(n,(e=>{v(e)})),Xn(o,(e=>{y(e)})),Xn((()=>e.scrollIntoView),(e=>{b(e)})),Xn((()=>e.refresherTriggered),(e=>{!0===e?w("refreshing"):!1===e&&w("restore")}))}(e,c,u,f,l,o,r,s,t);const d=vi((()=>{let t="";return e.scrollX?t+="overflow-x:auto;":t+="overflow-x:hidden;",e.scrollY?t+="overflow-y:auto;":t+="overflow-y:hidden;",t}));return()=>{const{refresherEnabled:t,refresherBackground:l,refresherDefaultStyle:u}=e,{refresherHeight:f,refreshState:h,refreshRotate:p}=c;return Kr("uni-scroll-view",{ref:o},[Kr("div",{ref:i,class:"uni-scroll-view"},[Kr("div",{ref:r,style:d.value,class:"uni-scroll-view"},[Kr("div",{ref:s,class:"uni-scroll-view-content"},[t?Kr("div",{ref:a,style:{backgroundColor:l,height:f+"px"},class:"uni-scroll-view-refresher"},["none"!==u?Kr("div",{class:"uni-scroll-view-refresh"},[Kr("div",{class:"uni-scroll-view-refresh-inner"},["pulling"==h?Kr("svg",{key:"refresh__icon",style:{transform:"rotate("+p+"deg)"},fill:"#2BD009",class:"uni-scroll-view-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},[Kr("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null),Kr("path",{d:"M0 0h24v24H0z",fill:"none"},null)],4):null,"refreshing"==h?Kr("svg",{key:"refresh__spinner",class:"uni-scroll-view-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},[Kr("circle",{cx:"50",cy:"50",r:"20",fill:"none",style:"color: #2bd009","stroke-width":"3"},null)]):null])]):null,"none"==u?n.refresher&&n.refresher():null],4):null,n.default&&n.default()],512)],4)],512)],512)}}});function gp(e,t,n,o,r,i){function s(){c&&(clearTimeout(c),c=null)}let a,l,c=null,u=!0,f=0,d=1,h=null,p=!1,g=0,m="";const v=vi((()=>n.value.length>t.displayMultipleItems)),y=vi((()=>e.circular&&v.value));function b(r){Math.floor(2*f)===Math.floor(2*r)&&Math.ceil(2*f)===Math.ceil(2*r)||y.value&&function(o){if(!u)for(let r=n.value,i=r.length,s=o+t.displayMultipleItems,a=0;a<i;a++){const t=r[a],n=Math.floor(o/i)*i+a,l=n+i,c=n-i,u=Math.max(o-(n+1),n-s,0),f=Math.max(o-(l+1),l-s,0),d=Math.max(o-(c+1),c-s,0),h=Math.min(u,f,d),p=[n,l,c][[u,f,d].indexOf(h)];t.updatePosition(p,e.vertical)}}(r);const s="translate("+(e.vertical?"0":100*-r*d+"%")+", "+(e.vertical?100*-r*d+"%":"0")+") translateZ(0)",l=o.value;if(l&&(l.style.webkitTransform=s,l.style.transform=s),f=r,!a){if(r%1==0)return;a=r}r-=Math.floor(a);const c=n.value;r<=-(c.length-1)?r+=c.length:r>=c.length&&(r-=c.length),r=a%1>.5||a<0?r-1:r,i("transition",{},{dx:e.vertical?0:r*l.offsetWidth,dy:e.vertical?r*l.offsetHeight:0})}function _(e){const o=n.value.length;if(!o)return-1;const r=(Math.round(e)%o+o)%o;if(y.value){if(o<=t.displayMultipleItems)return 0}else if(r>o-t.displayMultipleItems)return o-t.displayMultipleItems;return r}function w(){h=null}function x(){if(!h)return void(p=!1);const e=h,o=e.toPos,r=e.acc,s=e.endTime,c=e.source,u=s-Date.now();if(u<=0){b(o),h=null,p=!1,a=null;const e=n.value[t.current];if(e){const n=e.getItemId();i("animationfinish",{},{current:t.current,currentItemId:n,source:c})}return}b(o+r*u*u/2),l=requestAnimationFrame(x)}function T(e,o,r){w();const i=t.duration,s=n.value.length;let a=f;if(y.value)if(r<0){for(;a<e;)a+=s;for(;a-s>e;)a-=s}else if(r>0){for(;a>e;)a-=s;for(;a+s<e;)a+=s;a+s-e<e-a&&(a+=s)}else{for(;a+s<e;)a+=s;for(;a-s>e;)a-=s;a+s-e<e-a&&(a+=s)}else"click"===o&&(e=e+t.displayMultipleItems-1<s?e:0);h={toPos:e,acc:2*(a-e)/(i*i),endTime:Date.now()+i,source:o},p||(p=!0,l=requestAnimationFrame(x))}function S(){s();const e=n.value,o=function(){c=null,m="autoplay",y.value?t.current=_(t.current+1):t.current=t.current+t.displayMultipleItems<e.length?t.current+1:0,T(t.current,"autoplay",y.value?1:0),c=setTimeout(o,t.interval)};u||e.length<=t.displayMultipleItems||(c=setTimeout(o,t.interval))}function E(e){e?S():s()}return Xn([()=>e.current,()=>e.currentItemId,()=>[...n.value]],(()=>{let o=-1;if(e.currentItemId)for(let t=0,r=n.value;t<r.length;t++){if(r[t].getItemId()===e.currentItemId){o=t;break}}o<0&&(o=Math.round(e.current)||0),o=o<0?0:o,t.current!==o&&(m="",t.current=o)})),Xn([()=>e.vertical,()=>y.value,()=>t.displayMultipleItems,()=>[...n.value]],(function(){s(),h&&(b(h.toPos),h=null);const r=n.value;for(let t=0;t<r.length;t++)r[t].updatePosition(t,e.vertical);d=1;const i=o.value;if(1===t.displayMultipleItems&&r.length){const e=r[0].getBoundingClientRect(),t=i.getBoundingClientRect();d=e.width/t.width,d>0&&d<1||(d=1)}const a=f;f=-2;const l=t.current;l>=0?(u=!1,t.userTracking?(b(a+l-g),g=l):(b(l),e.autoplay&&S())):(u=!0,b(-t.displayMultipleItems-1))})),Xn((()=>t.interval),(()=>{c&&(s(),S())})),Xn((()=>t.current),((e,o)=>{!function(e,o){const r=m;m="";const s=n.value;if(!r){const t=s.length;T(e,"",y.value&&o+(t-e)%t>t/2?1:0)}const a=s[e];if(a){const e=t.currentItemId=a.getItemId();i("change",{},{current:t.current,currentItemId:e,source:r})}}(e,o),r("update:current",e)})),Xn((()=>t.currentItemId),(e=>{r("update:currentItemId",e)})),Xn((()=>e.autoplay&&!t.userTracking),E),E(e.autoplay&&!t.userTracking),Ao((()=>{let r=!1,i=0,a=0;function l(e){t.userTracking=!1;const n=i/Math.abs(i);let o=0;!e&&Math.abs(i)>.2&&(o=.5*n);const r=_(f+o);e?b(g):(m="touch",t.current=r,T(r,"touch",0!==o?o:0===r&&y.value&&f>=1?1:0))}Qh(o.value,(c=>{if(!e.disableTouch&&!u){if("start"===c.detail.state)return t.userTracking=!0,r=!1,s(),g=f,i=0,a=Date.now(),void w();if("end"===c.detail.state)return l(!1);if("cancel"===c.detail.state)return l(!0);if(t.userTracking){if(!r){r=!0;const n=Math.abs(c.detail.dx),o=Math.abs(c.detail.dy);if((n>=o&&e.vertical||n<=o&&!e.vertical)&&(t.userTracking=!1),!t.userTracking)return void(e.autoplay&&S())}return function(r){const s=a;a=Date.now();const l=n.value.length-t.displayMultipleItems;function c(e){return.5-.25/(e+.5)}function u(e,t){let n=g+e;i=.6*i+.4*t,y.value||(n<0||n>l)&&(n<0?n=-c(-n):n>l&&(n=l+c(n-l)),i=0),b(n)}const f=a-s||1,d=o.value;e.vertical?u(-r.dy/d.offsetHeight,-r.ddy/f):u(-r.dx/d.offsetWidth,-r.ddx/f)}(c.detail),!1}}}))})),Do((()=>{s(),cancelAnimationFrame(l)})),{onSwiperDotClick:function(e){T(t.current=e,m="click",y.value?1:0)},circularEnabled:y,swiperEnabled:v}}const mp=ou({name:"Swiper",props:{indicatorDots:{type:[Boolean,String],default:!1},vertical:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},circular:{type:[Boolean,String],default:!1},interval:{type:[Number,String],default:5e3},duration:{type:[Number,String],default:500},current:{type:[Number,String],default:0},indicatorColor:{type:String,default:""},indicatorActiveColor:{type:String,default:""},previousMargin:{type:String,default:""},nextMargin:{type:String,default:""},currentItemId:{type:String,default:""},skipHiddenItemLayout:{type:[Boolean,String],default:!1},displayMultipleItems:{type:[Number,String],default:1},disableTouch:{type:[Boolean,String],default:!1},navigation:{type:[Boolean,String],default:!1},navigationColor:{type:String,default:"#fff"},navigationActiveColor:{type:String,default:"rgba(53, 53, 53, 0.6)"}},emits:["change","transition","animationfinish","update:current","update:currentItemId"],setup(e,{slots:t,emit:n}){const o=tn(null),r=cu(o,n),i=tn(null),s=tn(null),a=function(e){return qt({interval:vi((()=>{const t=Number(e.interval);return isNaN(t)?5e3:t})),duration:vi((()=>{const t=Number(e.duration);return isNaN(t)?500:t})),displayMultipleItems:vi((()=>{const t=Math.round(e.displayMultipleItems);return isNaN(t)?1:t})),current:Math.round(e.current)||0,currentItemId:e.currentItemId,userTracking:!1})}(e),l=vi((()=>{let t={};return(e.nextMargin||e.previousMargin)&&(t=e.vertical?{left:0,right:0,top:dc(e.previousMargin,!0),bottom:dc(e.nextMargin,!0)}:{top:0,bottom:0,left:dc(e.previousMargin,!0),right:dc(e.nextMargin,!0)}),t})),c=vi((()=>{const t=Math.abs(100/a.displayMultipleItems)+"%";return{width:e.vertical?"100%":t,height:e.vertical?t:"100%"}}));let u=[];const f=[],d=tn([]);function h(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n instanceof Element||(n=n.el);const o=f.find((e=>n===e.rootRef.value));o&&e.push(Kt(o))}d.value=e}Vn("addSwiperContext",(function(e){f.push(e),h()}));Vn("removeSwiperContext",(function(e){const t=f.indexOf(e);t>=0&&(f.splice(t,1),h())}));const{onSwiperDotClick:p,circularEnabled:g,swiperEnabled:m}=gp(e,a,d,s,n,r);let v=()=>null;return v=vp(o,e,a,p,d,g,m),()=>{const n=t.default&&t.default();return u=Kh(n),Kr("uni-swiper",{ref:o},[Kr("div",{ref:i,class:"uni-swiper-wrapper"},[Kr("div",{class:"uni-swiper-slides",style:l.value},[Kr("div",{ref:s,class:"uni-swiper-slide-frame",style:c.value},[n],4)],4),e.indicatorDots&&Kr("div",{class:["uni-swiper-dots",e.vertical?"uni-swiper-dots-vertical":"uni-swiper-dots-horizontal"]},[d.value.map(((t,n,o)=>Kr("div",{onClick:()=>p(n),class:{"uni-swiper-dot":!0,"uni-swiper-dot-active":n<a.current+a.displayMultipleItems&&n>=a.current||n<a.current+a.displayMultipleItems-o.length},style:{background:n===a.current?e.indicatorActiveColor:e.indicatorColor}},null,14,["onClick"])))],2),v()],512)],512)}}}),vp=(e,t,n,o,r,i,s)=>{let a=!1,l=!1,c=!1,u=tn(!1);function f(e,n){const o=e.currentTarget;o&&(o.style.backgroundColor="over"===n?t.navigationActiveColor:"")}Un((()=>{a="auto"===t.navigation,u.value=!0!==t.navigation||a,y()})),Un((()=>{const e=r.value.length,t=!i.value;l=0===n.current&&t,c=n.current===e-1&&t||t&&n.current+n.displayMultipleItems>=e,s.value||(l=!0,c=!0,a&&(u.value=!0))}));const d={onMouseover:e=>f(e,"over"),onMouseout:e=>f(e,"out")};function h(e,t,s){if(e.stopPropagation(),s)return;const a=r.value.length;let l=n.current;switch(t){case"prev":l--,l<0&&i.value&&(l=a-1);break;case"next":l++,l>=a&&i.value&&(l=0)}o(l)}const p=()=>gc("M21.781 7.844l-9.063 8.594 9.063 8.594q0.25 0.25 0.25 0.609t-0.25 0.578q-0.25 0.25-0.578 0.25t-0.578-0.25l-9.625-9.125q-0.156-0.125-0.203-0.297t-0.047-0.359q0-0.156 0.047-0.328t0.203-0.297l9.625-9.125q0.25-0.25 0.578-0.25t0.578 0.25q0.25 0.219 0.25 0.578t-0.25 0.578z",t.navigationColor,26);let g;const m=n=>{clearTimeout(g);const{clientX:o,clientY:r}=n,{left:i,right:s,top:a,bottom:l,width:c,height:f}=e.value.getBoundingClientRect();let d=!1;if(d=t.vertical?!(r-a<f/3||l-r<f/3):!(o-i<c/3||s-o<c/3),d)return g=setTimeout((()=>{u.value=d}),300);u.value=d},v=()=>{u.value=!0};function y(){e.value&&(e.value.removeEventListener("mousemove",m),e.value.removeEventListener("mouseleave",v),a&&(e.value.addEventListener("mousemove",m),e.value.addEventListener("mouseleave",v)))}return Ao(y),function(){const e={"uni-swiper-navigation-hide":u.value,"uni-swiper-navigation-vertical":t.vertical};return t.navigation?Kr(Pr,null,[Kr("div",oi({class:["uni-swiper-navigation uni-swiper-navigation-prev",T({"uni-swiper-navigation-disabled":l},e)],onClick:e=>h(e,"prev",l)},d),[p()],16,["onClick"]),Kr("div",oi({class:["uni-swiper-navigation uni-swiper-navigation-next",T({"uni-swiper-navigation-disabled":c},e)],onClick:e=>h(e,"next",c)},d),[p()],16,["onClick"])]):null}},yp=ou({name:"SwiperItem",props:{itemId:{type:String,default:""}},setup(e,{slots:t}){const n=tn(null),o={rootRef:n,getItemId:()=>e.itemId,getBoundingClientRect:()=>n.value.getBoundingClientRect(),updatePosition(e,t){const o=t?"0":100*e+"%",r=t?100*e+"%":"0",i=n.value,s=`translate(${o},${r}) translateZ(0)`;i&&(i.style.webkitTransform=s,i.style.transform=s)}};return Ao((()=>{const e=Wn("addSwiperContext");e&&e(o)})),Do((()=>{const e=Wn("removeSwiperContext");e&&e(o)})),()=>Kr("uni-swiper-item",{ref:n,style:{position:"absolute",width:"100%",height:"100%"}},[t.default&&t.default()],512)}}),bp=ou({name:"Switch",props:{name:{type:String,default:""},checked:{type:[Boolean,String],default:!1},type:{type:String,default:"switch"},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:""}},emits:["change"],setup(e,{emit:t}){const n=tn(null),o=tn(e.checked),r=function(e,t){const n=Wn(uu,!1),o=Wn(fu,!1),r={submit:()=>{const n=["",null];return e.name&&(n[0]=e.name,n[1]=t.value),n},reset:()=>{t.value=!1}};n&&(n.addField(r),Do((()=>{n.removeField(r)})));return o}(e,o),i=cu(n,t);Xn((()=>e.checked),(e=>{o.value=e}));const s=t=>{e.disabled||(o.value=!o.value,i("change",t,{value:o.value}))};return r&&(r.addHandler(s),$o((()=>{r.removeHandler(s)}))),du(e,{"label-click":s}),()=>{const{color:t,type:r}=e,i=au(e,"disabled"),a={};return t&&o.value&&(a.backgroundColor=t,a.borderColor=t),Kr("uni-switch",oi({ref:n},i,{onClick:s}),[Kr("div",{class:"uni-switch-wrapper"},[No(Kr("div",{class:["uni-switch-input",[o.value?"uni-switch-input-checked":""]],style:a},null,6),[[_s,"switch"===r]]),No(Kr("div",{class:"uni-checkbox-input"},[o.value?gc(hc,e.color,22):""],512),[[_s,"checkbox"===r]])])],16,["onClick"])}}});const _p={ensp:" ",emsp:" ",nbsp:" "};function wp(e,t){return e.replace(/\\n/g,"\n").split("\n").map((e=>function(e,{space:t,decode:n}){if(!e)return e;t&&_p[t]&&(e=e.replace(/ /g,_p[t]));if(!n)return e;return e.replace(/&nbsp;/g,_p.nbsp).replace(/&ensp;/g,_p.ensp).replace(/&emsp;/g,_p.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'")}(e,t)))}const xp=ou({name:"Text",props:{selectable:{type:[Boolean,String],default:!1},space:{type:String,default:""},decode:{type:[Boolean,String],default:!1}},setup:(e,{slots:t})=>()=>{const n=[];return t.default&&t.default().forEach((t=>{if(8&t.shapeFlag&&t.type!==$r){const o=wp(t.children,{space:e.space,decode:e.decode}),r=o.length-1;o.forEach(((e,t)=>{(0!==t||e)&&n.push(Jr(e)),t!==r&&n.push(Kr("br"))}))}else n.push(t)})),Kr("uni-text",{selectable:!!e.selectable||null},[Kr("span",null,n)],8,["selectable"])}}),Tp=T({},jh,{placeholderClass:{type:String,default:"input-placeholder"},autoHeight:{type:[Boolean,String],default:!1},confirmType:{type:String,default:"return",validator:e=>Ep.concat("return").includes(e)}});let Sp=!1;const Ep=["done","go","next","search","send"];const kp=ou({name:"Textarea",props:Tp,emits:["confirm","linechange",...qh],setup(e,{emit:t}){const n=tn(null),o=tn(null),{fieldRef:r,state:i,scopedAttrsState:s,fixDisabledColor:a,trigger:l}=Vh(e,n,t),c=vi((()=>i.value.split("\n"))),u=vi((()=>Ep.includes(e.confirmType))),f=tn(0),d=tn(null);function h({height:e}){f.value=e}function p(e){"Enter"===e.key&&u.value&&e.preventDefault()}function g(t){if("Enter"===t.key&&u.value){!function(e){l("confirm",e,{value:i.value})}(t);const n=t.target;!e.confirmHold&&n.blur()}}return Xn((()=>f.value),(t=>{const r=n.value,i=d.value,s=o.value;let a=parseFloat(getComputedStyle(r).lineHeight);isNaN(a)&&(a=i.offsetHeight);var c=Math.round(t/a);l("linechange",{},{height:t,heightRpx:750/window.innerWidth*t,lineCount:c}),e.autoHeight&&(r.style.height="auto",s.style.height=t+"px")})),function(){const e="(prefers-color-scheme: dark)";Sp=0===String(navigator.platform).indexOf("iP")&&0===String(navigator.vendor).indexOf("Apple")&&window.matchMedia(e).media!==e}(),()=>{let t=e.disabled&&a?Kr("textarea",{key:"disabled-textarea",ref:r,value:i.value,tabindex:"-1",readonly:!!e.disabled,maxlength:i.maxlength,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":Sp},style:{overflowY:e.autoHeight?"hidden":"auto"},onFocus:e=>e.target.blur()},null,46,["value","readonly","maxlength","onFocus"]):Kr("textarea",{key:"textarea",ref:r,value:i.value,disabled:!!e.disabled,maxlength:i.maxlength,enterkeyhint:e.confirmType,inputmode:e.inputmode,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":Sp},style:{overflowY:e.autoHeight?"hidden":"auto"},onKeydown:p,onKeyup:g},null,46,["value","disabled","maxlength","enterkeyhint","inputmode","onKeydown","onKeyup"]);return Kr("uni-textarea",{ref:n},[Kr("div",{ref:o,class:"uni-textarea-wrapper"},[No(Kr("div",oi(s.attrs,{style:e.placeholderStyle,class:["uni-textarea-placeholder",e.placeholderClass]}),[e.placeholder],16),[[_s,!i.value.length]]),Kr("div",{ref:d,class:"uni-textarea-line"},[" "],512),Kr("div",{class:"uni-textarea-compute"},[c.value.map((e=>Kr("div",null,[e.trim()?e:"."]))),Kr(sh,{initial:!0,onResize:h},null,8,["initial","onResize"])]),"search"===e.confirmType?Kr("form",{action:"",onSubmit:()=>!1,class:"uni-input-form"},[t],40,["onSubmit"]):t],512)],512)}}}),Cp=ou({name:"View",props:T({},iu),setup(e,{slots:t}){const{hovering:n,binding:o}=su(e);return()=>{const r=e.hoverClass;return r&&"none"!==r?Kr("uni-view",oi({class:n.value?r:""},o),[t.default&&t.default()],16):Kr("uni-view",null,[t.default&&t.default()])}}});function Op(e,t){if(t||(t=e.id),t)return e.$options.name.toLowerCase()+"."+t}function Mp(e,t,n){e&&$l(n||_c(),e,(({type:e,data:n},o)=>{t(e,n,o)}))}function Ap(e,t){e&&function(e,t){t=Il(e,t),delete Pl[t]}(t||_c(),e)}function Pp(e,t,n,o){const r=li().proxy;Ao((()=>{Mp(t||Op(r),e,o),!n&&t||Xn((()=>r.id),((t,n)=>{Mp(Op(r,t),e,o),Ap(n&&Op(r,n))}))})),$o((()=>{Ap(t||Op(r),o)}))}let Ip=0;function $p(e){const t=mc(),n=li().proxy,o=n.$options.name.toLowerCase(),r=e||n.id||"context"+Ip++;return Ao((()=>{n.$el.__uniContextInfo={id:r,type:o,page:t}})),`${o}.${r}`}function Dp(e,t,n,o){P(t)&&Co(e,t.bind(n),o)}function Lp(e,t,n){var o;const r=e.mpType||n.$mpType;if(r&&"component"!==r&&(Object.keys(e).forEach((o=>{if(function(e,t,n=!0){return!(n&&!P(t))&&(ke.indexOf(e)>-1||0===e.indexOf("on"))}(o,e[o],!1)){const r=e[o];C(r)?r.forEach((e=>Dp(o,e,n,t))):Dp(o,r,n,t)}})),"page"===r)){t.__isVisible=!0;try{Sc(n,"onLoad",t.attrs.__pageQuery),delete t.attrs.__pageQuery,"preloadPage"!==(null==(o=n.$page)?void 0:o.openType)&&Sc(n,"onShow")}catch(i){console.error(i.message+"\n"+i.stack)}}}function Bp(e,t,n){Lp(e,t,n)}function Fp(e,t,n){return e[t]=n}function Rp(e,...t){const n=this[e];return n?n(...t):(console.error(`method ${e} not found`),null)}function Np(e){return function(t,n,o){if(!n)throw t;const r=e._instance;if(!r||!r.proxy)throw t;Sc(r.proxy,"onError",t)}}function jp(e,t){return e?[...new Set([].concat(e,t))]:t}function qp(e){const t=e._context.config;var n;t.errorHandler=Oe(e,Np),n=t.optionMergeStrategies,ke.forEach((e=>{n[e]=jp}));const o=t.globalProperties;o.$set=Fp,o.$applyOptions=Bp,o.$callMethod=Rp,function(e){Ce.forEach((t=>t(e)))}(e)}const zp=uc("upm");function Hp(){return Wn(zp)}function Vp(e){const t=function(e){return qt(function(e){{const{enablePullDownRefresh:t,navigationBar:n}=e;if(t){const t=function(e){return e.offset&&(e.offset=dc(e.offset)),e.height&&(e.height=dc(e.height)),e.range&&(e.range=dc(e.range)),e}(T({support:!0,color:"#2BD009",style:"circle",height:70,range:150,offset:0},e.pullToRefresh)),{type:o,style:r}=n;"custom"!==r&&"transparent"!==o&&(t.offset+=44+nc.top),e.pullToRefresh=t}}if(history.state){const t=history.state.__type__;"redirectTo"!==t&&"reLaunch"!==t||0!==rg().length||(e.isEntry=!0,e.isQuit=!0)}return e}(JSON.parse(JSON.stringify(Tc(pl().meta,e)))))}(e);return Vn(zp,t),t}function Wp(){return pl()}function Up(){return history.state&&history.state.__id__||1}let Yp;function Xp(){var e;return Yp||(Yp=__uniConfig.tabBar&&qt((e=__uniConfig.tabBar,gl()&&e.list&&e.list.forEach((e=>{bl(e,["text"])})),e))),Yp}const Kp=window.CSS&&window.CSS.supports;function Gp(e){return Kp&&(Kp(e)||Kp.apply(window.CSS,e.split(":")))}const Zp=Gp("top:env(a)"),Jp=Gp("top:constant(a)"),Qp=Gp("backdrop-filter:blur(10px)"),eg=(()=>Zp?"env":Jp?"constant":"")();function tg(e){return eg?`calc(${e}px + ${eg}(safe-area-inset-bottom))`:`${e}px`}const ng=new Map;function og(){return ng}function rg(){const e=[],t=ng.values();for(const n of t)n.$.__isTabBar?n.$.__isActive&&e.push(n):e.push(n);return e}function ig(e,t=!0){const n=ng.get(e);n.$.__isUnload=!0,Sc(n,"onUnload"),ng.delete(e),t&&function(e){const t=ug.get(e);t&&(ug.delete(e),fg.pruneCacheEntry(t))}(e)}let sg=Up();function ag(e){const t=Hp();let n=e.fullPath;return e.meta.isEntry&&-1===n.indexOf(e.meta.route)&&(n="/"+e.meta.route+n.replace("/","")),function(e,t,n,o,r,i){const{id:s,route:a}=o,l=Ie(o.navigationBar,__uniConfig.themeConfig,i).titleColor;return{id:s,path:ae(a),route:a,fullPath:t,options:n,meta:o,openType:e,eventChannel:r,statusBarStyle:"#ffffff"===l?"light":"dark"}}("navigateTo",n,{},t)}function lg(e){const t=ag(e.$route);!function(e,t){e.route=t.route,e.$vm=e,e.$page=t,e.$mpType="page",t.meta.isTabBar&&(e.$.__isTabBar=!0,e.$.__isActive=!0)}(e,t),ng.set(cg(t.path,t.id),e)}function cg(e,t){return e+"$$"+t}const ug=new Map,fg={get:e=>ug.get(e),set(e,t){!function(e){const t=parseInt(e.split("$$")[1]);if(!t)return;fg.forEach(((e,n)=>{const o=parseInt(n.split("$$")[1]);if(o&&o>t){if(function(e){return"tabBar"===e.props.type}(e))return;fg.delete(n),fg.pruneCacheEntry(e),Tn((()=>{ng.forEach(((e,t)=>{e.$.isUnmounted&&ng.delete(t)}))}))}}))}(e),ug.set(e,t)},delete(e){ug.get(e)&&ug.delete(e)},forEach(e){ug.forEach(e)}};function dg(e,t){!function(e){const t=pg(e),{body:n}=document;gg&&n.removeAttribute(gg),t&&n.setAttribute(t,""),gg=t}(e),function(e){let t=0;if(e.isTabBar){const e=Xp();e.shown&&(t=parseInt(e.height))}var n;cc({"--window-top":(n=0,eg?`calc(${n}px + ${eg}(safe-area-inset-top))`:`${n}px`),"--window-bottom":tg(t)})}(t),function(e){{const t="nvue-dir-"+__uniConfig.nvue["flex-direction"];e.isNVue?(document.body.setAttribute("nvue",""),document.body.setAttribute(t,"")):(document.body.removeAttribute("nvue"),document.body.removeAttribute(t))}}(t),function(e,t){document.removeEventListener("touchmove",Ec),mg&&document.removeEventListener("scroll",mg);if(t.disableScroll)return document.addEventListener("touchmove",Ec);const{onPageScroll:n,onReachBottom:o}=e,r="transparent"===t.navigationBar.type;if(!n&&!o&&!r)return;const i={},s=e.proxy.$page.id;(n||r)&&(i.onPageScroll=function(e,t,n){return o=>{t&&Av.publishHandler("onPageScroll",{scrollTop:o},e),n&&Av.emit(e+".onPageScroll",{scrollTop:o})}}(s,n,r));o&&(i.onReachBottomDistance=t.onReachBottomDistance||50,i.onReachBottom=()=>Av.publishHandler("onReachBottom",{},s));mg=Oc(i),requestAnimationFrame((()=>document.addEventListener("scroll",mg)))}(e,t)}function hg(e){const t=pg(e);t&&function(e){const t=document.querySelector("uni-page-body");t&&t.setAttribute(e,"")}(t)}function pg(e){return e.type.__scopeId}let gg,mg;function vg(e){const t=dl({history:bg(),strict:!!__uniConfig.router.strict,routes:__uniRoutes,scrollBehavior:yg});e.router=t,e.use(t)}const yg=(e,t,n)=>{if(n)return n};function bg(){let{routerBase:e}=__uniConfig.router;"/"===e&&(e="");const t=aa(e);return t.listen(((e,t,n)=>{"back"===n.direction&&function(e=1){const t=rg(),n=t.length-1,o=n-e;for(let r=n;r>o;r--){const e=t[r].$page;ig(cg(e.path,e.id),!1)}}(Math.abs(n.delta))})),t}const _g={install(e){qp(e),qc(e),Jc(e),e.config.warnHandler||(e.config.warnHandler=wg),vg(e)}};function wg(e,t,n){if(t){if("PageMetaHead"===t.$.type.name)return;const e=t.$.parent;if(e&&"PageMeta"===e.type.name)return}const o=[`[Vue warn]: ${e}`];n.length&&o.push("\n",n),console.warn(...o)}const xg={class:"uni-async-loading"},Tg=Kr("i",{class:"uni-loading"},null,-1),Sg=ru({name:"AsyncLoading",render:()=>(Fr(),zr("div",xg,[Tg]))});function Eg(){window.location.reload()}const kg=ru({name:"AsyncError",setup(){xl();const{t:e}=_l();return()=>Kr("div",{class:"uni-async-error",onClick:Eg},[e("uni.async.error")],8,["onClick"])}});let Cg;function Og(){return Cg}function Mg(e){Cg=e,Object.defineProperty(Cg.$.ctx,"$children",{get:()=>rg().map((e=>e.$vm))});const t=Cg.$.appContext.app;t.component(Sg.name)||t.component(Sg.name,Sg),t.component(kg.name)||t.component(kg.name,kg),function(e){e.$vm=e,e.$mpType="app";const t=tn(_l().getLocale());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(Cg),function(e,t){const n=e.$options||{};n.globalData=T(n.globalData||{},t),Object.defineProperty(e,"globalData",{get:()=>n.globalData,set(e){n.globalData=e}})}(Cg),Gc(),Wl()}function Ag(e,{clone:t,init:n,setup:o,before:r}){t&&(e=T({},e)),r&&r(e);const i=e.setup;return e.setup=(e,t)=>{const r=li();n(r.proxy);const s=o(r);if(i)return i(s||e,t)},e}function Pg(e,t){return e&&(e.__esModule||"Module"===e[Symbol.toStringTag])?Ag(e.default,t):Ag(e,t)}function Ig(e){return Pg(e,{clone:!0,init:lg,setup(e){e.$pageInstance=e;const t=Wp(),n=be(t.query);e.attrs.__pageQuery=n,e.proxy.$page.options=n;const o=Hp();var r,i,s;return Mo((()=>{dg(e,o)})),Ao((()=>{hg(e);const{onReady:n}=e;n&&K(n),Bg(t)})),_o((()=>{if(!e.__isVisible){dg(e,o),e.__isVisible=!0;const{onShow:n}=e;n&&K(n),Tn((()=>{Bg(t)}))}}),"ba",r),function(e,t){_o(e,"bda",t)}((()=>{if(e.__isVisible&&!e.__isUnload){e.__isVisible=!1;const{onHide:t}=e;t&&K(t)}})),i=o.id,Av.subscribe(Il(i,"invokeViewApi"),s?s(Dl):Dl),$o((()=>{!function(e){Av.unsubscribe(Il(e,"invokeViewApi")),Object.keys(Pl).forEach((t=>{0===t.indexOf(e+".")&&delete Pl[t]}))}(o.id)})),n}})}function $g(){const{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}=em(),r=90===Math.abs(Number(window.orientation))?"landscape":"portrait";Pv.emit("onResize",{deviceOrientation:r,size:{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}})}function Dg(e){R(e.data)&&"WEB_INVOKE_APPSERVICE"===e.data.type&&Pv.emit("onWebInvokeAppService",e.data.data,e.data.pageId)}function Lg(){const{emit:e}=Pv;"visible"===document.visibilityState?e("onAppEnterForeground",T({},ih)):e("onAppEnterBackground")}function Bg(e){const{tabBarText:t,tabBarIndex:n,route:o}=e.meta;t&&Sc("onTabItemTap",{index:n,text:t,pagePath:o})}function Fg(e){e=e>0&&e<1/0?e:0;const t=Math.floor(e/3600),n=Math.floor(e%3600/60),o=Math.floor(e%3600%60),r=(t<10?"0":"")+t;let i=(n<10?"0":"")+n+":"+((o<10?"0":"")+o);return"00"!==r&&(i=r+":"+i),i}function Rg(e,t,n){const o=qt({gestureType:"none",volumeOld:0,volumeNew:0,currentTimeOld:0,currentTimeNew:0}),r={x:0,y:0};return{state:o,onTouchstart:function(e){const t=e.targetTouches[0];r.x=t.pageX,r.y=t.pageY,o.gestureType="none",o.volumeOld=0,o.currentTimeOld=o.currentTimeNew=0},onTouchmove:function(i){function s(){i.stopPropagation(),i.preventDefault()}n.fullscreen&&s();const a=o.gestureType;if("stop"===a)return;const l=i.targetTouches[0],c=l.pageX,u=l.pageY,f=r,d=t.value;if("progress"===a?function(e){const n=t.value.duration;let r=e/600*n+o.currentTimeOld;r<0?r=0:r>n&&(r=n);o.currentTimeNew=r}(c-f.x):"volume"===a&&function(e){const n=t.value,r=o.volumeOld;let i;"number"==typeof r&&(i=r-e/200,i<0?i=0:i>1&&(i=1),n.volume=i,o.volumeNew=i)}(u-f.y),"none"===a)if(Math.abs(c-f.x)>Math.abs(u-f.y)){if(!e.enableProgressGesture)return void(o.gestureType="stop");o.gestureType="progress",o.currentTimeOld=o.currentTimeNew=d.currentTime,n.fullscreen||s()}else{if(!e.pageGesture)return void(o.gestureType="stop");o.gestureType="volume",o.volumeOld=d.volume,n.fullscreen||s()}},onTouchend:function(e){const n=t.value;"none"!==o.gestureType&&"stop"!==o.gestureType&&(e.stopPropagation(),e.preventDefault()),"progress"===o.gestureType&&o.currentTimeOld!==o.currentTimeNew&&(n.currentTime=o.currentTimeNew),o.gestureType="none"}}}const Ng=ou({name:"Video",props:{id:{type:String,default:""},src:{type:String,default:""},duration:{type:[Number,String],default:""},controls:{type:[Boolean,String],default:!0},danmuList:{type:Array,default:()=>[]},danmuBtn:{type:[Boolean,String],default:!1},enableDanmu:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},loop:{type:[Boolean,String],default:!1},muted:{type:[Boolean,String],default:!1},objectFit:{type:String,default:"contain"},poster:{type:String,default:""},direction:{type:[String,Number],default:""},showProgress:{type:Boolean,default:!0},initialTime:{type:[String,Number],default:0},showFullscreenBtn:{type:[Boolean,String],default:!0},pageGesture:{type:[Boolean,String],default:!1},enableProgressGesture:{type:[Boolean,String],default:!0},showPlayBtn:{type:[Boolean,String],default:!0},showCenterPlayBtn:{type:[Boolean,String],default:!0}},emits:["fullscreenchange","progress","loadedmetadata","waiting","error","play","pause","ended","timeupdate"],setup(e,{emit:t,attrs:n,slots:o}){const r=tn(null),i=tn(null),s=cu(r,t),{state:a}=Lh(),{$attrs:l}=Xh({excludeListeners:!0}),{t:c}=_l();Ol();const{videoRef:u,state:f,play:d,pause:h,seek:p,playbackRate:g,toggle:m,onDurationChange:v,onLoadedMetadata:y,onProgress:b,onWaiting:_,onVideoError:w,onPlay:x,onPause:T,onEnded:S,onTimeUpdate:E}=function(e,t,n){const o=tn(null),r=vi((()=>vu(e.src))),i=qt({start:!1,src:r,playing:!1,currentTime:0,duration:0,progress:0,buffered:0});function s(e){const t=e.target,n=t.buffered;n.length&&(i.buffered=n.end(n.length-1)/t.duration*100)}return Xn((()=>r.value),(()=>{i.playing=!1,i.currentTime=0})),Xn((()=>i.buffered),(e=>{n("progress",{},{buffered:e})})),{videoRef:o,state:i,play:function(){const e=o.value;i.start=!0,e.play()},pause:function(){o.value.pause()},seek:function(e){const t=o.value;"number"!=typeof(e=Number(e))||isNaN(e)||(t.currentTime=e)},playbackRate:function(e){o.value.playbackRate=e},toggle:function(){const e=o.value;i.playing?e.pause():e.play()},onDurationChange:function({target:e}){i.duration=e.duration},onLoadedMetadata:function(t){const o=Number(e.initialTime)||0,r=t.target;o>0&&(r.currentTime=o),n("loadedmetadata",t,{width:r.videoWidth,height:r.videoHeight,duration:r.duration}),s(t)},onProgress:s,onWaiting:function(e){n("waiting",e,{})},onVideoError:function(e){i.playing=!1,n("error",e,{})},onPlay:function(e){i.start=!0,i.playing=!0,n("play",e,{})},onPause:function(e){i.playing=!1,n("pause",e,{})},onEnded:function(e){i.playing=!1,n("ended",e,{})},onTimeUpdate:function(e){const t=e.target,o=i.currentTime=t.currentTime;n("timeupdate",e,{currentTime:o,duration:t.duration})}}}(e,0,s),{state:k,danmuRef:O,updateDanmu:M,toggleDanmu:A,sendDanmu:P}=function(e,t){const n=tn(null),o=qt({enable:Boolean(e.enableDanmu)});let r={time:0,index:-1};const i=C(e.danmuList)?JSON.parse(JSON.stringify(e.danmuList)):[];function s(e){const t=document.createElement("p");t.className="uni-video-danmu-item",t.innerText=e.text;let o=`bottom: ${100*Math.random()}%;color: ${e.color};`;t.setAttribute("style",o),n.value.appendChild(t),setTimeout((function(){o+="left: 0;-webkit-transform: translateX(-100%);transform: translateX(-100%);",t.setAttribute("style",o),setTimeout((function(){t.remove()}),4e3)}),17)}return i.sort((function(e,t){return(e.time||0)-(t.time||0)})),{state:o,danmuRef:n,updateDanmu:function(e){const n=e.target.currentTime,a=r,l={time:n,index:a.index};if(n>a.time)for(let r=a.index+1;r<i.length;r++){const e=i[r];if(!(n>=(e.time||0)))break;l.index=r,t.playing&&o.enable&&s(e)}else if(n<a.time)for(let t=a.index-1;t>-1&&n<=(i[t].time||0);t--)l.index=t-1;r=l},toggleDanmu:function(){o.enable=!o.enable},sendDanmu:function(e){i.splice(r.index+1,0,{text:String(e.text),color:e.color,time:t.currentTime||0})}}}(e,f),{state:I,onFullscreenChange:$,emitFullscreenChange:D,toggleFullscreen:L,requestFullScreen:B,exitFullScreen:F}=function(e,t,n,o,r){const i=qt({fullscreen:!1}),s=/^Apple/.test(navigator.vendor);function a(t){i.fullscreen=t,e("fullscreenchange",{},{fullScreen:t,direction:"vertical"})}function l(e){const i=r.value,l=t.value,c=n.value;let u;e?!document.fullscreenEnabled&&!document.webkitFullscreenEnabled||s&&!o.userAction?c.webkitEnterFullScreen?c.webkitEnterFullScreen():(u=!0,l.remove(),l.classList.add("uni-video-type-fullscreen"),document.body.appendChild(l)):l[document.fullscreenEnabled?"requestFullscreen":"webkitRequestFullscreen"]():document.fullscreenEnabled||document.webkitFullscreenEnabled?document.fullscreenElement?document.exitFullscreen():document.webkitFullscreenElement&&document.webkitExitFullscreen():c.webkitExitFullScreen?c.webkitExitFullScreen():(u=!0,l.remove(),l.classList.remove("uni-video-type-fullscreen"),i.appendChild(l)),u&&a(e)}function c(){l(!1)}return $o(c),{state:i,onFullscreenChange:function(e,t){t&&document.fullscreenEnabled||a(!(!document.fullscreenElement&&!document.webkitFullscreenElement))},emitFullscreenChange:a,toggleFullscreen:l,requestFullScreen:function(){l(!0)},exitFullScreen:c}}(s,i,u,a,r),{state:R,onTouchstart:N,onTouchend:j,onTouchmove:q}=Rg(e,u,I),{state:z,progressRef:H,ballRef:V,clickProgress:W,toggleControls:U}=function(e,t,n){const o=tn(null),r=tn(null),i=vi((()=>e.showCenterPlayBtn&&!t.start)),s=tn(!0),a=vi((()=>!i.value&&e.controls&&s.value)),l=qt({touching:!1,controlsTouching:!1,centerPlayBtnShow:i,controlsShow:a,controlsVisible:s});let c;function u(){c=setTimeout((()=>{l.controlsVisible=!1}),3e3)}function f(){c&&(clearTimeout(c),c=null)}return $o((()=>{c&&clearTimeout(c)})),Xn((()=>l.controlsShow&&t.playing&&!l.controlsTouching),(e=>{e?u():f()})),Xn([()=>t.currentTime,()=>{e.duration}],(function(){l.touching||(t.progress=t.currentTime/t.duration*100)})),Ao((()=>{const e=me(!1);let i,s,a,c=!0;const u=r.value;function f(e){const n=e.targetTouches[0],r=n.pageX,l=n.pageY;if(c&&Math.abs(r-i)<Math.abs(l-s))return void d(e);c=!1;const u=o.value.offsetWidth;let f=a+(r-i)/u*100;f<0?f=0:f>100&&(f=100),t.progress=f,e.preventDefault(),e.stopPropagation()}function d(o){l.controlsTouching=!1,l.touching&&(u.removeEventListener("touchmove",f,e),c||(o.preventDefault(),o.stopPropagation(),n(t.duration*t.progress/100)),l.touching=!1)}u.addEventListener("touchstart",(n=>{l.controlsTouching=!0;const o=n.targetTouches[0];i=o.pageX,s=o.pageY,a=t.progress,c=!0,l.touching=!0,u.addEventListener("touchmove",f,e)})),u.addEventListener("touchend",d),u.addEventListener("touchcancel",d)})),{state:l,progressRef:o,ballRef:r,clickProgress:function(e){const r=o.value;let i=e.target,s=e.offsetX;for(;i&&i!==r;)s+=i.offsetLeft,i=i.parentNode;const a=r.offsetWidth;let l=0;s>=0&&s<=a&&(l=s/a,n(t.duration*l))},toggleControls:function(){l.controlsVisible=!l.controlsVisible},autoHideStart:u,autoHideEnd:f}}(e,f,p);return function(e,t,n,o,r,i,s){const a={play:e,pause:t,seek:n,sendDanmu:o,playbackRate:r,requestFullScreen:i,exitFullScreen:s};Pp(((e,t)=>{let n;switch(e){case"seek":n=t.position;break;case"sendDanmu":n=t;break;case"playbackRate":n=t.rate}e in a&&a[e](n)}),$p(),!0)}(d,h,p,P,g,B,F),()=>Kr("uni-video",{ref:r,id:e.id},[Kr("div",{ref:i,class:"uni-video-container",onTouchstart:N,onTouchend:j,onTouchmove:q,onFullscreenchange:bs($,["stop"]),onWebkitfullscreenchange:bs((e=>$(e,!0)),["stop"])},[Kr("video",oi({ref:u,style:{"object-fit":e.objectFit},muted:!!e.muted,loop:!!e.loop,src:f.src,poster:e.poster,autoplay:!!e.autoplay},l.value,{class:"uni-video-video","webkit-playsinline":!0,playsinline:!0,onClick:U,onDurationchange:v,onLoadedmetadata:y,onProgress:b,onWaiting:_,onError:w,onPlay:x,onPause:T,onEnded:S,onTimeupdate:e=>{E(e),M(e)},onWebkitbeginfullscreen:()=>D(!0),onX5videoenterfullscreen:()=>D(!0),onWebkitendfullscreen:()=>D(!1),onX5videoexitfullscreen:()=>D(!1)}),null,16,["muted","loop","src","poster","autoplay","webkit-playsinline","playsinline","onClick","onDurationchange","onLoadedmetadata","onProgress","onWaiting","onError","onPlay","onPause","onEnded","onTimeupdate","onWebkitbeginfullscreen","onX5videoenterfullscreen","onWebkitendfullscreen","onX5videoexitfullscreen"]),No(Kr("div",{class:"uni-video-bar uni-video-bar-full",onClick:bs((()=>{}),["stop"])},[Kr("div",{class:"uni-video-controls"},[No(Kr("div",{class:{"uni-video-control-button":!0,"uni-video-control-button-play":!f.playing,"uni-video-control-button-pause":f.playing},onClick:bs(m,["stop"])},null,10,["onClick"]),[[_s,e.showPlayBtn]]),No(Kr("div",{class:"uni-video-current-time"},[Fg(f.currentTime)],512),[[_s,e.showProgress]]),No(Kr("div",{ref:H,class:"uni-video-progress-container",onClick:bs(W,["stop"])},[Kr("div",{class:"uni-video-progress"},[Kr("div",{style:{width:f.buffered+"%"},class:"uni-video-progress-buffered"},null,4),Kr("div",{ref:V,style:{left:f.progress+"%"},class:"uni-video-ball"},[Kr("div",{class:"uni-video-inner"},null)],4)])],8,["onClick"]),[[_s,e.showProgress]]),No(Kr("div",{class:"uni-video-duration"},[Fg(Number(e.duration)||f.duration)],512),[[_s,e.showProgress]])]),No(Kr("div",{class:{"uni-video-danmu-button":!0,"uni-video-danmu-button-active":k.enable},onClick:bs(A,["stop"])},[c("uni.video.danmu")],10,["onClick"]),[[_s,e.danmuBtn]]),No(Kr("div",{class:{"uni-video-fullscreen":!0,"uni-video-type-fullscreen":I.fullscreen},onClick:bs((()=>L(!I.fullscreen)),["stop"])},null,10,["onClick"]),[[_s,e.showFullscreenBtn]])],8,["onClick"]),[[_s,z.controlsShow]]),No(Kr("div",{ref:O,style:"z-index: 0;",class:"uni-video-danmu"},null,512),[[_s,f.start&&k.enable]]),z.centerPlayBtnShow&&Kr("div",{class:"uni-video-cover",onClick:bs((()=>{}),["stop"])},[Kr("div",{class:"uni-video-cover-play-button",onClick:bs(d,["stop"])},null,8,["onClick"]),Kr("p",{class:"uni-video-cover-duration"},[Fg(Number(e.duration)||f.duration)])],8,["onClick"]),Kr("div",{class:{"uni-video-toast":!0,"uni-video-toast-volume":"volume"===R.gestureType}},[Kr("div",{class:"uni-video-toast-title"},[c("uni.video.volume")]),Kr("svg",{class:"uni-video-toast-icon",width:"200px",height:"200px",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},[Kr("path",{d:"M475.400704 201.19552l0 621.674496q0 14.856192-10.856448 25.71264t-25.71264 10.856448-25.71264-10.856448l-190.273536-190.273536-149.704704 0q-14.856192 0-25.71264-10.856448t-10.856448-25.71264l0-219.414528q0-14.856192 10.856448-25.71264t25.71264-10.856448l149.704704 0 190.273536-190.273536q10.856448-10.856448 25.71264-10.856448t25.71264 10.856448 10.856448 25.71264zm219.414528 310.837248q0 43.425792-24.28416 80.851968t-64.2816 53.425152q-5.71392 2.85696-14.2848 2.85696-14.856192 0-25.71264-10.570752t-10.856448-25.998336q0-11.999232 6.856704-20.284416t16.570368-14.2848 19.427328-13.142016 16.570368-20.284416 6.856704-32.569344-6.856704-32.569344-16.570368-20.284416-19.427328-13.142016-16.570368-14.2848-6.856704-20.284416q0-15.427584 10.856448-25.998336t25.71264-10.570752q8.57088 0 14.2848 2.85696 39.99744 15.427584 64.2816 53.139456t24.28416 81.137664zm146.276352 0q0 87.422976-48.56832 161.41824t-128.5632 107.707392q-7.428096 2.85696-14.2848 2.85696-15.427584 0-26.284032-10.856448t-10.856448-25.71264q0-22.284288 22.284288-33.712128 31.997952-16.570368 43.425792-25.141248 42.283008-30.855168 65.995776-77.423616t23.712768-99.136512-23.712768-99.136512-65.995776-77.423616q-11.42784-8.57088-43.425792-25.141248-22.284288-11.42784-22.284288-33.712128 0-14.856192 10.856448-25.71264t25.71264-10.856448q7.428096 0 14.856192 2.85696 79.99488 33.712128 128.5632 107.707392t48.56832 161.41824zm146.276352 0q0 131.42016-72.566784 241.41312t-193.130496 161.989632q-7.428096 2.85696-14.856192 2.85696-14.856192 0-25.71264-10.856448t-10.856448-25.71264q0-20.570112 22.284288-33.712128 3.999744-2.285568 12.85632-5.999616t12.85632-5.999616q26.284032-14.2848 46.854144-29.140992 70.281216-51.996672 109.707264-129.705984t39.426048-165.132288-39.426048-165.132288-109.707264-129.705984q-20.570112-14.856192-46.854144-29.140992-3.999744-2.285568-12.85632-5.999616t-12.85632-5.999616q-22.284288-13.142016-22.284288-33.712128 0-14.856192 10.856448-25.71264t25.71264-10.856448q7.428096 0 14.856192 2.85696 120.563712 51.996672 193.130496 161.989632t72.566784 241.41312z"},null)]),Kr("div",{class:"uni-video-toast-value"},[Kr("div",{style:{width:100*R.volumeNew+"%"},class:"uni-video-toast-value-content"},[Kr("div",{class:"uni-video-toast-volume-grids"},[Uo(10,(()=>Kr("div",{class:"uni-video-toast-volume-grids-item"},null)))])],4)])],2),Kr("div",{class:{"uni-video-toast":!0,"uni-video-toast-progress":"progress"===R.gestureType}},[Kr("div",{class:"uni-video-toast-title"},[Fg(R.currentTimeNew)," / ",Fg(f.duration)])],2),Kr("div",{class:"uni-video-slots"},[o.default&&o.default()])],40,["onTouchstart","onTouchend","onTouchmove","onFullscreenchange","onWebkitfullscreenchange"])],8,["id"])}}),jg=({name:e,arg:t})=>{"postMessage"===e||uni[e](t)},qg=ce((()=>Pv.on("onWebInvokeAppService",jg))),zg=ou({inheritAttrs:!1,name:"WebView",props:{src:{type:String,default:""},fullscreen:{type:Boolean,default:!0}},setup(e){qg();const t=tn(null),n=tn(null),{$attrs:o,$excludeAttrs:r,$listeners:i}=Xh({excludeListeners:!0});let s;return(()=>{const r=document.createElement("iframe");Un((()=>{for(const e in o.value)if(k(o.value,e)){const t=o.value[e];r[e]=t}})),Un((()=>{r.src=vu(e.src)})),n.value=r,s=function(e,t,n){return()=>{var o,r;if(n){const{top:n,left:o,width:r,height:i}=e.value.getBoundingClientRect();le(t.value,{position:"absolute",display:"block",border:"0",top:n+"px",left:o+"px",width:r+"px",height:i+"px"})}else le(t.value,{width:(null==(o=e.value)?void 0:o.style.width)||"300px",height:(null==(r=e.value)?void 0:r.style.height)||"150px"})}}(t,n,e.fullscreen),e.fullscreen&&document.body.appendChild(r)})(),Ao((()=>{var o;s(),!e.fullscreen&&(null==(o=t.value)||o.appendChild(n.value))})),yo((()=>{e.fullscreen&&(n.value.style.display="block")})),bo((()=>{e.fullscreen&&(n.value.style.display="none")})),$o((()=>{e.fullscreen&&document.body.removeChild(n.value)})),()=>Kr(Pr,null,[Kr("uni-web-view",oi({class:e.fullscreen?"uni-webview--fullscreen":""},i.value,r.value,{ref:t}),[Kr(sh,{onResize:s},null,8,["onResize"])],16)])}});const Hg=navigator.cookieEnabled&&(window.localStorage||window.sessionStorage)||{};let Vg;function Wg(){if(Vg=Vg||Hg.__DC_STAT_UUID,!Vg){Vg=Date.now()+""+Math.floor(1e7*Math.random());try{Hg.__DC_STAT_UUID=Vg}catch(e){}}return Vg}function Ug(){if(!0!==__uniConfig.darkmode)return I(__uniConfig.darkmode)?__uniConfig.darkmode:"light";try{return window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark"}catch(e){return"light"}}function Yg(){let e,t="0",n="",o="phone";const r=navigator.language;if(_u){e="iOS";const o=yu.match(/OS\s([\w_]+)\slike/);o&&(t=o[1].replace(/_/g,"."));const r=yu.match(/\(([a-zA-Z]+);/);r&&(n=r[1])}else if(bu){e="Android";const o=yu.match(/Android[\s/]([\w\.]+)[;\s]/);o&&(t=o[1]);const r=yu.match(/\((.+?)\)/),i=r?r[1].split(";"):yu.split(" "),s=[/\bAndroid\b/i,/\bLinux\b/i,/\bU\b/i,/^\s?[a-z][a-z]$/i,/^\s?[a-z][a-z]-[a-z][a-z]$/i,/\bwv\b/i,/\/[\d\.,]+$/,/^\s?[\d\.,]+$/,/\bBrowser\b/i,/\bMobile\b/i];for(let e=0;e<i.length;e++){const t=i[e];if(t.indexOf("Build")>0){n=t.split("Build")[0].trim();break}let o;for(let e=0;e<s.length;e++)if(s[e].test(t)){o=!0;break}if(!o){n=t.trim();break}}}else if(Su)n="iPad",e="iOS",o="pad",t=P(window.BigInt)?"14.0":"13.0";else if(wu||xu||Tu){n="PC",e="PC",o="pc",t="0";let r=yu.match(/\((.+?)\)/)[1];if(wu){switch(e="Windows",wu[1]){case"5.1":t="XP";break;case"6.0":t="Vista";break;case"6.1":t="7";break;case"6.2":t="8";break;case"6.3":t="8.1";break;case"10.0":t="10"}const n=r&&r.match(/[Win|WOW]([\d]+)/);n&&(t+=` x${n[1]}`)}else if(xu){e="macOS";const n=r&&r.match(/Mac OS X (.+)/)||"";t&&(t=n[1].replace(/_/g,"."),-1!==t.indexOf(";")&&(t=t.split(";")[0]))}else if(Tu){e="Linux";const n=r&&r.match(/Linux (.*)/)||"";n&&(t=n[1],-1!==t.indexOf(";")&&(t=t.split(";")[0]))}}else e="Other",t="0",o="unknown";const i=`${e} ${t}`,s=e.toLocaleLowerCase();let a="",l=String(function(){const e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,n=e.indexOf("Edge")>-1&&!t,o=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;if(t){new RegExp("MSIE (\\d+\\.\\d+);").test(e);const t=parseFloat(RegExp.$1);return t>6?t:6}return n?-1:o?11:-1}());if("-1"!==l)a="IE";else{const e=["Version","Firefox","Chrome","Edge{0,1}"],t=["Safari","Firefox","Chrome","Edge"];for(let n=0;n<e.length;n++){const o=e[n],r=new RegExp(`(${o})/(\\S*)\\b`);r.test(yu)&&(a=t[n],l=yu.match(r)[2])}}let c="portrait";const u=void 0===window.screen.orientation?window.orientation:window.screen.orientation.angle;return c=90===Math.abs(u)?"landscape":"portrait",{deviceBrand:void 0,brand:void 0,deviceModel:n,deviceOrientation:c,model:n,system:i,platform:s,browserName:a.toLocaleLowerCase(),browserVersion:l,language:r,deviceType:o,ua:yu,osname:e,osversion:t,theme:Ug()}}const Xg=mf(0,(()=>{const e=window.devicePixelRatio,t=Eu(),n=ku(t),o=Cu(t,n),r=function(e,t){return e?Math[t?"min":"max"](screen.height,screen.width):screen.height}(t,n),i=Ou(o);let s=window.innerHeight;const a=nc.top,l={left:nc.left,right:i-nc.right,top:nc.top,bottom:s-nc.bottom,width:i-nc.left-nc.right,height:s-nc.top-nc.bottom},{top:c,bottom:u}=ac();return s-=c,s-=u,{windowTop:c,windowBottom:u,windowWidth:i,windowHeight:s,pixelRatio:e,screenWidth:o,screenHeight:r,statusBarHeight:a,safeArea:l,safeAreaInsets:{top:nc.top,right:nc.right,bottom:nc.bottom,left:nc.left},screenTop:r-s}}));let Kg,Gg=!0;function Zg(){Gg&&(Kg=Yg())}const Jg=mf(0,(()=>{Zg();const{deviceBrand:e,deviceModel:t,brand:n,model:o,platform:r,system:i,deviceOrientation:s,deviceType:a}=Kg;return{brand:n,deviceBrand:e,deviceModel:t,devicePixelRatio:window.devicePixelRatio,deviceId:Wg(),deviceOrientation:s,deviceType:a,model:o,platform:r,system:i}})),Qg=mf(0,(()=>{Zg();const{theme:e,language:t,browserName:n,browserVersion:o}=Kg;return{appId:__uniConfig.appId,appName:__uniConfig.appName,appVersion:__uniConfig.appVersion,appVersionCode:__uniConfig.appVersionCode,appLanguage:vd?vd():t,enableDebug:!1,hostSDKVersion:void 0,hostPackageName:void 0,hostFontSizeSetting:void 0,hostName:n,hostVersion:o,hostTheme:e,hostLanguage:t,language:t,SDKVersion:"",theme:e,version:""}})),em=mf(0,(()=>{Gg=!0,Zg(),Gg=!1;const e=Xg(),t=Jg(),n=Qg();Gg=!0;const{ua:o,browserName:r,browserVersion:i,osname:s,osversion:a}=Kg,l=T(e,t,n,{ua:o,browserName:r,browserVersion:i,uniPlatform:"web",uniCompileVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion,fontSizeSetting:void 0,osName:s.toLocaleLowerCase(),osVersion:a,osLanguage:void 0,osTheme:void 0});return delete l.screenTop,delete l.enableDebug,__uniConfig.darkmode||delete l.theme,function(e){let t={};return R(e)&&Object.keys(e).sort().forEach((n=>{const o=n;t[o]=e[o]})),Object.keys(t)?t:e}(l)})),tm=vf("getSystemInfo",((e,{resolve:t})=>t(em())));const nm=mf(0,((e,t)=>{const n=typeof t,o="string"===n?t:JSON.stringify({type:n,data:t});localStorage.setItem(e,o)}));function om(e){const t=localStorage&&localStorage.getItem(e);if(!I(t))throw new Error("data not found");let n=t;try{const e=function(e){const t=["object","string","number","boolean","undefined"];try{const n=I(e)?JSON.parse(e):e,o=n.type;if(t.indexOf(o)>=0){const e=Object.keys(n);if(2===e.length&&"data"in n){if(typeof n.data===o)return n.data;if("object"===o&&/^\d{4}-\d{2}-\d{2}T\d{2}\:\d{2}\:\d{2}\.\d{3}Z$/.test(n.data))return new Date(n.data)}else if(1===e.length)return""}}catch(n){}}(JSON.parse(t));void 0!==e&&(n=e)}catch(o){}return n}const rm=mf(0,(e=>{try{return om(e)}catch(t){return""}})),im=mf(0,(e=>{localStorage&&localStorage.removeItem(e)})),sm=mf(0,(()=>{localStorage&&localStorage.clear()})),am=vf("hideKeyboard",((e,{resolve:t,reject:n})=>{const o=document.activeElement;!o||"TEXTAREA"!==o.tagName&&"INPUT"!==o.tagName||(o.blur(),t())})),lm={image:{jpg:"jpeg",jpe:"jpeg",pbm:"x-portable-bitmap",pgm:"x-portable-graymap",pnm:"x-portable-anymap",ppm:"x-portable-pixmap",psd:"vnd.adobe.photoshop",pic:"x-pict",rgb:"x-rgb",svg:"svg+xml",svgz:"svg+xml",tif:"tiff",xif:"vnd.xiff",wbmp:"vnd.wap.wbmp",wdp:"vnd.ms-photo",xbm:"x-xbitmap",ico:"x-icon"},video:{"3g2":"3gpp2","3gp":"3gpp",avi:"x-msvideo",f4v:"x-f4v",flv:"x-flv",jpgm:"jpm",jpgv:"jpeg",m1v:"mpeg",m2v:"mpeg",mpe:"mpeg",mpg:"mpeg",mpg4:"mpeg",m4v:"x-m4v",mkv:"x-matroska",mov:"quicktime",qt:"quicktime",movie:"x-sgi-movie",mp4v:"mp4",ogv:"ogg",smv:"x-smv",wm:"x-ms-wm",wmv:"x-ms-wmv",wmx:"x-ms-wmx",wvx:"x-ms-wvx"}};function cm({count:e,sourceType:t,type:n,extension:o}){const r=document.createElement("input");return r.type="file",le(r,{position:"absolute",visibility:"hidden",zIndex:"-999",width:"0",height:"0",top:"0",left:"0"}),r.accept=o.map((e=>{if("all"!==n){const t=e.replace(".","");return`${n}/${lm[n][t]||t}`}return function(){const e=window.navigator.userAgent.toLowerCase().match(/MicroMessenger/i);return!(!e||"micromessenger"!==e[0])}()?".":0===e.indexOf(".")?e:`.${e}`})).join(","),e&&e>1&&(r.multiple=!0),"all"!==n&&t instanceof Array&&1===t.length&&"camera"===t[0]&&r.setAttribute("capture","camera"),r}$h();let um=null;const fm=vf("chooseFile",(({count:e,sourceType:t,type:n,extension:o},{resolve:r,reject:i})=>{kl();const{t:s}=_l();um&&(document.body.removeChild(um),um=null),um=cm({count:e,sourceType:t,type:n,extension:o}),document.body.appendChild(um),um.addEventListener("change",(function(t){const n=t.target,o=[];if(n&&n.files){const t=n.files.length;for(let r=0;r<t;r++){const t=n.files[r];let i;Object.defineProperty(t,"path",{get:()=>(i=i||nh(t),i)}),r<e&&o.push(t)}}r({get tempFilePaths(){return o.map((({path:e})=>e))},tempFiles:o})})),um.click(),Dh()||console.warn(s("uni.chooseFile.notUserActivation"))}),0,Ad);let dm=null;const hm=vf("chooseImage",(({count:e,sourceType:t,extension:n},{resolve:o,reject:r})=>{kl();const{t:i}=_l();dm&&(document.body.removeChild(dm),dm=null),dm=cm({count:e,sourceType:t,extension:n,type:"image"}),document.body.appendChild(dm),dm.addEventListener("change",(function(t){const n=t.target,r=[];if(n&&n.files){const t=n.files.length;for(let o=0;o<t;o++){const t=n.files[o];let i;Object.defineProperty(t,"path",{get:()=>(i=i||nh(t),i)}),o<e&&r.push(t)}}o({get tempFilePaths(){return r.map((({path:e})=>e))},tempFiles:r})})),dm.click(),Dh()||console.warn(i("uni.chooseFile.notUserActivation"))}),0,Cd),pm={esc:["Esc","Escape"],enter:["Enter"]},gm=Object.keys(pm);function mm(){const e=tn(""),t=tn(!1),n=n=>{if(t.value)return;const o=gm.find((e=>-1!==pm[e].indexOf(n.key)));o&&(e.value=o),Tn((()=>e.value=""))};return Ao((()=>{document.addEventListener("keyup",n)})),$o((()=>{document.removeEventListener("keyup",n)})),{key:e,disable:t}}const vm=Kr("div",{class:"uni-mask"},null,-1);function ym(e,t,n){return t.onClose=(...e)=>(t.visible=!1,n.apply(null,e)),Ss(lo({setup:()=>()=>(Fr(),zr(e,t,null,16))}))}function bm(e){let t=document.getElementById(e);return t||(t=document.createElement("div"),t.id=e,document.body.append(t)),t}function _m(e,{onEsc:t,onEnter:n}){const o=tn(e.visible),{key:r,disable:i}=mm();return Xn((()=>e.visible),(e=>o.value=e)),Xn((()=>o.value),(e=>i.value=!e)),Un((()=>{const{value:e}=r;"esc"===e?t&&t():"enter"===e&&n&&n()})),o}let wm=null;const xm=vf("chooseVideo",(({sourceType:e,extension:t},{resolve:n,reject:o})=>{kl();const{t:r}=_l();wm&&(document.body.removeChild(wm),wm=null),wm=cm({sourceType:e,extension:t,type:"video"}),document.body.appendChild(wm),wm.addEventListener("change",(function(e){const t=e.target.files[0];let o="";const r={tempFilePath:o,tempFile:t,size:t.size,duration:0,width:0,height:0,name:t.name};Object.defineProperty(r,"tempFilePath",{get(){return o=o||nh(this.tempFile),o}});const i=document.createElement("video");if(void 0!==i.onloadedmetadata){const e=nh(t);i.onloadedmetadata=function(){oh(e),n(T(r,{duration:i.duration||0,width:i.videoWidth||0,height:i.videoHeight||0}))},setTimeout((()=>{i.onloadedmetadata=null,oh(e),n(r)}),300),i.src=e}else n(r)})),wm.click(),Dh()||console.warn(r("uni.chooseFile.notUserActivation"))}),0,Od),Tm=gf("request",(({url:e,data:t,header:n,method:o,dataType:r,responseType:i,withCredentials:s,timeout:a=__uniConfig.networkTimeout.request},{resolve:l,reject:c})=>{let u=null;const f=function(e){const t=Object.keys(e).find((e=>"content-type"===e.toLowerCase()));if(!t)return;const n=e[t];if(0===n.indexOf("application/json"))return"json";if(0===n.indexOf("application/x-www-form-urlencoded"))return"urlencoded";return"string"}(n);if("GET"!==o)if(I(t)||t instanceof ArrayBuffer)u=t;else if("json"===f)try{u=JSON.stringify(t)}catch(g){u=t.toString()}else if("urlencoded"===f){const e=[];for(const n in t)k(t,n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));u=e.join("&")}else u=t.toString();const d=new XMLHttpRequest,h=new Sm(d);d.open(o,e);for(const m in n)k(n,m)&&d.setRequestHeader(m,n[m]);const p=setTimeout((function(){d.onload=d.onabort=d.onerror=null,h.abort(),c("timeout")}),a);return d.responseType=i,d.onload=function(){clearTimeout(p);const e=d.status;let t="text"===i?d.responseText:d.response;if("text"===i&&"json"===r)try{t=JSON.parse(t)}catch(g){}l({data:t,statusCode:e,header:Em(d.getAllResponseHeaders()),cookies:[]})},d.onabort=function(){clearTimeout(p),c("abort")},d.onerror=function(){clearTimeout(p),c()},d.withCredentials=s,d.send(u),h}),0,Dd);class Sm{constructor(e){this._xhr=e}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}function Em(e){const t={};return e.split("\n").forEach((e=>{const n=e.match(/(\S+\s*):\s*(.*)/);n&&3===n.length&&(t[n[1]]=n[2])})),t}class km{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){P(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const Cm=gf("downloadFile",(({url:e,header:t,timeout:n=__uniConfig.networkTimeout.downloadFile},{resolve:o,reject:r})=>{var i,s=new XMLHttpRequest,a=new km(s);return s.open("GET",e,!0),Object.keys(t).forEach((e=>{s.setRequestHeader(e,t[e])})),s.responseType="blob",s.onload=function(){clearTimeout(i);const t=s.status,n=this.response;let r;const a=s.getResponseHeader("content-disposition");if(a){const e=a.match(/filename="?(\S+)"?\b/);e&&(r=e[1])}n.name=r||function(e){const t=(e=e.split("#")[0].split("?")[0]).split("/");return t[t.length-1]}(e),o({statusCode:t,tempFilePath:nh(n)})},s.onabort=function(){clearTimeout(i),r("abort")},s.onerror=function(){clearTimeout(i),r()},s.onprogress=function(e){a._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesWritten:n,totalBytesExpectedToWrite:o})}))},s.send(),i=setTimeout((function(){s.onprogress=s.onload=s.onabort=s.onerror=null,a.abort(),r("timeout")}),n),a}),0,Ld);class Om{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){P(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._isAbort=!0,this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const Mm=gf("uploadFile",(({url:e,file:t,filePath:n,name:o,files:r,header:i,formData:s,timeout:a=__uniConfig.networkTimeout.uploadFile},{resolve:l,reject:c})=>{var u=new Om;return C(r)&&r.length||(r=[{name:o,file:t,uri:n}]),Promise.all(r.map((({file:e,uri:t})=>e instanceof Blob?Promise.resolve(th(e)):eh(t)))).then((function(t){var n,o=new XMLHttpRequest,f=new FormData;Object.keys(s).forEach((e=>{f.append(e,s[e])})),Object.values(r).forEach((({name:e},n)=>{const o=t[n];f.append(e||"file",o,o.name||`file-${Date.now()}`)})),o.open("POST",e),Object.keys(i).forEach((e=>{o.setRequestHeader(e,i[e])})),o.upload.onprogress=function(e){u._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesSent:n,totalBytesExpectedToSend:o})}))},o.onerror=function(){clearTimeout(n),c()},o.onabort=function(){clearTimeout(n),c("abort")},o.onload=function(){clearTimeout(n);const e=o.status;l({statusCode:e,data:o.responseText||o.response})},u._isAbort?c("abort"):(n=setTimeout((function(){o.upload.onprogress=o.onload=o.onabort=o.onerror=null,u.abort(),c("timeout")}),a),o.send(f),u._xhr=o)})).catch((()=>{setTimeout((()=>{c("file error")}),0)})),u}),0,Bd),Am=vf("navigateBack",((e,{resolve:t,reject:n})=>{let o=!0;return!0===Sc("onBackPress",{from:e.from||"navigateBack"})&&(o=!1),o?(Og().$router.go(-e.delta),t()):n("onBackPress")}),0,zd);function Pm({type:e,url:t,tabBarText:n,events:o,isAutomatedTesting:r},i){const s=Og().$router,{path:a,query:l}=function(e){const[t,n]=e.split("?",2);return{path:t,query:we(n||"")}}(t);return new Promise(((t,c)=>{const u=function(e,t){return{__id__:t||++sg,__type__:e}}(e,i);s["navigateTo"===e?"push":"replace"]({path:a,query:l,state:u,force:!0}).then((i=>{if(pa(i))return c(i.message);if("switchTab"===e&&(s.currentRoute.value.meta.tabBarText=n),"navigateTo"===e){const e=s.currentRoute.value.meta;return e.eventChannel?o&&(Object.keys(o).forEach((t=>{e.eventChannel._addListener(t,"on",o[t])})),e.eventChannel._clearCache()):e.eventChannel=new Te(u.__id__,o),t(r?{__id__:u.__id__}:{eventChannel:e.eventChannel})}return r?t({__id__:u.__id__}):t()}))}))}const Im=vf("navigateTo",(({url:e,events:t,isAutomatedTesting:n},{resolve:o,reject:r})=>Pm({type:"navigateTo",url:e,events:t,isAutomatedTesting:n}).then(o).catch(r)),0,Rd);const $m=vf("redirectTo",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>(function(){const e=yc();if(!e)return;const t=e.$page;ig(cg(t.path,t.id))}(),Pm({type:"redirectTo",url:e,isAutomatedTesting:t}).then(n).catch(o))),0,Nd);const Dm=vf("reLaunch",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>(function(){const e=og().keys();for(const t of e)ig(t)}(),Pm({type:"reLaunch",url:e,isAutomatedTesting:t}).then(n).catch(o))),0,jd);function Lm(e,t){return e===t.fullPath||"/"===e&&t.meta.isEntry}const Bm=vf("switchTab",(({url:e,tabBarText:t,isAutomatedTesting:n},{resolve:o,reject:r})=>(function(){const e=wc();if(!e)return;const t=og(),n=t.keys();for(const o of n){const e=t.get(o);e.$.__isTabBar?e.$.__isActive=!1:ig(o)}e.$.__isTabBar&&(e.$.__isVisible=!1,Sc(e,"onHide"))}(),Pm({type:"switchTab",url:e,tabBarText:t,isAutomatedTesting:n},function(e){const t=og().values();for(const n of t){const t=n.$page;if(Lm(e,t))return n.$.__isActive=!0,t.id}}(e)).then(o).catch(r))),0,qd);function Fm(e){__uniConfig.darkmode&&Pv.on("onThemeChange",e)}function Rm(e){Pv.off("onThemeChange",e)}function Nm(e){let t={};return __uniConfig.darkmode&&(t=Ie(e,__uniConfig.themeConfig,Ug())),__uniConfig.darkmode?t:e}const jm={light:{cancelColor:"#000000"},dark:{cancelColor:"rgb(170, 170, 170)"}},qm=lo({props:{title:{type:String,default:""},content:{type:String,default:""},showCancel:{type:Boolean,default:!0},cancelText:{type:String,default:"Cancel"},cancelColor:{type:String,default:"#000000"},confirmText:{type:String,default:"OK"},confirmColor:{type:String,default:"#007aff"},visible:{type:Boolean},editable:{type:Boolean,default:!1},placeholderText:{type:String,default:""}},setup(e,{emit:t}){const n=tn(""),o=()=>s.value=!1,r=()=>(o(),t("close","cancel")),i=()=>(o(),t("close","confirm",n.value)),s=_m(e,{onEsc:r,onEnter:()=>{!e.editable&&i()}}),a=function(e){const t=tn(e.cancelColor),n=({theme:e})=>{((e,t)=>{t.value=jm[e].cancelColor})(e,t)};return Un((()=>{e.visible?(t.value=e.cancelColor,"#000"===e.cancelColor&&("dark"===Ug()&&n({theme:"dark"}),Fm(n))):Rm(n)})),t}(e);return()=>{const{title:t,content:o,showCancel:l,confirmText:c,confirmColor:u,editable:f,placeholderText:d}=e;return n.value=o,Kr(Ui,{name:"uni-fade"},{default:()=>[No(Kr("uni-modal",{onTouchmove:oc},[vm,Kr("div",{class:"uni-modal"},[t&&Kr("div",{class:"uni-modal__hd"},[Kr("strong",{class:"uni-modal__title",textContent:t},null,8,["textContent"])]),f?Kr("textarea",{class:"uni-modal__textarea",rows:"1",placeholder:d,value:o,onInput:e=>n.value=e.target.value},null,40,["placeholder","value","onInput"]):Kr("div",{class:"uni-modal__bd",onTouchmovePassive:rc,textContent:o},null,40,["onTouchmovePassive","textContent"]),Kr("div",{class:"uni-modal__ft"},[l&&Kr("div",{style:{color:a.value},class:"uni-modal__btn uni-modal__btn_default",onClick:r},[e.cancelText],12,["onClick"]),Kr("div",{style:{color:u},class:"uni-modal__btn uni-modal__btn_primary",onClick:i},[c],12,["onClick"])])])],40,["onTouchmove"]),[[_s,s.value]])]})}}});let zm;const Hm=ce((()=>{Pv.on("onHidePopup",(()=>zm.visible=!1))}));let Vm;function Wm(e,t){const n="confirm"===e,o={confirm:n,cancel:"cancel"===e};n&&zm.editable&&(o.content=t),Vm&&Vm(o)}const Um=vf("showModal",((e,{resolve:t})=>{Hm(),Vm=t,zm?(T(zm,e),zm.visible=!0):(zm=qt(e),Tn((()=>(ym(qm,zm,Wm).mount(bm("u-a-m")),Tn((()=>zm.visible=!0))))))}),0,Kd),Ym={title:{type:String,default:""},icon:{default:"success",validator:e=>-1!==Gd.indexOf(e)},image:{type:String,default:""},duration:{type:Number,default:1500},mask:{type:Boolean,default:!1},visible:{type:Boolean}},Xm={light:"#fff",dark:"rgba(255,255,255,0.9)"},Km=e=>Xm[e],Gm=lo({name:"Toast",props:Ym,setup(e){Tl(),Sl();const{Icon:t}=function(e){const t=tn(Km(Ug())),n=({theme:e})=>t.value=Km(e);Un((()=>{e.visible?Fm(n):Rm(n)}));return{Icon:vi((()=>{switch(e.icon){case"success":return Kr(gc(hc,t.value,38),{class:"uni-toast__icon"});case"error":return Kr(gc(pc,t.value,38),{class:"uni-toast__icon"});case"loading":return Kr("i",{class:["uni-toast__icon","uni-loading"]},null,2);default:return null}}))}}(e),n=_m(e,{});return()=>{const{mask:o,duration:r,title:i,image:s}=e;return Kr(Ui,{name:"uni-fade"},{default:()=>[No(Kr("uni-toast",{"data-duration":r},[o?Kr("div",{class:"uni-mask",style:"background: transparent;",onTouchmove:oc},null,40,["onTouchmove"]):"",s||t.value?Kr("div",{class:"uni-toast"},[s?Kr("img",{src:s,class:"uni-toast__icon"},null,10,["src"]):t.value,Kr("p",{class:"uni-toast__content"},[i])]):Kr("div",{class:"uni-sample-toast"},[Kr("p",{class:"uni-simple-toast__text"},[i])])],8,["data-duration"]),[[_s,n.value]])]})}}});let Zm,Jm,Qm="";const ev=Le();function tv(e){Zm?T(Zm,e):(Zm=qt(T(e,{visible:!1})),Tn((()=>{ev.run((()=>{Xn([()=>Zm.visible,()=>Zm.duration],(([e,t])=>{if(e){if(Jm&&clearTimeout(Jm),"onShowLoading"===Qm)return;Jm=setTimeout((()=>{av("onHideToast")}),t)}else Jm&&clearTimeout(Jm)}))})),Pv.on("onHidePopup",(()=>av("onHidePopup"))),ym(Gm,Zm,(()=>{})).mount(bm("u-a-t"))}))),setTimeout((()=>{Zm.visible=!0}),10)}const nv=vf("showToast",((e,{resolve:t,reject:n})=>{tv(e),Qm="onShowToast",t()}),0,Zd),ov={icon:"loading",duration:1e8,image:""},rv=vf("showLoading",((e,{resolve:t,reject:n})=>{T(e,ov),tv(e),Qm="onShowLoading",t()}),0,Xd),iv=vf("hideToast",((e,{resolve:t,reject:n})=>{av("onHideToast"),t()})),sv=vf("hideLoading",((e,{resolve:t,reject:n})=>{av("onHideLoading"),t()}));function av(e){const{t:t}=_l();if(!Qm)return;let n="";if("onHideToast"===e&&"onShowToast"!==Qm?n=t("uni.showToast.unpaired"):"onHideLoading"===e&&"onShowLoading"!==Qm&&(n=t("uni.showLoading.unpaired")),n)return console.warn(n);Qm="",setTimeout((()=>{Zm.visible=!1}),10)}const lv=vf("loadFontFace",(({family:e,source:t,desc:n},{resolve:o,reject:r})=>{(function(e,t,n){const o=document.fonts;if(o){const r=new FontFace(e,t,n);return r.load().then((()=>{o.add&&o.add(r)}))}return new Promise((o=>{const r=document.createElement("style"),i=[];if(n){const{style:e,weight:t,stretch:o,unicodeRange:r,variant:s,featureSettings:a}=n;e&&i.push(`font-style:${e}`),t&&i.push(`font-weight:${t}`),o&&i.push(`font-stretch:${o}`),r&&i.push(`unicode-range:${r}`),s&&i.push(`font-variant:${s}`),a&&i.push(`font-feature-settings:${a}`)}r.innerText=`@font-face{font-family:"${e}";src:${t};${i.join(";")}}`,document.head.appendChild(r),o()}))})(e,t,n).then((()=>{o()})).catch((e=>{r(`loadFontFace:fail ${e}`)}))}));function cv(e){function t(){var t;t=e.navigationBar.titleText,document.title=t,Pv.emit("onNavigationBarChange",{titleText:t})}Un(t),yo(t)}const uv=vf("setNavigationBarTitle",((e,{resolve:t,reject:n})=>{!function(e,t,n,o,r){if(!e)return r("page not found");const{navigationBar:i}=e;switch(t){case"setNavigationBarColor":const{frontColor:e,backgroundColor:t,animation:o}=n,{duration:r,timingFunc:s}=o;e&&(i.titleColor="#000000"===e?"#000000":"#ffffff"),t&&(i.backgroundColor=t),i.duration=r+"ms",i.timingFunc=s;break;case"showNavigationBarLoading":i.loading=!0;break;case"hideNavigationBarLoading":i.loading=!1;break;case"setNavigationBarTitle":const{title:a}=n;i.titleText=a}o()}(bc(),"setNavigationBarTitle",e,t,n)})),fv=vf("stopPullDownRefresh",((e,{resolve:t})=>{Pv.invokeViewMethod("stopPullDownRefresh",{},_c()),t()})),dv=ru({name:"TabBar",setup(){const e=tn([]),t=Xp(),n=function(e,t){const n=Vt(e),o=n?qt(Nm(e)):Nm(e);return __uniConfig.darkmode&&n&&Xn(e,(e=>{const t=Nm(e);for(const n in t)o[n]=t[n]})),t&&Fm(t),o}(t,(()=>{const e=Nm(t);n.backgroundColor=e.backgroundColor,n.borderStyle=e.borderStyle,n.color=e.color,n.selectedColor=e.selectedColor,n.blurEffect=e.blurEffect,e.list&&e.list.length&&e.list.forEach(((e,t)=>{n.list[t].iconPath=e.iconPath,n.list[t].selectedIconPath=e.selectedIconPath}))}));!function(e,t){function n(){let n=[];n=e.list.filter((e=>!1!==e.visible)),t.value=n}tn(T({type:"midButton"},e.midButton)),Un(n)}(n,e),function(e){Xn((()=>e.shown),(t=>{cc({"--window-bottom":tg(t?parseInt(e.height):0)})}))}(n);const o=function(e,t,n){return Un((()=>{const o=e.meta;if(o.isTabBar){const e=o.route,r=n.value.findIndex((t=>t.pagePath===e));t.selectedIndex=r}})),(t,n)=>()=>{const{pagePath:o,text:r}=t;let i=ae(o);i===__uniRoutes[0].alias&&(i="/"),e.path!==i?Bm({from:"tabBar",url:i,tabBarText:r}):Sc("onTabItemTap",{index:n,text:r,pagePath:o})}}(pl(),n,e),{style:r,borderStyle:i,placeholderStyle:s}=function(e){const t=vi((()=>{let t=e.backgroundColor;const n=e.blurEffect;return t||Qp&&n&&"none"!==n&&(t=hv[n]),{backgroundColor:t||"#f7f7fa",backdropFilter:"none"!==n?"blur(10px)":n}})),n=vi((()=>{const{borderStyle:t}=e;return{backgroundColor:pv[t]||t}})),o=vi((()=>({height:e.height})));return{style:t,borderStyle:n,placeholderStyle:o}}(n);return Ao((()=>{n.iconfontSrc&&lv({family:"UniTabbarIconFont",source:`url("${n.iconfontSrc}")`})})),()=>{const t=function(e,t,n){const{selectedIndex:o,selectedColor:r,color:i}=e;return n.value.map(((n,s)=>{const a=o===s;return function(e,t,n,o,r,i,s,a){return Kr("div",{key:s,class:"uni-tabbar__item",onClick:a(r,s)},[gv(e,t||"",n,o,r,i)],8,["onClick"])}(a?r:i,a&&n.selectedIconPath||n.iconPath||"",n.iconfont?a&&n.iconfont.selectedText||n.iconfont.text:void 0,n.iconfont?a&&n.iconfont.selectedColor||n.iconfont.color:void 0,n,e,s,t)}))}(n,o,e);return Kr("uni-tabbar",{class:"uni-tabbar-"+n.position},[Kr("div",{class:"uni-tabbar",style:r.value},[Kr("div",{class:"uni-tabbar-border",style:i.value},null,4),t],4),Kr("div",{class:"uni-placeholder",style:s.value},null,4)],2)}}});const hv={dark:"rgb(0, 0, 0, 0.8)",light:"rgb(250, 250, 250, 0.8)",extralight:"rgb(250, 250, 250, 0.8)"},pv={white:"rgba(255, 255, 255, 0.33)",black:"rgba(0, 0, 0, 0.33)"};function gv(e,t,n,o,r,i){const{height:s}=i;return Kr("div",{class:"uni-tabbar__bd",style:{height:s}},[n?vv(n,o||"rgb(0, 0, 0, 0.8)",r,i):t&&mv(t,r,i),r.text&&yv(e,r,i),r.redDot&&bv(r.badge)],4)}function mv(e,t,n){const{type:o,text:r}=t,{iconWidth:i}=n;return Kr("div",{class:"uni-tabbar__icon"+(r?" uni-tabbar__icon__diff":""),style:{width:i,height:i}},["midButton"!==o&&Kr("img",{src:vu(e)},null,8,["src"])],6)}function vv(e,t,n,o){var r;const{type:i,text:s}=n,{iconWidth:a}=o,l="uni-tabbar__icon"+(s?" uni-tabbar__icon__diff":""),c={width:a,height:a},u={fontSize:(null==(r=n.iconfont)?void 0:r.fontSize)||a,color:t};return Kr("div",{class:l,style:c},["midButton"!==i&&Kr("div",{class:"uni-tabbar__iconfont",style:u},[e],4)],6)}function yv(e,t,n){const{iconPath:o,text:r}=t,{fontSize:i,spacing:s}=n;return Kr("div",{class:"uni-tabbar__label",style:{color:e,fontSize:i,lineHeight:o?"normal":1.8,marginTop:o?s:"inherit"}},[r],4)}function bv(e){return Kr("div",{class:"uni-tabbar__reddot"+(e?" uni-tabbar__badge":"")},[e],2)}const _v=ru({name:"Layout",setup(e,{emit:t}){const n=tn(null);lc({"--status-bar-height":"0px","--top-window-height":"0px","--window-left":"0px","--window-right":"0px","--window-margin":"0px","--tab-bar-height":"0px"});const o=function(){const e=pl();return{routeKey:vi((()=>cg("/"+e.meta.route,Up()))),isTabBar:vi((()=>e.meta.isTabBar)),routeCache:fg}}(),{layoutState:r,windowState:i}=function(){Wp();{const e=qt({marginWidth:0,leftWindowWidth:0,rightWindowWidth:0});return Xn((()=>e.marginWidth),(e=>lc({"--window-margin":e+"px"}))),Xn((()=>e.leftWindowWidth+e.marginWidth),(e=>{lc({"--window-left":e+"px"})})),Xn((()=>e.rightWindowWidth+e.marginWidth),(e=>{lc({"--window-right":e+"px"})})),{layoutState:e,windowState:vi((()=>({})))}}}();!function(e,t){const n=Wp();function o(){const o=document.body.clientWidth,r=rg();let i={};if(r.length>0){i=r[r.length-1].$page.meta}else{const e=Ac(n.path,!0);e&&(i=e.meta)}const s=parseInt(String((k(i,"maxWidth")?i.maxWidth:__uniConfig.globalStyle.maxWidth)||Number.MAX_SAFE_INTEGER));let a=!1;a=o>s,a&&s?(e.marginWidth=(o-s)/2,Tn((()=>{const e=t.value;e&&e.setAttribute("style","max-width:"+s+"px;margin:0 auto;")}))):(e.marginWidth=0,Tn((()=>{const e=t.value;e&&e.removeAttribute("style")})))}Xn([()=>n.path],o),Ao((()=>{o(),window.addEventListener("resize",o)}))}(r,n);const s=function(e){const t=Wp(),n=Xp(),o=vi((()=>t.meta.isTabBar&&n.shown));return lc({"--tab-bar-height":n.height}),o}(),a=function(e){const t=tn(!1);return vi((()=>({"uni-app--showtabbar":e&&e.value,"uni-app--maxwidth":t.value})))}(s);return()=>{const e=function(e,t,n,o,r,i){return function({routeKey:e,isTabBar:t,routeCache:n}){return Kr(fl,null,{default:Rn((({Component:o})=>[(Fr(),zr(mo,{matchBy:"key",cache:n},[(Fr(),zr(Ho(o),{type:t.value?"tabBar":"",key:e.value}))],1032,["cache"]))])),_:1})}(e)}(o),t=function(e){return No(Kr(dv,null,null,512),[[_s,e.value]])}(s);return Kr("uni-app",{ref:n,class:a.value},[e,t],2)}}});function wv(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!Hr(e)}function xv(e){if(e.mode===Ev.TIME)return"00:00";if(e.mode===Ev.DATE){const t=(new Date).getFullYear()-150;switch(e.fields){case kv.YEAR:return t.toString();case kv.MONTH:return t+"-01";default:return t+"-01-01"}}return""}function Tv(e){if(e.mode===Ev.TIME)return"23:59";if(e.mode===Ev.DATE){const t=(new Date).getFullYear()+150;switch(e.fields){case kv.YEAR:return t.toString();case kv.MONTH:return t+"-12";default:return t+"-12-31"}}return""}function Sv(e,t,n,o){const r=e.mode===Ev.DATE?"-":":",i=e.mode===Ev.DATE?t.dateArray:t.timeArray;let s;if(e.mode===Ev.TIME)s=2;else switch(e.fields){case kv.YEAR:s=1;break;case kv.MONTH:s=2;break;default:s=3}const a=String(n).split(r);let l=[];for(let c=0;c<s;c++){const e=a[c];l.push(i[c].indexOf(e))}return l.indexOf(-1)>=0&&(l=o?Sv(e,t,o):l.map((()=>0))),l}const Ev={SELECTOR:"selector",MULTISELECTOR:"multiSelector",TIME:"time",DATE:"date"},kv={YEAR:"year",MONTH:"month",DAY:"day"},Cv={PICKER:"picker",SELECT:"select"},Ov=ou({name:"Picker",compatConfig:{MODE:3},props:{name:{type:String,default:""},range:{type:Array,default:()=>[]},rangeKey:{type:String,default:""},value:{type:[Number,String,Array],default:0},mode:{type:String,default:Ev.SELECTOR,validator:e=>Object.values(Ev).includes(e)},fields:{type:String,default:""},start:{type:String,default:e=>xv(e)},end:{type:String,default:e=>Tv(e)},disabled:{type:[Boolean,String],default:!1},selectorType:{type:String,default:""}},emits:["change","cancel","columnchange"],setup(e,{emit:t,slots:n}){Cl();const{t:o}=_l(),r=tn(null),i=tn(null),s=tn(null),a=tn(null),l=tn(!1),{state:c,rangeArray:u}=function(e){const t=qt({valueSync:void 0,visible:!1,contentVisible:!1,popover:null,valueChangeSource:"",timeArray:[],dateArray:[],valueArray:[],oldValueArray:[],isDesktop:!1,popupStyle:{content:{},triangle:{}}}),n=vi((()=>{let n=e.range;switch(e.mode){case Ev.SELECTOR:return[n];case Ev.MULTISELECTOR:return n;case Ev.TIME:return t.timeArray;case Ev.DATE:{const n=t.dateArray;switch(e.fields){case kv.YEAR:return[n[0]];case kv.MONTH:return[n[0],n[1]];default:return[n[0],n[1],n[2]]}}}return[]}));return{state:t,rangeArray:n}}(e),f=cu(r,t),{system:d,selectorTypeComputed:h,_show:p,_l10nColumn:g,_l10nItem:m,_input:v,_fixInputPosition:y,_pickerViewChange:b,_cancel:_,_change:w,_resetFormData:x,_getFormData:S,_createTime:E,_createDate:k,_setValueSync:O}=function(e,t,n,o,r,i,s){const a=function(){const e=tn(!1);return e.value=(()=>0===String(navigator.vendor).indexOf("Apple")&&navigator.maxTouchPoints>0)(),e}(),l=function(){const e=tn("");return e.value=(()=>{if(/win|mac/i.test(navigator.platform)){if("Google Inc."===navigator.vendor)return"chrome";if(/Firefox/.test(navigator.userAgent))return"firefox"}return""})(),e}(),c=vi((()=>{const t=e.selectorType;return Object.values(Cv).includes(t)?t:a.value?Cv.PICKER:Cv.SELECT})),u=vi((()=>e.mode===Ev.DATE&&!Object.values(kv).includes(e.fields)&&t.isDesktop?l.value:"")),f=vi((()=>Sv(e,t,e.start,xv(e)))),d=vi((()=>Sv(e,t,e.end,Tv(e))));function h(n){if(e.disabled)return;t.valueChangeSource="";let o=r.value,i=n.currentTarget;o.remove(),(document.querySelector("uni-app")||document.body).appendChild(o),o.style.display="block";const s=i.getBoundingClientRect();t.popover={top:s.top,left:s.left,width:s.width,height:s.height},setTimeout((()=>{t.visible=!0}),20)}function p(){return{value:t.valueSync,key:e.name}}function g(){switch(e.mode){case Ev.SELECTOR:t.valueSync=0;break;case Ev.MULTISELECTOR:t.valueSync=e.value.map((e=>0));break;case Ev.DATE:case Ev.TIME:t.valueSync=""}}function m(){let e=[],n=[];for(let t=0;t<24;t++)e.push((t<10?"0":"")+t);for(let t=0;t<60;t++)n.push((t<10?"0":"")+t);t.timeArray.push(e,n)}function v(){let t=(new Date).getFullYear(),n=t-150,o=t+150;if(e.start){const t=new Date(e.start).getFullYear();!isNaN(t)&&t<n&&(n=t)}if(e.end){const t=new Date(e.end).getFullYear();!isNaN(t)&&t>o&&(o=t)}return{start:n,end:o}}function y(){let e=[];const n=v();for(let t=n.start,i=n.end;t<=i;t++)e.push(String(t));let o=[];for(let t=1;t<=12;t++)o.push((t<10?"0":"")+t);let r=[];for(let t=1;t<=31;t++)r.push((t<10?"0":"")+t);t.dateArray.push(e,o,r)}function b(e){return 60*e[0]+e[1]}function _(e){const t=31;return e[0]*t*12+(e[1]||0)*t+(e[2]||0)}function w(e,t){for(let n=0;n<e.length&&n<t.length;n++)e[n]=t[n]}function x(){let n=e.value;switch(e.mode){case Ev.MULTISELECTOR:{C(n)||(n=t.valueArray),C(t.valueSync)||(t.valueSync=[]);const o=t.valueSync.length=Math.max(n.length,e.range.length);for(let r=0;r<o;r++){const o=Number(n[r]),i=Number(t.valueSync[r]),s=isNaN(o)?isNaN(i)?0:i:o,a=e.range[r]?e.range[r].length-1:0;t.valueSync.splice(r,1,s<0||s>a?0:s)}}break;case Ev.TIME:case Ev.DATE:t.valueSync=String(n);break;default:{const e=Number(n);t.valueSync=e<0?0:e;break}}}function T(){let n,o=t.valueSync;switch(e.mode){case Ev.MULTISELECTOR:n=[...o];break;case Ev.TIME:n=Sv(e,t,o,fe({mode:Ev.TIME}));break;case Ev.DATE:n=Sv(e,t,o,fe({mode:Ev.DATE}));break;default:n=[o]}t.oldValueArray=[...n],t.valueArray=[...n]}function S(){let n=t.valueArray;switch(e.mode){case Ev.SELECTOR:return n[0];case Ev.MULTISELECTOR:return n.map((e=>e));case Ev.TIME:return t.valueArray.map(((e,n)=>t.timeArray[n][e])).join(":");case Ev.DATE:return t.valueArray.map(((e,n)=>t.dateArray[n][e])).join("-")}}function E(){O(),t.valueChangeSource="click";const e=S();t.valueSync=C(e)?e.map((e=>e)):e,n("change",{},{value:e})}function k(e){if("firefox"===u.value&&e){const{top:n,left:o,width:r,height:i}=t.popover,{pageX:s,pageY:a}=e;if(s>o&&s<o+r&&a>n&&a<n+i)return}O(),n("cancel",{},{})}function O(){t.visible=!1,setTimeout((()=>{let e=r.value;e.remove(),o.value.prepend(e),e.style.display="none"}),260)}function M(){e.mode===Ev.SELECTOR&&c.value===Cv.SELECT&&(i.value.scrollTop=34*t.valueArray[0])}function A(e){const n=e.target;t.valueSync=n.value,Tn((()=>{E()}))}function P(e){if("chrome"===u.value){const t=o.value.getBoundingClientRect(),n=32;s.value.style.left=e.clientX-t.left-1.5*n+"px",s.value.style.top=e.clientY-t.top-.5*n+"px"}}function I(e){t.valueArray=$(e.detail.value,!0)}function $(t,n){const{getLocale:o}=_l();if(e.mode===Ev.DATE){const r=o();if(!r.startsWith("zh"))switch(e.fields){case kv.YEAR:return t;case kv.MONTH:return[t[1],t[0]];default:switch(r){case"es":case"fr":return[t[2],t[1],t[0]];default:return n?[t[2],t[0],t[1]]:[t[1],t[2],t[0]]}}}return t}function D(t,n){const{getLocale:o}=_l();if(e.mode===Ev.DATE){const r=o();if(r.startsWith("zh")){return t+["年","月","日"][n]}if(e.fields!==kv.YEAR&&n===(e.fields===kv.MONTH||"es"!==r&&"fr"!==r?0:1)){let e;switch(r){case"es":e=["enero","febrero","marzo","abril","mayo","junio","​​julio","agosto","septiembre","octubre","noviembre","diciembre"];break;case"fr":e=["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"];break;default:e=["January","February","March","April","May","June","July","August","September","October","November","December"]}return e[Number(t)-1]}}return t}return Xn((()=>t.visible),(e=>{e?(clearTimeout(Mv),t.contentVisible=e,M()):Mv=setTimeout((()=>{t.contentVisible=e}),300)})),Xn([()=>e.mode,()=>e.value,()=>e.range],x,{deep:!0}),Xn((()=>t.valueSync),T,{deep:!0}),Xn((()=>t.valueArray),(o=>{if(e.mode===Ev.TIME||e.mode===Ev.DATE){const n=e.mode===Ev.TIME?b:_,o=t.valueArray,r=f.value,i=d.value;if(e.mode===Ev.DATE){const e=t.dateArray,n=e[2].length,r=Number(e[2][o[2]])||1,i=new Date(`${e[0][o[0]]}/${e[1][o[1]]}/${r}`).getDate();i<r&&(o[2]-=i+n-r)}n(o)<n(r)?w(o,r):n(o)>n(i)&&w(o,i)}o.forEach(((o,r)=>{o!==t.oldValueArray[r]&&(t.oldValueArray[r]=o,e.mode===Ev.MULTISELECTOR&&n("columnchange",{},{column:r,value:o}))}))})),{selectorTypeComputed:c,system:u,_show:h,_cancel:k,_change:E,_l10nColumn:$,_l10nItem:D,_input:A,_resetFormData:g,_getFormData:p,_createTime:m,_createDate:y,_setValueSync:x,_fixInputPosition:P,_pickerViewChange:I}}(e,c,f,r,i,s,a);!function(e,t,n){const{key:o,disable:r}=mm();Un((()=>{r.value=!e.visible})),Xn(o,(e=>{"esc"===e?t():"enter"===e&&n()}))}(c,_,w),function(e,t){const n=Wn(uu,!1);if(n){const o={reset:e,submit:()=>{const e=["",null],{key:n,value:o}=t();return""!==n&&(e[0]=n,e[1]=o),e}};n.addField(o),$o((()=>{n.removeField(o)}))}}(x,S),E(),k(),O();const M=function(e){const t=tn(0),n=tn(0),o=vi((()=>t.value>=500&&n.value>=500)),r=vi((()=>{const t={content:{transform:"",left:"",top:"",bottom:""},triangle:{left:"",top:"",bottom:"","border-width":"","border-color":""}},r=t.content,i=t.triangle,s=e.popover;function a(e){return Number(e)||0}if(o.value&&s){T(i,{position:"absolute",width:"0",height:"0","margin-left":"-6px","border-style":"solid"});const e=a(s.left),t=a(s.width),o=a(s.top),l=a(s.height),c=e+t/2;r.transform="none !important";const u=Math.max(0,c-150);r.left=`${u}px`;let f=Math.max(12,c-u);f=Math.min(288,f),i.left=`${f}px`;const d=n.value/2;o+l-d>d-o?(r.top="auto",r.bottom=n.value-o+6+"px",i.bottom="-6px",i["border-width"]="6px 6px 0 6px",i["border-color"]="#fcfcfd transparent transparent transparent"):(r.top=`${o+l+6}px`,i.top="-6px",i["border-width"]="0 6px 6px 6px",i["border-color"]="transparent transparent #fcfcfd transparent")}return t}));return Ao((()=>{const e=()=>{const{windowWidth:e,windowHeight:o,windowTop:r}=em();t.value=e,n.value=o+(r||0)};window.addEventListener("resize",e),e(),Do((()=>{window.removeEventListener("resize",e)}))})),{isDesktop:o,popupStyle:r}}(c);return Un((()=>{c.isDesktop=M.isDesktop.value,c.popupStyle=M.popupStyle.value})),$o((()=>{i.value&&i.value.remove()})),Ao((()=>{l.value=!0})),()=>{let t;const{visible:f,contentVisible:x,valueArray:T,popupStyle:S,valueSync:E}=c,{rangeKey:k,mode:C,start:O,end:M}=e,A=au(e,"disabled");return Kr("uni-picker",oi({ref:r},A,{onClick:lu(p)}),[l.value?Kr("div",{ref:i,class:["uni-picker-container",`uni-${C}-${h.value}`],onWheel:oc,onTouchmove:oc},[Kr(Ui,{name:"uni-fade"},{default:()=>[No(Kr("div",{class:"uni-mask uni-picker-mask",onClick:lu(_),onMousemove:y},null,40,["onClick","onMousemove"]),[[_s,f]])]}),d.value?null:Kr("div",{class:[{"uni-picker-toggle":f},"uni-picker-custom"],style:S.content},[Kr("div",{class:"uni-picker-header",onClick:rc},[Kr("div",{class:"uni-picker-action uni-picker-action-cancel",onClick:lu(_)},[o("uni.picker.cancel")],8,["onClick"]),Kr("div",{class:"uni-picker-action uni-picker-action-confirm",onClick:w},[o("uni.picker.done")],8,["onClick"])],8,["onClick"]),x?Kr(rp,{value:g(T),class:"uni-picker-content",onChange:b},wv(t=Uo(g(u.value),((e,t)=>{let n;return Kr(dp,{key:t},wv(n=Uo(e,((e,n)=>Kr("div",{key:n,class:"uni-picker-item"},["object"==typeof e?e[k]||"":m(e,t)]))))?n:{default:()=>[n],_:1})})))?t:{default:()=>[t],_:1},8,["value","onChange"]):null,Kr("div",{ref:s,class:"uni-picker-select",onWheel:rc,onTouchmove:rc},[Uo(u.value[0],((e,t)=>Kr("div",{key:t,class:["uni-picker-item",{selected:T[0]===t}],onClick:()=>{T[0]=t,w()}},["object"==typeof e?e[k]||"":e],10,["onClick"])))],40,["onWheel","onTouchmove"]),Kr("div",{style:S.triangle},null,4)],6)],40,["onWheel","onTouchmove"]):null,Kr("div",null,[n.default&&n.default()]),d.value?Kr("div",{class:"uni-picker-system",onMousemove:lu(y)},[Kr("input",{class:["uni-picker-system_input",d.value],ref:a,value:E,type:C,tabindex:"-1",min:O,max:M,onChange:e=>{v(e),rc(e)}},null,42,["value","type","min","max","onChange"])],40,["onMousemove"]):null],16,["onClick"])}}});let Mv;const Av=T(Ll,{publishHandler(e,t,n){Pv.subscribeHandler(e,t,n)}}),Pv=T(Vc,{publishHandler(e,t,n){Av.subscribeHandler(e,t,n)}}),Iv={name:"PageRefresh",setup(){const{pullToRefresh:e}=Hp();return{offset:e.offset,color:e.color}}},$v=(e,t)=>{const n=e.__vccOpts||e;for(const[o,r]of t)n[o]=r;return n},Dv={class:"uni-page-refresh-inner"},Lv=["fill"],Bv=[Xr("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null,-1),Xr("path",{d:"M0 0h24v24H0z",fill:"none"},null,-1)],Fv={class:"uni-page-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},Rv=["stroke"];const Nv=$v(Iv,[["render",function(e,t,n,r,i,s){return Fr(),qr("uni-page-refresh",null,[Xr("div",{style:o({"margin-top":r.offset+"px"}),class:"uni-page-refresh"},[Xr("div",Dv,[(Fr(),qr("svg",{fill:r.color,class:"uni-page-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},Bv,8,Lv)),(Fr(),qr("svg",Fv,[Xr("circle",{stroke:r.color,class:"uni-page-refresh__path",cx:"50",cy:"50",r:"20",fill:"none","stroke-width":"4","stroke-miterlimit":"10"},null,8,Rv)]))])],4)])}]]);function jv(e,t,n){const o=Array.prototype.slice.call(e.changedTouches).filter((e=>e.identifier===t))[0];return!!o&&(e.deltaY=o.pageY-n,!0)}const qv="aborting",zv="refreshing",Hv="restoring";function Vv(e){const{id:t,pullToRefresh:n}=Hp(),{range:o,height:r}=n;let i,s,a,l,c,u,f,d;Pp((()=>{d||(d=zv,m(),setTimeout((()=>{w()}),50))}),"startPullDownRefresh",!1,t),Pp((()=>{d===zv&&(v(),d=Hv,m(),function(e){if(!s)return;a.transition="-webkit-transform 0.3s",a.transform+=" scale(0.01)";const t=function(){n&&clearTimeout(n),s.removeEventListener("webkitTransitionEnd",t),a.transition="",a.transform="translate3d(-50%, 0, 0)",e()};s.addEventListener("webkitTransitionEnd",t);const n=setTimeout(t,350)}((()=>{v(),d=h=p=null})))}),"stopPullDownRefresh",!1,t),Ao((()=>{i=e.value.$el,s=i.querySelector(".uni-page-refresh"),a=s.style,l=s.querySelector(".uni-page-refresh-inner").style}));let h=null,p=null;function g(e){d&&i&&i.classList[e]("uni-page-refresh--"+d)}function m(){g("add")}function v(){g("remove")}const y=lu((e=>{const t=e.changedTouches[0];c=t.identifier,u=t.pageY,f=!([qv,zv,Hv].indexOf(d)>=0)})),b=lu((e=>{if(!f)return;if(!jv(e,c,u))return;let{deltaY:t}=e;if(0!==(document.documentElement.scrollTop||document.body.scrollTop))return void(c=null);if(t<0&&!d)return;e.preventDefault(),null===h&&(p=t,d="pulling",m()),t-=p,t<0&&(t=0),h=t;(t>=o&&"reached"!==d||t<o&&"pulling"!==d)&&(v(),d="reached"===d?"pulling":"reached",m()),function(e){if(!s)return;let t=e/o;t>1?t=1:t*=t*t;const n=Math.round(e/(o/r))||0;l.transform="rotate("+360*t+"deg)",a.clip="rect("+(45-n)+"px,45px,45px,-5px)",a.transform="translate3d(-50%, "+n+"px, 0)"}(t)})),_=lu((e=>{jv(e,c,u)&&null!==d&&("pulling"===d?(v(),d=qv,m(),function(e){if(!s)return;if(a.transform){a.transition="-webkit-transform 0.3s",a.transform="translate3d(-50%, 0, 0)";const t=function(){n&&clearTimeout(n),s.removeEventListener("webkitTransitionEnd",t),a.transition="",e()};s.addEventListener("webkitTransitionEnd",t);const n=setTimeout(t,350)}else e()}((()=>{v(),d=h=p=null}))):"reached"===d&&(v(),d=zv,m(),w()))}));function w(){s&&(a.transition="-webkit-transform 0.2s",a.transform="translate3d(-50%, "+r+"px, 0)",Sc(t,"onPullDownRefresh"))}return{onTouchstartPassive:y,onTouchmove:b,onTouchend:_,onTouchcancel:_}}const Wv=ru({name:"PageBody",setup(e,t){const n=Hp(),o=tn(null),r=n.enablePullDownRefresh?Vv(o):null;return()=>{const e=function(e,t){if(!t.enablePullDownRefresh)return null;return Kr(Nv,{ref:e},null,512)}(o,n);return Kr(Pr,null,[e,Kr("uni-page-wrapper",r,[Kr("uni-page-body",null,[Yo(t.slots,"default")])],16)])}}});const Uv=ru({name:"Page",setup(e,t){const n=Vp(Up());return n.navigationBar,cv(n),()=>Kr("uni-page",{"data-page":n.route},[Yv(t)])}});function Yv(e){return Fr(),zr(Wv,{key:0},{default:Rn((()=>[Yo(e.slots,"page")])),_:3})}const Xv={loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4,suspensible:!0};window.uni={},window.wx={},window.rpx2px=kf;const Kv=Object.assign({}),Gv=Object.assign;window.__uniConfig=Gv({tabBar:{position:"bottom",color:"#000000",selectedColor:"#000000",borderStyle:"white",blurEffect:"none",fontSize:"10px",iconWidth:"24px",spacing:"3px",height:"50px",backgroundColor:"#ffffff",list:[{pagePath:"pages/home/<USER>",iconPath:"/static/images/tabbar/home.png",selectedIconPath:"/static/images/tabbar/home_.png",text:"首页"},{pagePath:"pages/work/index",iconPath:"/static/images/tabbar/work.png",selectedIconPath:"/static/images/tabbar/work_.png",text:"工作台"},{pagePath:"pages/msg/index",iconPath:"/static/images/tabbar/msg.png",selectedIconPath:"/static/images/tabbar/msg_.png",text:"消息"},{pagePath:"pages/mine/index",iconPath:"/static/images/tabbar/mine.png",selectedIconPath:"/static/images/tabbar/mine_.png",text:"我的"}],selectedIndex:0,shown:!0},easycom:{autoscan:!0,custom:{"tui-(.*)":"@/components/thorui/thorui/tui-$1/tui-$1.vue","snowy-(.*)":"@/components/snowy/snowy-$1/snowy-$1.vue"}},globalStyle:{backgroundColor:"#F8F8F8",navigationBar:{backgroundColor:"#F8F8F8",titleText:"uni-app",type:"default",titleColor:"#000000"},isNVue:!1},uniIdRouter:{},compilerVersion:"3.99"},{appId:"__UNI__EF8711B",appName:"Snowy-Mobile",appVersion:"1.0.0",appVersionCode:"100",async:Xv,debug:!1,networkTimeout:{request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},sdkConfigs:{},qqMapKey:void 0,bMapKey:void 0,googleMapKey:void 0,aMapKey:void 0,aMapSecurityJsCode:void 0,aMapServiceHost:void 0,nvue:{"flex-direction":"column"},locale:"zh-Hans",fallbackLocale:"",locales:Object.keys(Kv).reduce(((e,t)=>{const n=t.replace(/\.\/locale\/(uni-app.)?(.*).json/,"$2");return Gv(e[n]||(e[n]={}),Kv[t].default),e}),{}),router:{mode:"history",base:"/",assets:"assets",routerBase:"/"},darkmode:!1,themeConfig:{}}),window.__uniLayout=window.__uniLayout||{};const Zv={delay:Xv.delay,timeout:Xv.timeout,suspensible:Xv.suspensible};Xv.loading&&(Zv.loadingComponent={name:"SystemAsyncLoading",render:()=>Kr(qo(Xv.loading))}),Xv.error&&(Zv.errorComponent={name:"SystemAsyncError",render:()=>Kr(qo(Xv.error))});const Jv=()=>t((()=>import("./pages-login.8196a1c6.js")),["assets/pages-login.8196a1c6.js","assets/uv-icon.019f93fe.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/uv-icon-b31de1be.css","assets/login-6a76b79c.css"]).then((e=>Ig(e.default||e))),Qv=uo(Gv({loader:Jv},Zv)),ey=()=>t((()=>import("./pages-home-index.0538c8f1.js")),["assets/pages-home-index.0538c8f1.js","assets/uv-swiper.874acae4.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/uv-swiper-df313444.css","assets/uv-calendars.cf386575.js","assets/uv-popup.f10e3fa8.js","assets/uv-transition.8392ab8b.js","assets/uv-icon.019f93fe.js","assets/uv-icon-b31de1be.css","assets/uv-popup-b9f21e35.css","assets/uv-calendars-6207389e.css","assets/uv-row.88bea320.js","assets/uv-row-04928528.css","assets/uv-form.bce81edd.js","assets/uv-form-403d331e.css","assets/tui-button.600cfc04.js","assets/tui-button-fd3f0dd8.css","assets/index-9fba0b16.css"]).then((e=>Ig(e.default||e))),ty=uo(Gv({loader:ey},Zv)),ny=()=>t((()=>import("./pages-work-index.4f9cc50b.js")),["assets/pages-work-index.4f9cc50b.js","assets/uv-swiper.874acae4.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/uv-swiper-df313444.css","assets/uv-icon.019f93fe.js","assets/uv-icon-b31de1be.css","assets/uv-row.88bea320.js","assets/uv-row-04928528.css","assets/snowy-icon.e7a99593.js","assets/snowy-icon-676e0cc1.css","assets/uv-grid.51cab1b3.js","assets/uv-grid-590f9c77.css","assets/index-1ec3009b.css"]).then((e=>Ig(e.default||e))),oy=uo(Gv({loader:ny},Zv)),ry=()=>t((()=>import("./pages-msg-index.c720ce4d.js")),["assets/pages-msg-index.c720ce4d.js","assets/tui-tabs.58282db7.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/tui-tabs-7f6e2cc5.css","assets/uv-row.88bea320.js","assets/uv-row-04928528.css","assets/snowy-empty.8508b7b3.js","assets/snowy-empty-feed3136.css","assets/index-ee1972da.css"]).then((e=>Ig(e.default||e))),iy=uo(Gv({loader:ry},Zv)),sy=()=>t((()=>import("./pages-msg-detail.cd38df70.js")),["assets/pages-msg-detail.cd38df70.js","assets/uv-row.88bea320.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/uv-row-04928528.css","assets/uv-icon.019f93fe.js","assets/uv-icon-b31de1be.css","assets/uv-transition.8392ab8b.js","assets/detail-fe26b1e0.css"]).then((e=>Ig(e.default||e))),ay=uo(Gv({loader:sy},Zv)),ly=()=>t((()=>import("./pages-mine-index.34e7d91b.js")),["assets/pages-mine-index.34e7d91b.js","assets/uv-row.88bea320.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/uv-row-04928528.css","assets/uv-icon.019f93fe.js","assets/uv-icon-b31de1be.css","assets/snowy-icon.e7a99593.js","assets/snowy-icon-676e0cc1.css","assets/uv-grid.51cab1b3.js","assets/uv-grid-590f9c77.css","assets/index-37936dff.css"]).then((e=>Ig(e.default||e))),cy=uo(Gv({loader:ly},Zv)),uy=()=>t((()=>import("./pages-config-index.66ed03e6.js")),["assets/pages-config-index.66ed03e6.js","assets/snowy-search.21626719.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/snowy-search-8d1bcfae.css","assets/uv-icon.019f93fe.js","assets/uv-icon-b31de1be.css","assets/uv-row.88bea320.js","assets/uv-row-04928528.css","assets/tui-button.600cfc04.js","assets/tui-button-fd3f0dd8.css","assets/snowy-float-btn.c0b619f0.js","assets/snowy-float-btn-f4d46117.css","assets/tui-list-view.2e7f2ee0.js","assets/tui-list-view-abbfceb8.css","assets/uv-popup.f10e3fa8.js","assets/uv-transition.8392ab8b.js","assets/uv-popup-b9f21e35.css","assets/index-cc3fd289.css"]).then((e=>Ig(e.default||e))),fy=uo(Gv({loader:uy},Zv)),dy=()=>t((()=>import("./pages-config-form.d731e86d.js")),["assets/pages-config-form.d731e86d.js","assets/uv-form.bce81edd.js","assets/uv-icon.019f93fe.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/uv-icon-b31de1be.css","assets/uv-transition.8392ab8b.js","assets/uv-form-403d331e.css","assets/tui-button.600cfc04.js","assets/tui-button-fd3f0dd8.css","assets/form-6bc8e407.css"]).then((e=>Ig(e.default||e))),hy=uo(Gv({loader:dy},Zv)),py=()=>t((()=>import("./pages-common-webview-index.3685decf.js")),[]).then((e=>Ig(e.default||e))),gy=uo(Gv({loader:py},Zv)),my=()=>t((()=>import("./pages-biz-org-index.98690b85.js")),["assets/pages-biz-org-index.98690b85.js","assets/tui-list-view.2e7f2ee0.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/tui-list-view-abbfceb8.css","assets/snowy-empty.8508b7b3.js","assets/snowy-empty-feed3136.css","assets/snowy-float-btn.c0b619f0.js","assets/uv-icon.019f93fe.js","assets/uv-icon-b31de1be.css","assets/snowy-float-btn-f4d46117.css","assets/department.42e35462.js","assets/bizOrgApi.fca39c8b.js","assets/index-8345bcd2.css"]).then((e=>Ig(e.default||e))),vy=uo(Gv({loader:my},Zv)),yy=()=>t((()=>import("./pages-biz-org-form.dc856031.js")),["assets/pages-biz-org-form.dc856031.js","assets/snowy-org-picker.bb6789bc.js","assets/uv-icon.019f93fe.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/uv-icon-b31de1be.css","assets/uv-row.88bea320.js","assets/uv-row-04928528.css","assets/uv-popup.f10e3fa8.js","assets/uv-transition.8392ab8b.js","assets/uv-popup-b9f21e35.css","assets/snowy-org-picker-96baeebf.css","assets/uv-form.bce81edd.js","assets/uv-form-403d331e.css","assets/uv-radio-group.57606234.js","assets/uv-radio-group-08ecc7ff.css","assets/uv-number-box.4f68c385.js","assets/uv-number-box-143012ff.css","assets/snowy-user-picker.d90df5d1.js","assets/snowy-search.21626719.js","assets/snowy-search-8d1bcfae.css","assets/snowy-empty.8508b7b3.js","assets/snowy-empty-feed3136.css","assets/snowy-user-picker-2e37536f.css","assets/tui-button.600cfc04.js","assets/tui-button-fd3f0dd8.css","assets/bizOrgApi.fca39c8b.js","assets/form-585c57e9.css"]).then((e=>Ig(e.default||e))),by=uo(Gv({loader:yy},Zv)),_y=()=>t((()=>import("./pages-biz-position-index.366178e5.js")),["assets/pages-biz-position-index.366178e5.js","assets/tui-list-view.2e7f2ee0.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/tui-list-view-abbfceb8.css","assets/snowy-icon.e7a99593.js","assets/uv-icon.019f93fe.js","assets/uv-icon-b31de1be.css","assets/snowy-icon-676e0cc1.css","assets/snowy-empty.8508b7b3.js","assets/snowy-empty-feed3136.css","assets/snowy-float-btn.c0b619f0.js","assets/snowy-float-btn-f4d46117.css","assets/department.42e35462.js","assets/bizOrgApi.fca39c8b.js","assets/bizPositionApi.11993909.js","assets/uv-popup.f10e3fa8.js","assets/uv-transition.8392ab8b.js","assets/uv-popup-b9f21e35.css","assets/index-2691d35a.css"]).then((e=>Ig(e.default||e))),wy=uo(Gv({loader:_y},Zv)),xy=()=>t((()=>import("./pages-biz-position-form.312e2357.js")),["assets/pages-biz-position-form.312e2357.js","assets/snowy-org-picker.bb6789bc.js","assets/uv-icon.019f93fe.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/uv-icon-b31de1be.css","assets/uv-row.88bea320.js","assets/uv-row-04928528.css","assets/uv-popup.f10e3fa8.js","assets/uv-transition.8392ab8b.js","assets/uv-popup-b9f21e35.css","assets/snowy-org-picker-96baeebf.css","assets/uv-form.bce81edd.js","assets/uv-form-403d331e.css","assets/snowy-sel-picker.9b6e310d.js","assets/snowy-search.21626719.js","assets/snowy-search-8d1bcfae.css","assets/snowy-empty.8508b7b3.js","assets/snowy-empty-feed3136.css","assets/snowy-sel-picker-dc1237a5.css","assets/uv-number-box.4f68c385.js","assets/uv-number-box-143012ff.css","assets/tui-button.600cfc04.js","assets/tui-button-fd3f0dd8.css","assets/bizPositionApi.11993909.js","assets/form-40fc8181.css"]).then((e=>Ig(e.default||e))),Ty=uo(Gv({loader:xy},Zv)),Sy=()=>t((()=>import("./pages-biz-user-index.46c160ca.js")),["assets/pages-biz-user-index.46c160ca.js","assets/snowy-search.21626719.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/snowy-search-8d1bcfae.css","assets/tui-list-view.2e7f2ee0.js","assets/tui-list-view-abbfceb8.css","assets/snowy-empty.8508b7b3.js","assets/snowy-empty-feed3136.css","assets/snowy-float-btn.c0b619f0.js","assets/uv-icon.019f93fe.js","assets/uv-icon-b31de1be.css","assets/snowy-float-btn-f4d46117.css","assets/department.42e35462.js","assets/uv-popup.f10e3fa8.js","assets/uv-transition.8392ab8b.js","assets/uv-popup-b9f21e35.css","assets/bizUserApi.2dee0f02.js","assets/bizOrgApi.fca39c8b.js","assets/index-6eb30d0a.css"]).then((e=>Ig(e.default||e))),Ey=uo(Gv({loader:Sy},Zv)),ky=()=>t((()=>import("./pages-biz-user-form.75d6bcd4.js")),["assets/pages-biz-user-form.75d6bcd4.js","assets/tui-tabs.58282db7.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/tui-tabs-7f6e2cc5.css","assets/uv-form.bce81edd.js","assets/uv-icon.019f93fe.js","assets/uv-icon-b31de1be.css","assets/uv-transition.8392ab8b.js","assets/uv-form-403d331e.css","assets/uv-radio-group.57606234.js","assets/uv-radio-group-08ecc7ff.css","assets/snowy-calendar.226962e5.js","assets/uv-calendars.cf386575.js","assets/uv-popup.f10e3fa8.js","assets/uv-popup-b9f21e35.css","assets/uv-calendars-6207389e.css","assets/snowy-calendar-05ef8dcd.css","assets/snowy-org-picker.bb6789bc.js","assets/uv-row.88bea320.js","assets/uv-row-04928528.css","assets/snowy-org-picker-96baeebf.css","assets/snowy-sel-picker.9b6e310d.js","assets/snowy-search.21626719.js","assets/snowy-search-8d1bcfae.css","assets/snowy-empty.8508b7b3.js","assets/snowy-empty-feed3136.css","assets/snowy-sel-picker-dc1237a5.css","assets/snowy-user-picker.d90df5d1.js","assets/snowy-user-picker-2e37536f.css","assets/tui-button.600cfc04.js","assets/tui-button-fd3f0dd8.css","assets/bizUserApi.2dee0f02.js","assets/form-829024d7.css"]).then((e=>Ig(e.default||e))),Cy=uo(Gv({loader:ky},Zv)),Oy=()=>t((()=>import("./pages-mine-info-index.adacccfd.js")),["assets/pages-mine-info-index.adacccfd.js","assets/uv-icon.019f93fe.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/uv-icon-b31de1be.css","assets/uv-row.88bea320.js","assets/uv-row-04928528.css","assets/index-675f6e2e.css"]).then((e=>Ig(e.default||e))),My=uo(Gv({loader:Oy},Zv)),Ay=()=>t((()=>import("./pages-mine-info-edit.f6e8171f.js")),["assets/pages-mine-info-edit.f6e8171f.js","assets/uv-form.bce81edd.js","assets/uv-icon.019f93fe.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/uv-icon-b31de1be.css","assets/uv-transition.8392ab8b.js","assets/uv-form-403d331e.css","assets/uv-radio-group.57606234.js","assets/uv-radio-group-08ecc7ff.css","assets/snowy-calendar.226962e5.js","assets/uv-calendars.cf386575.js","assets/uv-popup.f10e3fa8.js","assets/uv-popup-b9f21e35.css","assets/uv-calendars-6207389e.css","assets/snowy-calendar-05ef8dcd.css","assets/tui-button.600cfc04.js","assets/tui-button-fd3f0dd8.css","assets/edit-26235e71.css"]).then((e=>Ig(e.default||e))),Py=uo(Gv({loader:Ay},Zv)),Iy=()=>t((()=>import("./pages-mine-home-config-index.a5c7e25a.js")),["assets/pages-mine-home-config-index.a5c7e25a.js","assets/uv-icon.019f93fe.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/uv-icon-b31de1be.css","assets/uv-row.88bea320.js","assets/uv-row-04928528.css","assets/tui-list-view.2e7f2ee0.js","assets/tui-list-view-abbfceb8.css","assets/uv-popup.f10e3fa8.js","assets/uv-transition.8392ab8b.js","assets/uv-popup-b9f21e35.css","assets/index-0fee96a1.css"]).then((e=>Ig(e.default||e))),$y=uo(Gv({loader:Iy},Zv)),Dy=()=>t((()=>import("./pages-mine-pwd-index.fbb86714.js")),["assets/pages-mine-pwd-index.fbb86714.js","assets/uv-form.bce81edd.js","assets/uv-icon.019f93fe.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/uv-icon-b31de1be.css","assets/uv-transition.8392ab8b.js","assets/uv-form-403d331e.css","assets/tui-button.600cfc04.js","assets/tui-button-fd3f0dd8.css","assets/index-78e31313.css"]).then((e=>Ig(e.default||e))),Ly=uo(Gv({loader:Dy},Zv));function By(e,t){return Fr(),zr(Uv,null,{page:Rn((()=>[Kr(e,Gv({},t,{ref:"page"}),null,512)])),_:1})}function Fy(e,t){return I(e)?t:e}window.__uniRoutes=[{path:"/",alias:"/pages/login",component:{setup(){const e=Og(),t=e&&e.$route&&e.$route.query||{};return()=>By(Qv,t)}},loader:Jv,meta:{isQuit:!0,isEntry:!0,enablePullDownRefresh:!1,navigationBar:{titleText:"登录",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/home/<USER>",component:{setup(){const e=Og(),t=e&&e.$route&&e.$route.query||{};return()=>By(ty,t)}},loader:ey,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:0,navigationBar:{titleText:"小诺移动端框架",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/work/index",component:{setup(){const e=Og(),t=e&&e.$route&&e.$route.query||{};return()=>By(oy,t)}},loader:ny,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:1,navigationBar:{titleText:"工作台",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/msg/index",component:{setup(){const e=Og(),t=e&&e.$route&&e.$route.query||{};return()=>By(iy,t)}},loader:ry,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:2,enablePullDownRefresh:!0,onReachBottomDistance:50,navigationBar:{titleText:"消息",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/msg/detail",component:{setup(){const e=Og(),t=e&&e.$route&&e.$route.query||{};return()=>By(ay,t)}},loader:sy,meta:{navigationBar:{titleText:"消息详情",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/mine/index",component:{setup(){const e=Og(),t=e&&e.$route&&e.$route.query||{};return()=>By(cy,t)}},loader:ly,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:3,navigationBar:{titleText:"我的",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/config/index",component:{setup(){const e=Og(),t=e&&e.$route&&e.$route.query||{};return()=>By(fy,t)}},loader:uy,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"环境设置",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/config/form",component:{setup(){const e=Og(),t=e&&e.$route&&e.$route.query||{};return()=>By(hy,t)}},loader:dy,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"环境管理",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/common/webview/index",component:{setup(){const e=Og(),t=e&&e.$route&&e.$route.query||{};return()=>By(gy,t)}},loader:py,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"浏览网页",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/biz/org/index",component:{setup(){const e=Og(),t=e&&e.$route&&e.$route.query||{};return()=>By(vy,t)}},loader:my,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"机构管理",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/biz/org/form",component:{setup(){const e=Og(),t=e&&e.$route&&e.$route.query||{};return()=>By(by,t)}},loader:yy,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"机构管理",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/biz/position/index",component:{setup(){const e=Og(),t=e&&e.$route&&e.$route.query||{};return()=>By(wy,t)}},loader:_y,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"职位管理",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/biz/position/form",component:{setup(){const e=Og(),t=e&&e.$route&&e.$route.query||{};return()=>By(Ty,t)}},loader:xy,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"职位管理",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/biz/user/index",component:{setup(){const e=Og(),t=e&&e.$route&&e.$route.query||{};return()=>By(Ey,t)}},loader:Sy,meta:{enablePullDownRefresh:!0,onReachBottomDistance:50,navigationBar:{titleText:"用户管理",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/biz/user/form",component:{setup(){const e=Og(),t=e&&e.$route&&e.$route.query||{};return()=>By(Cy,t)}},loader:ky,meta:{enablePullDownRefresh:!1,onReachBottomDistance:50,navigationBar:{titleText:"用户管理",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/mine/info/index",component:{setup(){const e=Og(),t=e&&e.$route&&e.$route.query||{};return()=>By(My,t)}},loader:Oy,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"个人信息",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/mine/info/edit",component:{setup(){const e=Og(),t=e&&e.$route&&e.$route.query||{};return()=>By(Py,t)}},loader:Ay,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"编辑资料",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/mine/home-config/index",component:{setup(){const e=Og(),t=e&&e.$route&&e.$route.query||{};return()=>By($y,t)}},loader:Iy,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"首页设置",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/mine/pwd/index",component:{setup(){const e=Og(),t=e&&e.$route&&e.$route.query||{};return()=>By(Ly,t)}},loader:Dy,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"修改密码",style:"custom",type:"default"},isNVue:!1}}].map((e=>(e.meta.route=(e.alias||e.path).slice(1),e)));const Ry=e=>(t,n=li())=>{!di&&Co(e,t,n)},Ny=Ry("onShow"),jy=Ry("onLoad"),qy=Ry("onReachBottom"),zy=Ry("onPullDownRefresh");function Hy(){return"undefined"!=typeof navigator&&"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}}const Vy="function"==typeof Proxy;class Wy{constructor(e,t){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=e,this.hook=t;const n={};if(e.settings)for(const s in e.settings){const t=e.settings[s];n[s]=t.defaultValue}const o=`__vue-devtools-plugin-settings__${e.id}`;let r={...n};try{const e=localStorage.getItem(o),t=JSON.parse(e);Object.assign(r,t)}catch(i){}this.fallbacks={getSettings:()=>r,setSettings(e){try{localStorage.setItem(o,JSON.stringify(e))}catch(i){}r=e}},t.on("plugin:settings:set",((e,t)=>{e===this.plugin.id&&this.fallbacks.setSettings(t)})),this.proxiedOn=new Proxy({},{get:(e,t)=>this.target?this.target.on[t]:(...e)=>{this.onQueue.push({method:t,args:e})}}),this.proxiedTarget=new Proxy({},{get:(e,t)=>this.target?this.target[t]:"on"===t?this.proxiedOn:Object.keys(this.fallbacks).includes(t)?(...e)=>(this.targetQueue.push({method:t,args:e,resolve:()=>{}}),this.fallbacks[t](...e)):(...e)=>new Promise((n=>{this.targetQueue.push({method:t,args:e,resolve:n})}))})}async setRealTarget(e){this.target=e;for(const t of this.onQueue)this.target.on[t.method](...t.args);for(const t of this.targetQueue)t.resolve(await this.target[t.method](...t.args))}}function Uy(e,t){const n=Hy(),o=Hy().__VUE_DEVTOOLS_GLOBAL_HOOK__,r=Vy&&e.enableEarlyProxy;if(!o||!n.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__&&r){const i=r?new Wy(e,o):null;(n.__VUE_DEVTOOLS_PLUGINS__=n.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:e,setupFn:t,proxy:i}),i&&t(i.proxiedTarget)}else o.emit("devtools-plugin:setup",e,t)}
/*!
 * vuex v4.1.0
 * (c) 2022 Evan You
 * @license MIT
 */function Yy(e,t){Object.keys(e).forEach((function(n){return t(e[n],n)}))}function Xy(e,t,n){return t.indexOf(e)<0&&(n&&n.prepend?t.unshift(e):t.push(e)),function(){var n=t.indexOf(e);n>-1&&t.splice(n,1)}}function Ky(e,t){e._actions=Object.create(null),e._mutations=Object.create(null),e._wrappedGetters=Object.create(null),e._modulesNamespaceMap=Object.create(null);var n=e.state;Zy(e,n,[],e._modules.root,!0),Gy(e,n,t)}function Gy(e,t,n){var o=e._state,r=e._scope;e.getters={},e._makeLocalGettersCache=Object.create(null);var i=e._wrappedGetters,s={},a={},l=Le(!0);l.run((function(){Yy(i,(function(t,n){s[n]=function(e,t){return function(){return e(t)}}(t,e),a[n]=vi((function(){return s[n]()})),Object.defineProperty(e.getters,n,{get:function(){return a[n].value},enumerable:!0})}))})),e._state=qt({data:t}),e._scope=l,e.strict&&function(e){Xn((function(){return e._state.data}),(function(){}),{deep:!0,flush:"sync"})}(e),o&&n&&e._withCommit((function(){o.data=null})),r&&r.stop()}function Zy(e,t,n,o,r){var i=!n.length,s=e._modules.getNamespace(n);if(o.namespaced&&(e._modulesNamespaceMap[s],e._modulesNamespaceMap[s]=o),!i&&!r){var a=Qy(t,n.slice(0,-1)),l=n[n.length-1];e._withCommit((function(){a[l]=o.state}))}var c=o.context=function(e,t,n){var o=""===t,r={dispatch:o?e.dispatch:function(n,o,r){var i=eb(n,o,r),s=i.payload,a=i.options,l=i.type;return a&&a.root||(l=t+l),e.dispatch(l,s)},commit:o?e.commit:function(n,o,r){var i=eb(n,o,r),s=i.payload,a=i.options,l=i.type;a&&a.root||(l=t+l),e.commit(l,s,a)}};return Object.defineProperties(r,{getters:{get:o?function(){return e.getters}:function(){return Jy(e,t)}},state:{get:function(){return Qy(e.state,n)}}}),r}(e,s,n);o.forEachMutation((function(t,n){!function(e,t,n,o){(e._mutations[t]||(e._mutations[t]=[])).push((function(t){n.call(e,o.state,t)}))}(e,s+n,t,c)})),o.forEachAction((function(t,n){var o=t.root?n:s+n,r=t.handler||t;!function(e,t,n,o){(e._actions[t]||(e._actions[t]=[])).push((function(t){var r,i=n.call(e,{dispatch:o.dispatch,commit:o.commit,getters:o.getters,state:o.state,rootGetters:e.getters,rootState:e.state},t);return(r=i)&&"function"==typeof r.then||(i=Promise.resolve(i)),e._devtoolHook?i.catch((function(t){throw e._devtoolHook.emit("vuex:error",t),t})):i}))}(e,o,r,c)})),o.forEachGetter((function(t,n){!function(e,t,n,o){if(e._wrappedGetters[t])return;e._wrappedGetters[t]=function(e){return n(o.state,o.getters,e.state,e.getters)}}(e,s+n,t,c)})),o.forEachChild((function(o,i){Zy(e,t,n.concat(i),o,r)}))}function Jy(e,t){if(!e._makeLocalGettersCache[t]){var n={},o=t.length;Object.keys(e.getters).forEach((function(r){if(r.slice(0,o)===t){var i=r.slice(o);Object.defineProperty(n,i,{get:function(){return e.getters[r]},enumerable:!0})}})),e._makeLocalGettersCache[t]=n}return e._makeLocalGettersCache[t]}function Qy(e,t){return t.reduce((function(e,t){return e[t]}),e)}function eb(e,t,n){var o;return null!==(o=e)&&"object"==typeof o&&e.type&&(n=t,t=e,e=e.type),{type:e,payload:t,options:n}}var tb=0;function nb(e,t){Uy({id:"org.vuejs.vuex",app:e,label:"Vuex",homepage:"https://next.vuex.vuejs.org/",logo:"https://vuejs.org/images/icons/favicon-96x96.png",packageName:"vuex",componentStateTypes:["vuex bindings"]},(function(n){n.addTimelineLayer({id:"vuex:mutations",label:"Vuex Mutations",color:ob}),n.addTimelineLayer({id:"vuex:actions",label:"Vuex Actions",color:ob}),n.addInspector({id:"vuex",label:"Vuex",icon:"storage",treeFilterPlaceholder:"Filter stores..."}),n.on.getInspectorTree((function(n){if(n.app===e&&"vuex"===n.inspectorId)if(n.filter){var o=[];ab(o,t._modules.root,n.filter,""),n.rootNodes=o}else n.rootNodes=[sb(t._modules.root,"")]})),n.on.getInspectorState((function(n){if(n.app===e&&"vuex"===n.inspectorId){var o=n.nodeId;Jy(t,o),n.state=function(e,t,n){t="root"===n?t:t[n];var o=Object.keys(t),r={state:Object.keys(e.state).map((function(t){return{key:t,editable:!0,value:e.state[t]}}))};if(o.length){var i=function(e){var t={};return Object.keys(e).forEach((function(n){var o=n.split("/");if(o.length>1){var r=t,i=o.pop();o.forEach((function(e){r[e]||(r[e]={_custom:{value:{},display:e,tooltip:"Module",abstract:!0}}),r=r[e]._custom.value})),r[i]=lb((function(){return e[n]}))}else t[n]=lb((function(){return e[n]}))})),t}(t);r.getters=Object.keys(i).map((function(e){return{key:e.endsWith("/")?ib(e):e,editable:!1,value:lb((function(){return i[e]}))}}))}return r}((r=t._modules,(s=(i=o).split("/").filter((function(e){return e}))).reduce((function(e,t,n){var o=e[t];if(!o)throw new Error('Missing module "'+t+'" for path "'+i+'".');return n===s.length-1?o:o._children}),"root"===i?r:r.root._children)),"root"===o?t.getters:t._makeLocalGettersCache,o)}var r,i,s})),n.on.editInspectorState((function(n){if(n.app===e&&"vuex"===n.inspectorId){var o=n.nodeId,r=n.path;"root"!==o&&(r=o.split("/").filter(Boolean).concat(r)),t._withCommit((function(){n.set(t._state.data,r,n.state.value)}))}})),t.subscribe((function(e,t){var o={};e.payload&&(o.payload=e.payload),o.state=t,n.notifyComponentUpdate(),n.sendInspectorTree("vuex"),n.sendInspectorState("vuex"),n.addTimelineEvent({layerId:"vuex:mutations",event:{time:Date.now(),title:e.type,data:o}})})),t.subscribeAction({before:function(e,t){var o={};e.payload&&(o.payload=e.payload),e._id=tb++,e._time=Date.now(),o.state=t,n.addTimelineEvent({layerId:"vuex:actions",event:{time:e._time,title:e.type,groupId:e._id,subtitle:"start",data:o}})},after:function(e,t){var o={},r=Date.now()-e._time;o.duration={_custom:{type:"duration",display:r+"ms",tooltip:"Action duration",value:r}},e.payload&&(o.payload=e.payload),o.state=t,n.addTimelineEvent({layerId:"vuex:actions",event:{time:Date.now(),title:e.type,groupId:e._id,subtitle:"end",data:o}})}})}))}var ob=8702998,rb={label:"namespaced",textColor:16777215,backgroundColor:6710886};function ib(e){return e&&"root"!==e?e.split("/").slice(-2,-1)[0]:"Root"}function sb(e,t){return{id:t||"root",label:ib(t),tags:e.namespaced?[rb]:[],children:Object.keys(e._children).map((function(n){return sb(e._children[n],t+n+"/")}))}}function ab(e,t,n,o){o.includes(n)&&e.push({id:o||"root",label:o.endsWith("/")?o.slice(0,o.length-1):o||"Root",tags:t.namespaced?[rb]:[]}),Object.keys(t._children).forEach((function(r){ab(e,t._children[r],n,o+r+"/")}))}function lb(e){try{return e()}catch(t){return t}}var cb=function(e,t){this.runtime=t,this._children=Object.create(null),this._rawModule=e;var n=e.state;this.state=("function"==typeof n?n():n)||{}},ub={namespaced:{configurable:!0}};ub.namespaced.get=function(){return!!this._rawModule.namespaced},cb.prototype.addChild=function(e,t){this._children[e]=t},cb.prototype.removeChild=function(e){delete this._children[e]},cb.prototype.getChild=function(e){return this._children[e]},cb.prototype.hasChild=function(e){return e in this._children},cb.prototype.update=function(e){this._rawModule.namespaced=e.namespaced,e.actions&&(this._rawModule.actions=e.actions),e.mutations&&(this._rawModule.mutations=e.mutations),e.getters&&(this._rawModule.getters=e.getters)},cb.prototype.forEachChild=function(e){Yy(this._children,e)},cb.prototype.forEachGetter=function(e){this._rawModule.getters&&Yy(this._rawModule.getters,e)},cb.prototype.forEachAction=function(e){this._rawModule.actions&&Yy(this._rawModule.actions,e)},cb.prototype.forEachMutation=function(e){this._rawModule.mutations&&Yy(this._rawModule.mutations,e)},Object.defineProperties(cb.prototype,ub);var fb=function(e){this.register([],e,!1)};function db(e,t,n){if(t.update(n),n.modules)for(var o in n.modules){if(!t.getChild(o))return;db(e.concat(o),t.getChild(o),n.modules[o])}}fb.prototype.get=function(e){return e.reduce((function(e,t){return e.getChild(t)}),this.root)},fb.prototype.getNamespace=function(e){var t=this.root;return e.reduce((function(e,n){return e+((t=t.getChild(n)).namespaced?n+"/":"")}),"")},fb.prototype.update=function(e){db([],this.root,e)},fb.prototype.register=function(e,t,n){var o=this;void 0===n&&(n=!0);var r=new cb(t,n);0===e.length?this.root=r:this.get(e.slice(0,-1)).addChild(e[e.length-1],r);t.modules&&Yy(t.modules,(function(t,r){o.register(e.concat(r),t,n)}))},fb.prototype.unregister=function(e){var t=this.get(e.slice(0,-1)),n=e[e.length-1],o=t.getChild(n);o&&o.runtime&&t.removeChild(n)},fb.prototype.isRegistered=function(e){var t=this.get(e.slice(0,-1)),n=e[e.length-1];return!!t&&t.hasChild(n)};var hb=function(e){var t=this;void 0===e&&(e={});var n=e.plugins;void 0===n&&(n=[]);var o=e.strict;void 0===o&&(o=!1);var r=e.devtools;this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new fb(e),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._makeLocalGettersCache=Object.create(null),this._scope=null,this._devtools=r;var i=this,s=this.dispatch,a=this.commit;this.dispatch=function(e,t){return s.call(i,e,t)},this.commit=function(e,t,n){return a.call(i,e,t,n)},this.strict=o;var l=this._modules.root.state;Zy(this,l,[],this._modules.root),Gy(this,l),n.forEach((function(e){return e(t)}))},pb={state:{configurable:!0}};hb.prototype.install=function(e,t){e.provide(t||"store",this),e.config.globalProperties.$store=this,void 0!==this._devtools&&this._devtools&&nb(e,this)},pb.state.get=function(){return this._state.data},pb.state.set=function(e){},hb.prototype.commit=function(e,t,n){var o=this,r=eb(e,t,n),i=r.type,s=r.payload,a={type:i,payload:s},l=this._mutations[i];l&&(this._withCommit((function(){l.forEach((function(e){e(s)}))})),this._subscribers.slice().forEach((function(e){return e(a,o.state)})))},hb.prototype.dispatch=function(e,t){var n=this,o=eb(e,t),r=o.type,i=o.payload,s={type:r,payload:i},a=this._actions[r];if(a){try{this._actionSubscribers.slice().filter((function(e){return e.before})).forEach((function(e){return e.before(s,n.state)}))}catch(c){}var l=a.length>1?Promise.all(a.map((function(e){return e(i)}))):a[0](i);return new Promise((function(e,t){l.then((function(t){try{n._actionSubscribers.filter((function(e){return e.after})).forEach((function(e){return e.after(s,n.state)}))}catch(c){}e(t)}),(function(e){try{n._actionSubscribers.filter((function(e){return e.error})).forEach((function(t){return t.error(s,n.state,e)}))}catch(c){}t(e)}))}))}},hb.prototype.subscribe=function(e,t){return Xy(e,this._subscribers,t)},hb.prototype.subscribeAction=function(e,t){return Xy("function"==typeof e?{before:e}:e,this._actionSubscribers,t)},hb.prototype.watch=function(e,t,n){var o=this;return Xn((function(){return e(o.state,o.getters)}),t,Object.assign({},n))},hb.prototype.replaceState=function(e){var t=this;this._withCommit((function(){t._state.data=e}))},hb.prototype.registerModule=function(e,t,n){void 0===n&&(n={}),"string"==typeof e&&(e=[e]),this._modules.register(e,t),Zy(this,this.state,e,this._modules.get(e),n.preserveState),Gy(this,this.state)},hb.prototype.unregisterModule=function(e){var t=this;"string"==typeof e&&(e=[e]),this._modules.unregister(e),this._withCommit((function(){delete Qy(t.state,e.slice(0,-1))[e[e.length-1]]})),Ky(this)},hb.prototype.hasModule=function(e){return"string"==typeof e&&(e=[e]),this._modules.isRegistered(e)},hb.prototype.hotUpdate=function(e){this._modules.update(e),Ky(this,!0)},hb.prototype._withCommit=function(e){var t=this._committing;this._committing=!0,e(),this._committing=t},Object.defineProperties(hb.prototype,pb);const gb={SERVER_TYPE:"SNOWY",TIMEOUT:1e4,TOKEN_NAME:"token",TOKEN_PREFIX:"",SYS_BASE_CONFIG:{SNOWY_SYS_LOGO:"/static/logo.png",SNOWY_SYS_BACK_IMAGE:"",SNOWY_SYS_NAME:"Snowy",SNOWY_SYS_VERSION:"2.0",SNOWY_SYS_COPYRIGHT:"Snowy ©2022 Created by xiaonuo.vip",SNOWY_SYS_COPYRIGHT_URL:"https://www.xiaonuo.vip",SNOWY_SYS_DEFAULT_FILE_ENGINE:"LOCAL",SNOWY_SYS_DEFAULT_CAPTCHA_OPEN:"false",SNOWY_SYS_DEFAULT_PASSWORD:"123456"},HOME_CONFIGS:[{name:"轮播",code:"swiper",isShow:!0},{name:"图表",code:"chart",isShow:!0},{name:"日程",code:"schedule",isShow:!0}],NO_TOKEN_BACK_URL:"/pages/login",NO_TOKEN_WHITE_LIST:["/pages/login","/","/pages/config/index","/pages/config/form","/pages/common/webview/index"],HAS_TOKEN_BACK_URL:"/pages/home/<USER>",HAS_TOKEN_WHITE_LIST:["/pages/home/<USER>","/pages/msg/index","/pages/msg/detail","/pages/work/index","/pages/mine/index","/pages/mine/info/edit","/pages/mine/home-config/index","/pages/mine/pwd/index","/pages/mine/info/index"]},mb="envKey",vb="allEnv",yb="sysBaseConfig",bb="homeConfigs",_b="userInfo",wb="userMobileMenus",xb="dictTypeTreeData";let Tb=[mb,vb,yb,bb,_b,wb,xb],Sb=rm("storage_data")||{};const Eb=function(e,t){if(-1!=Tb.indexOf(e)){let n=rm("storage_data");n=n||{},n[e]=t,nm("storage_data",n)}},kb=function(e){return Sb[e]||""},Cb=function(e){delete Sb[e],nm("storage_data",Sb)};var Ob,Mb="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},Ab={};Ob={get exports(){return Ab},set exports(e){Ab=e}},function(){var e;function t(e,t,n){null!=e&&("number"==typeof e?this.fromNumber(e,t,n):null==t&&"string"!=typeof e?this.fromString(e,256):this.fromString(e,t))}function n(){return new t(null)}var o="undefined"!=typeof navigator;o&&"Microsoft Internet Explorer"==navigator.appName?(t.prototype.am=function(e,t,n,o,r,i){for(var s=32767&t,a=t>>15;--i>=0;){var l=32767&this[e],c=this[e++]>>15,u=a*l+c*s;r=((l=s*l+((32767&u)<<15)+n[o]+(1073741823&r))>>>30)+(u>>>15)+a*c+(r>>>30),n[o++]=1073741823&l}return r},e=30):o&&"Netscape"!=navigator.appName?(t.prototype.am=function(e,t,n,o,r,i){for(;--i>=0;){var s=t*this[e++]+n[o]+r;r=Math.floor(s/67108864),n[o++]=67108863&s}return r},e=26):(t.prototype.am=function(e,t,n,o,r,i){for(var s=16383&t,a=t>>14;--i>=0;){var l=16383&this[e],c=this[e++]>>14,u=a*l+c*s;r=((l=s*l+((16383&u)<<14)+n[o]+r)>>28)+(u>>14)+a*c,n[o++]=268435455&l}return r},e=28),t.prototype.DB=e,t.prototype.DM=(1<<e)-1,t.prototype.DV=1<<e,t.prototype.FV=Math.pow(2,52),t.prototype.F1=52-e,t.prototype.F2=2*e-52;var r,i,s=new Array;for(r="0".charCodeAt(0),i=0;i<=9;++i)s[r++]=i;for(r="a".charCodeAt(0),i=10;i<36;++i)s[r++]=i;for(r="A".charCodeAt(0),i=10;i<36;++i)s[r++]=i;function a(e){return"0123456789abcdefghijklmnopqrstuvwxyz".charAt(e)}function l(e,t){var n=s[e.charCodeAt(t)];return null==n?-1:n}function c(e){var t=n();return t.fromInt(e),t}function u(e){var t,n=1;return 0!=(t=e>>>16)&&(e=t,n+=16),0!=(t=e>>8)&&(e=t,n+=8),0!=(t=e>>4)&&(e=t,n+=4),0!=(t=e>>2)&&(e=t,n+=2),0!=(t=e>>1)&&(e=t,n+=1),n}function f(e){this.m=e}function d(e){this.m=e,this.mp=e.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<e.DB-15)-1,this.mt2=2*e.t}function h(e,t){return e&t}function p(e,t){return e|t}function g(e,t){return e^t}function m(e,t){return e&~t}function v(e){if(0==e)return-1;var t=0;return 0==(65535&e)&&(e>>=16,t+=16),0==(255&e)&&(e>>=8,t+=8),0==(15&e)&&(e>>=4,t+=4),0==(3&e)&&(e>>=2,t+=2),0==(1&e)&&++t,t}function y(e){for(var t=0;0!=e;)e&=e-1,++t;return t}function b(){}function _(e){return e}function w(e){this.r2=n(),this.q3=n(),t.ONE.dlShiftTo(2*e.t,this.r2),this.mu=this.r2.divide(e),this.m=e}f.prototype.convert=function(e){return e.s<0||e.compareTo(this.m)>=0?e.mod(this.m):e},f.prototype.revert=function(e){return e},f.prototype.reduce=function(e){e.divRemTo(this.m,null,e)},f.prototype.mulTo=function(e,t,n){e.multiplyTo(t,n),this.reduce(n)},f.prototype.sqrTo=function(e,t){e.squareTo(t),this.reduce(t)},d.prototype.convert=function(e){var o=n();return e.abs().dlShiftTo(this.m.t,o),o.divRemTo(this.m,null,o),e.s<0&&o.compareTo(t.ZERO)>0&&this.m.subTo(o,o),o},d.prototype.revert=function(e){var t=n();return e.copyTo(t),this.reduce(t),t},d.prototype.reduce=function(e){for(;e.t<=this.mt2;)e[e.t++]=0;for(var t=0;t<this.m.t;++t){var n=32767&e[t],o=n*this.mpl+((n*this.mph+(e[t]>>15)*this.mpl&this.um)<<15)&e.DM;for(e[n=t+this.m.t]+=this.m.am(0,o,e,t,0,this.m.t);e[n]>=e.DV;)e[n]-=e.DV,e[++n]++}e.clamp(),e.drShiftTo(this.m.t,e),e.compareTo(this.m)>=0&&e.subTo(this.m,e)},d.prototype.mulTo=function(e,t,n){e.multiplyTo(t,n),this.reduce(n)},d.prototype.sqrTo=function(e,t){e.squareTo(t),this.reduce(t)},t.prototype.copyTo=function(e){for(var t=this.t-1;t>=0;--t)e[t]=this[t];e.t=this.t,e.s=this.s},t.prototype.fromInt=function(e){this.t=1,this.s=e<0?-1:0,e>0?this[0]=e:e<-1?this[0]=e+this.DV:this.t=0},t.prototype.fromString=function(e,n){var o;if(16==n)o=4;else if(8==n)o=3;else if(256==n)o=8;else if(2==n)o=1;else if(32==n)o=5;else{if(4!=n)return void this.fromRadix(e,n);o=2}this.t=0,this.s=0;for(var r=e.length,i=!1,s=0;--r>=0;){var a=8==o?255&e[r]:l(e,r);a<0?"-"==e.charAt(r)&&(i=!0):(i=!1,0==s?this[this.t++]=a:s+o>this.DB?(this[this.t-1]|=(a&(1<<this.DB-s)-1)<<s,this[this.t++]=a>>this.DB-s):this[this.t-1]|=a<<s,(s+=o)>=this.DB&&(s-=this.DB))}8==o&&0!=(128&e[0])&&(this.s=-1,s>0&&(this[this.t-1]|=(1<<this.DB-s)-1<<s)),this.clamp(),i&&t.ZERO.subTo(this,this)},t.prototype.clamp=function(){for(var e=this.s&this.DM;this.t>0&&this[this.t-1]==e;)--this.t},t.prototype.dlShiftTo=function(e,t){var n;for(n=this.t-1;n>=0;--n)t[n+e]=this[n];for(n=e-1;n>=0;--n)t[n]=0;t.t=this.t+e,t.s=this.s},t.prototype.drShiftTo=function(e,t){for(var n=e;n<this.t;++n)t[n-e]=this[n];t.t=Math.max(this.t-e,0),t.s=this.s},t.prototype.lShiftTo=function(e,t){var n,o=e%this.DB,r=this.DB-o,i=(1<<r)-1,s=Math.floor(e/this.DB),a=this.s<<o&this.DM;for(n=this.t-1;n>=0;--n)t[n+s+1]=this[n]>>r|a,a=(this[n]&i)<<o;for(n=s-1;n>=0;--n)t[n]=0;t[s]=a,t.t=this.t+s+1,t.s=this.s,t.clamp()},t.prototype.rShiftTo=function(e,t){t.s=this.s;var n=Math.floor(e/this.DB);if(n>=this.t)t.t=0;else{var o=e%this.DB,r=this.DB-o,i=(1<<o)-1;t[0]=this[n]>>o;for(var s=n+1;s<this.t;++s)t[s-n-1]|=(this[s]&i)<<r,t[s-n]=this[s]>>o;o>0&&(t[this.t-n-1]|=(this.s&i)<<r),t.t=this.t-n,t.clamp()}},t.prototype.subTo=function(e,t){for(var n=0,o=0,r=Math.min(e.t,this.t);n<r;)o+=this[n]-e[n],t[n++]=o&this.DM,o>>=this.DB;if(e.t<this.t){for(o-=e.s;n<this.t;)o+=this[n],t[n++]=o&this.DM,o>>=this.DB;o+=this.s}else{for(o+=this.s;n<e.t;)o-=e[n],t[n++]=o&this.DM,o>>=this.DB;o-=e.s}t.s=o<0?-1:0,o<-1?t[n++]=this.DV+o:o>0&&(t[n++]=o),t.t=n,t.clamp()},t.prototype.multiplyTo=function(e,n){var o=this.abs(),r=e.abs(),i=o.t;for(n.t=i+r.t;--i>=0;)n[i]=0;for(i=0;i<r.t;++i)n[i+o.t]=o.am(0,r[i],n,i,0,o.t);n.s=0,n.clamp(),this.s!=e.s&&t.ZERO.subTo(n,n)},t.prototype.squareTo=function(e){for(var t=this.abs(),n=e.t=2*t.t;--n>=0;)e[n]=0;for(n=0;n<t.t-1;++n){var o=t.am(n,t[n],e,2*n,0,1);(e[n+t.t]+=t.am(n+1,2*t[n],e,2*n+1,o,t.t-n-1))>=t.DV&&(e[n+t.t]-=t.DV,e[n+t.t+1]=1)}e.t>0&&(e[e.t-1]+=t.am(n,t[n],e,2*n,0,1)),e.s=0,e.clamp()},t.prototype.divRemTo=function(e,o,r){var i=e.abs();if(!(i.t<=0)){var s=this.abs();if(s.t<i.t)return null!=o&&o.fromInt(0),void(null!=r&&this.copyTo(r));null==r&&(r=n());var a=n(),l=this.s,c=e.s,f=this.DB-u(i[i.t-1]);f>0?(i.lShiftTo(f,a),s.lShiftTo(f,r)):(i.copyTo(a),s.copyTo(r));var d=a.t,h=a[d-1];if(0!=h){var p=h*(1<<this.F1)+(d>1?a[d-2]>>this.F2:0),g=this.FV/p,m=(1<<this.F1)/p,v=1<<this.F2,y=r.t,b=y-d,_=null==o?n():o;for(a.dlShiftTo(b,_),r.compareTo(_)>=0&&(r[r.t++]=1,r.subTo(_,r)),t.ONE.dlShiftTo(d,_),_.subTo(a,a);a.t<d;)a[a.t++]=0;for(;--b>=0;){var w=r[--y]==h?this.DM:Math.floor(r[y]*g+(r[y-1]+v)*m);if((r[y]+=a.am(0,w,r,b,0,d))<w)for(a.dlShiftTo(b,_),r.subTo(_,r);r[y]<--w;)r.subTo(_,r)}null!=o&&(r.drShiftTo(d,o),l!=c&&t.ZERO.subTo(o,o)),r.t=d,r.clamp(),f>0&&r.rShiftTo(f,r),l<0&&t.ZERO.subTo(r,r)}}},t.prototype.invDigit=function(){if(this.t<1)return 0;var e=this[0];if(0==(1&e))return 0;var t=3&e;return(t=(t=(t=(t=t*(2-(15&e)*t)&15)*(2-(255&e)*t)&255)*(2-((65535&e)*t&65535))&65535)*(2-e*t%this.DV)%this.DV)>0?this.DV-t:-t},t.prototype.isEven=function(){return 0==(this.t>0?1&this[0]:this.s)},t.prototype.exp=function(e,o){if(e>4294967295||e<1)return t.ONE;var r=n(),i=n(),s=o.convert(this),a=u(e)-1;for(s.copyTo(r);--a>=0;)if(o.sqrTo(r,i),(e&1<<a)>0)o.mulTo(i,s,r);else{var l=r;r=i,i=l}return o.revert(r)},t.prototype.toString=function(e){if(this.s<0)return"-"+this.negate().toString(e);var t;if(16==e)t=4;else if(8==e)t=3;else if(2==e)t=1;else if(32==e)t=5;else{if(4!=e)return this.toRadix(e);t=2}var n,o=(1<<t)-1,r=!1,i="",s=this.t,l=this.DB-s*this.DB%t;if(s-- >0)for(l<this.DB&&(n=this[s]>>l)>0&&(r=!0,i=a(n));s>=0;)l<t?(n=(this[s]&(1<<l)-1)<<t-l,n|=this[--s]>>(l+=this.DB-t)):(n=this[s]>>(l-=t)&o,l<=0&&(l+=this.DB,--s)),n>0&&(r=!0),r&&(i+=a(n));return r?i:"0"},t.prototype.negate=function(){var e=n();return t.ZERO.subTo(this,e),e},t.prototype.abs=function(){return this.s<0?this.negate():this},t.prototype.compareTo=function(e){var t=this.s-e.s;if(0!=t)return t;var n=this.t;if(0!=(t=n-e.t))return this.s<0?-t:t;for(;--n>=0;)if(0!=(t=this[n]-e[n]))return t;return 0},t.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+u(this[this.t-1]^this.s&this.DM)},t.prototype.mod=function(e){var o=n();return this.abs().divRemTo(e,null,o),this.s<0&&o.compareTo(t.ZERO)>0&&e.subTo(o,o),o},t.prototype.modPowInt=function(e,t){var n;return n=e<256||t.isEven()?new f(t):new d(t),this.exp(e,n)},t.ZERO=c(0),t.ONE=c(1),b.prototype.convert=_,b.prototype.revert=_,b.prototype.mulTo=function(e,t,n){e.multiplyTo(t,n)},b.prototype.sqrTo=function(e,t){e.squareTo(t)},w.prototype.convert=function(e){if(e.s<0||e.t>2*this.m.t)return e.mod(this.m);if(e.compareTo(this.m)<0)return e;var t=n();return e.copyTo(t),this.reduce(t),t},w.prototype.revert=function(e){return e},w.prototype.reduce=function(e){for(e.drShiftTo(this.m.t-1,this.r2),e.t>this.m.t+1&&(e.t=this.m.t+1,e.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);e.compareTo(this.r2)<0;)e.dAddOffset(1,this.m.t+1);for(e.subTo(this.r2,e);e.compareTo(this.m)>=0;)e.subTo(this.m,e)},w.prototype.mulTo=function(e,t,n){e.multiplyTo(t,n),this.reduce(n)},w.prototype.sqrTo=function(e,t){e.squareTo(t),this.reduce(t)};var x,T,S,E=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],k=(1<<26)/E[E.length-1];function C(){var e;e=(new Date).getTime(),T[S++]^=255&e,T[S++]^=e>>8&255,T[S++]^=e>>16&255,T[S++]^=e>>24&255,S>=D&&(S-=D)}if(t.prototype.chunkSize=function(e){return Math.floor(Math.LN2*this.DB/Math.log(e))},t.prototype.toRadix=function(e){if(null==e&&(e=10),0==this.signum()||e<2||e>36)return"0";var t=this.chunkSize(e),o=Math.pow(e,t),r=c(o),i=n(),s=n(),a="";for(this.divRemTo(r,i,s);i.signum()>0;)a=(o+s.intValue()).toString(e).substr(1)+a,i.divRemTo(r,i,s);return s.intValue().toString(e)+a},t.prototype.fromRadix=function(e,n){this.fromInt(0),null==n&&(n=10);for(var o=this.chunkSize(n),r=Math.pow(n,o),i=!1,s=0,a=0,c=0;c<e.length;++c){var u=l(e,c);u<0?"-"==e.charAt(c)&&0==this.signum()&&(i=!0):(a=n*a+u,++s>=o&&(this.dMultiply(r),this.dAddOffset(a,0),s=0,a=0))}s>0&&(this.dMultiply(Math.pow(n,s)),this.dAddOffset(a,0)),i&&t.ZERO.subTo(this,this)},t.prototype.fromNumber=function(e,n,o){if("number"==typeof n)if(e<2)this.fromInt(1);else for(this.fromNumber(e,o),this.testBit(e-1)||this.bitwiseTo(t.ONE.shiftLeft(e-1),p,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(n);)this.dAddOffset(2,0),this.bitLength()>e&&this.subTo(t.ONE.shiftLeft(e-1),this);else{var r=new Array,i=7&e;r.length=1+(e>>3),n.nextBytes(r),i>0?r[0]&=(1<<i)-1:r[0]=0,this.fromString(r,256)}},t.prototype.bitwiseTo=function(e,t,n){var o,r,i=Math.min(e.t,this.t);for(o=0;o<i;++o)n[o]=t(this[o],e[o]);if(e.t<this.t){for(r=e.s&this.DM,o=i;o<this.t;++o)n[o]=t(this[o],r);n.t=this.t}else{for(r=this.s&this.DM,o=i;o<e.t;++o)n[o]=t(r,e[o]);n.t=e.t}n.s=t(this.s,e.s),n.clamp()},t.prototype.changeBit=function(e,n){var o=t.ONE.shiftLeft(e);return this.bitwiseTo(o,n,o),o},t.prototype.addTo=function(e,t){for(var n=0,o=0,r=Math.min(e.t,this.t);n<r;)o+=this[n]+e[n],t[n++]=o&this.DM,o>>=this.DB;if(e.t<this.t){for(o+=e.s;n<this.t;)o+=this[n],t[n++]=o&this.DM,o>>=this.DB;o+=this.s}else{for(o+=this.s;n<e.t;)o+=e[n],t[n++]=o&this.DM,o>>=this.DB;o+=e.s}t.s=o<0?-1:0,o>0?t[n++]=o:o<-1&&(t[n++]=this.DV+o),t.t=n,t.clamp()},t.prototype.dMultiply=function(e){this[this.t]=this.am(0,e-1,this,0,0,this.t),++this.t,this.clamp()},t.prototype.dAddOffset=function(e,t){if(0!=e){for(;this.t<=t;)this[this.t++]=0;for(this[t]+=e;this[t]>=this.DV;)this[t]-=this.DV,++t>=this.t&&(this[this.t++]=0),++this[t]}},t.prototype.multiplyLowerTo=function(e,t,n){var o,r=Math.min(this.t+e.t,t);for(n.s=0,n.t=r;r>0;)n[--r]=0;for(o=n.t-this.t;r<o;++r)n[r+this.t]=this.am(0,e[r],n,r,0,this.t);for(o=Math.min(e.t,t);r<o;++r)this.am(0,e[r],n,r,0,t-r);n.clamp()},t.prototype.multiplyUpperTo=function(e,t,n){--t;var o=n.t=this.t+e.t-t;for(n.s=0;--o>=0;)n[o]=0;for(o=Math.max(t-this.t,0);o<e.t;++o)n[this.t+o-t]=this.am(t-o,e[o],n,0,0,this.t+o-t);n.clamp(),n.drShiftTo(1,n)},t.prototype.modInt=function(e){if(e<=0)return 0;var t=this.DV%e,n=this.s<0?e-1:0;if(this.t>0)if(0==t)n=this[0]%e;else for(var o=this.t-1;o>=0;--o)n=(t*n+this[o])%e;return n},t.prototype.millerRabin=function(e){var o=this.subtract(t.ONE),r=o.getLowestSetBit();if(r<=0)return!1;var i=o.shiftRight(r);(e=e+1>>1)>E.length&&(e=E.length);for(var s=n(),a=0;a<e;++a){s.fromInt(E[Math.floor(Math.random()*E.length)]);var l=s.modPow(i,this);if(0!=l.compareTo(t.ONE)&&0!=l.compareTo(o)){for(var c=1;c++<r&&0!=l.compareTo(o);)if(0==(l=l.modPowInt(2,this)).compareTo(t.ONE))return!1;if(0!=l.compareTo(o))return!1}}return!0},t.prototype.clone=function(){var e=n();return this.copyTo(e),e},t.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},t.prototype.byteValue=function(){return 0==this.t?this.s:this[0]<<24>>24},t.prototype.shortValue=function(){return 0==this.t?this.s:this[0]<<16>>16},t.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1},t.prototype.toByteArray=function(){var e=this.t,t=new Array;t[0]=this.s;var n,o=this.DB-e*this.DB%8,r=0;if(e-- >0)for(o<this.DB&&(n=this[e]>>o)!=(this.s&this.DM)>>o&&(t[r++]=n|this.s<<this.DB-o);e>=0;)o<8?(n=(this[e]&(1<<o)-1)<<8-o,n|=this[--e]>>(o+=this.DB-8)):(n=this[e]>>(o-=8)&255,o<=0&&(o+=this.DB,--e)),0!=(128&n)&&(n|=-256),0==r&&(128&this.s)!=(128&n)&&++r,(r>0||n!=this.s)&&(t[r++]=n);return t},t.prototype.equals=function(e){return 0==this.compareTo(e)},t.prototype.min=function(e){return this.compareTo(e)<0?this:e},t.prototype.max=function(e){return this.compareTo(e)>0?this:e},t.prototype.and=function(e){var t=n();return this.bitwiseTo(e,h,t),t},t.prototype.or=function(e){var t=n();return this.bitwiseTo(e,p,t),t},t.prototype.xor=function(e){var t=n();return this.bitwiseTo(e,g,t),t},t.prototype.andNot=function(e){var t=n();return this.bitwiseTo(e,m,t),t},t.prototype.not=function(){for(var e=n(),t=0;t<this.t;++t)e[t]=this.DM&~this[t];return e.t=this.t,e.s=~this.s,e},t.prototype.shiftLeft=function(e){var t=n();return e<0?this.rShiftTo(-e,t):this.lShiftTo(e,t),t},t.prototype.shiftRight=function(e){var t=n();return e<0?this.lShiftTo(-e,t):this.rShiftTo(e,t),t},t.prototype.getLowestSetBit=function(){for(var e=0;e<this.t;++e)if(0!=this[e])return e*this.DB+v(this[e]);return this.s<0?this.t*this.DB:-1},t.prototype.bitCount=function(){for(var e=0,t=this.s&this.DM,n=0;n<this.t;++n)e+=y(this[n]^t);return e},t.prototype.testBit=function(e){var t=Math.floor(e/this.DB);return t>=this.t?0!=this.s:0!=(this[t]&1<<e%this.DB)},t.prototype.setBit=function(e){return this.changeBit(e,p)},t.prototype.clearBit=function(e){return this.changeBit(e,m)},t.prototype.flipBit=function(e){return this.changeBit(e,g)},t.prototype.add=function(e){var t=n();return this.addTo(e,t),t},t.prototype.subtract=function(e){var t=n();return this.subTo(e,t),t},t.prototype.multiply=function(e){var t=n();return this.multiplyTo(e,t),t},t.prototype.divide=function(e){var t=n();return this.divRemTo(e,t,null),t},t.prototype.remainder=function(e){var t=n();return this.divRemTo(e,null,t),t},t.prototype.divideAndRemainder=function(e){var t=n(),o=n();return this.divRemTo(e,t,o),new Array(t,o)},t.prototype.modPow=function(e,t){var o,r,i=e.bitLength(),s=c(1);if(i<=0)return s;o=i<18?1:i<48?3:i<144?4:i<768?5:6,r=i<8?new f(t):t.isEven()?new w(t):new d(t);var a=new Array,l=3,h=o-1,p=(1<<o)-1;if(a[1]=r.convert(this),o>1){var g=n();for(r.sqrTo(a[1],g);l<=p;)a[l]=n(),r.mulTo(g,a[l-2],a[l]),l+=2}var m,v,y=e.t-1,b=!0,_=n();for(i=u(e[y])-1;y>=0;){for(i>=h?m=e[y]>>i-h&p:(m=(e[y]&(1<<i+1)-1)<<h-i,y>0&&(m|=e[y-1]>>this.DB+i-h)),l=o;0==(1&m);)m>>=1,--l;if((i-=l)<0&&(i+=this.DB,--y),b)a[m].copyTo(s),b=!1;else{for(;l>1;)r.sqrTo(s,_),r.sqrTo(_,s),l-=2;l>0?r.sqrTo(s,_):(v=s,s=_,_=v),r.mulTo(_,a[m],s)}for(;y>=0&&0==(e[y]&1<<i);)r.sqrTo(s,_),v=s,s=_,_=v,--i<0&&(i=this.DB-1,--y)}return r.revert(s)},t.prototype.modInverse=function(e){var n=e.isEven();if(this.isEven()&&n||0==e.signum())return t.ZERO;for(var o=e.clone(),r=this.clone(),i=c(1),s=c(0),a=c(0),l=c(1);0!=o.signum();){for(;o.isEven();)o.rShiftTo(1,o),n?(i.isEven()&&s.isEven()||(i.addTo(this,i),s.subTo(e,s)),i.rShiftTo(1,i)):s.isEven()||s.subTo(e,s),s.rShiftTo(1,s);for(;r.isEven();)r.rShiftTo(1,r),n?(a.isEven()&&l.isEven()||(a.addTo(this,a),l.subTo(e,l)),a.rShiftTo(1,a)):l.isEven()||l.subTo(e,l),l.rShiftTo(1,l);o.compareTo(r)>=0?(o.subTo(r,o),n&&i.subTo(a,i),s.subTo(l,s)):(r.subTo(o,r),n&&a.subTo(i,a),l.subTo(s,l))}return 0!=r.compareTo(t.ONE)?t.ZERO:l.compareTo(e)>=0?l.subtract(e):l.signum()<0?(l.addTo(e,l),l.signum()<0?l.add(e):l):l},t.prototype.pow=function(e){return this.exp(e,new b)},t.prototype.gcd=function(e){var t=this.s<0?this.negate():this.clone(),n=e.s<0?e.negate():e.clone();if(t.compareTo(n)<0){var o=t;t=n,n=o}var r=t.getLowestSetBit(),i=n.getLowestSetBit();if(i<0)return t;for(r<i&&(i=r),i>0&&(t.rShiftTo(i,t),n.rShiftTo(i,n));t.signum()>0;)(r=t.getLowestSetBit())>0&&t.rShiftTo(r,t),(r=n.getLowestSetBit())>0&&n.rShiftTo(r,n),t.compareTo(n)>=0?(t.subTo(n,t),t.rShiftTo(1,t)):(n.subTo(t,n),n.rShiftTo(1,n));return i>0&&n.lShiftTo(i,n),n},t.prototype.isProbablePrime=function(e){var t,n=this.abs();if(1==n.t&&n[0]<=E[E.length-1]){for(t=0;t<E.length;++t)if(n[0]==E[t])return!0;return!1}if(n.isEven())return!1;for(t=1;t<E.length;){for(var o=E[t],r=t+1;r<E.length&&o<k;)o*=E[r++];for(o=n.modInt(o);t<r;)if(o%E[t++]==0)return!1}return n.millerRabin(e)},t.prototype.square=function(){var e=n();return this.squareTo(e),e},t.prototype.Barrett=w,null==T){var O;if(T=new Array,S=0,"undefined"!=typeof window&&window.crypto)if(window.crypto.getRandomValues){var M=new Uint8Array(32);for(window.crypto.getRandomValues(M),O=0;O<32;++O)T[S++]=M[O]}else if("Netscape"==navigator.appName&&navigator.appVersion<"5"){var A=window.crypto.random(32);for(O=0;O<A.length;++O)T[S++]=255&A.charCodeAt(O)}for(;S<D;)O=Math.floor(65536*Math.random()),T[S++]=O>>>8,T[S++]=255&O;S=0,C()}function P(){if(null==x){for(C(),(x=new $).init(T),S=0;S<T.length;++S)T[S]=0;S=0}return x.next()}function I(){}function $(){this.i=0,this.j=0,this.S=new Array}I.prototype.nextBytes=function(e){var t;for(t=0;t<e.length;++t)e[t]=P()},$.prototype.init=function(e){var t,n,o;for(t=0;t<256;++t)this.S[t]=t;for(n=0,t=0;t<256;++t)n=n+this.S[t]+e[t%e.length]&255,o=this.S[t],this.S[t]=this.S[n],this.S[n]=o;this.i=0,this.j=0},$.prototype.next=function(){var e;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,e=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=e,this.S[e+this.S[this.i]&255]};var D=256;Ob.exports={default:t,BigInteger:t,SecureRandom:I}}.call(Mb);const{BigInteger:Pb}=Ab;class Ib{constructor(){this.tlv=null,this.t="00",this.l="00",this.v=""}getEncodedHex(){return this.tlv||(this.v=this.getValue(),this.l=this.getLength(),this.tlv=this.t+this.l+this.v),this.tlv}getLength(){const e=this.v.length/2;let t=e.toString(16);if(t.length%2==1&&(t="0"+t),e<128)return t;return(128+t.length/2).toString(16)+t}getValue(){return""}}class $b extends Ib{constructor(e){super(),this.t="02",e&&(this.v=function(e){let t=e.toString(16);if("-"!==t[0])t.length%2==1?t="0"+t:t.match(/^[0-7]/)||(t="00"+t);else{t=t.substr(1);let n=t.length;n%2==1?n+=1:t.match(/^[0-7]/)||(n+=2);let o="";for(let e=0;e<n;e++)o+="f";o=new Pb(o,16),t=o.xor(e).add(Pb.ONE),t=t.toString(16).replace(/^-/,"")}return t}(e))}getValue(){return this.v}}class Db extends Ib{constructor(e){super(),this.t="30",this.asn1Array=e}getValue(){return this.v=this.asn1Array.map((e=>e.getEncodedHex())).join(""),this.v}}function Lb(e,t){return+e[t+2]<8?1:128&+e.substr(t+2,2)}function Bb(e,t){const n=Lb(e,t),o=e.substr(t+2,2*n);if(!o)return-1;return(+o[0]<8?new Pb(o,16):new Pb(o.substr(2),16)).intValue()}function Fb(e,t){return t+2*(Lb(e,t)+1)}var Rb={encodeDer(e,t){const n=new $b(e),o=new $b(t);return new Db([n,o]).getEncodedHex()},decodeDer(e){const t=Fb(e,0),n=Fb(e,t),o=Bb(e,t),r=e.substr(n,2*o),i=n+r.length,s=Fb(e,i),a=Bb(e,i),l=e.substr(s,2*a);return{r:new Pb(r,16),s:new Pb(l,16)}}};const{BigInteger:Nb}=Ab,jb=new Nb("2"),qb=new Nb("3");class zb{constructor(e,t){this.x=t,this.q=e}equals(e){return e===this||this.q.equals(e.q)&&this.x.equals(e.x)}toBigInteger(){return this.x}negate(){return new zb(this.q,this.x.negate().mod(this.q))}add(e){return new zb(this.q,this.x.add(e.toBigInteger()).mod(this.q))}subtract(e){return new zb(this.q,this.x.subtract(e.toBigInteger()).mod(this.q))}multiply(e){return new zb(this.q,this.x.multiply(e.toBigInteger()).mod(this.q))}divide(e){return new zb(this.q,this.x.multiply(e.toBigInteger().modInverse(this.q)).mod(this.q))}square(){return new zb(this.q,this.x.square().mod(this.q))}}class Hb{constructor(e,t,n,o){this.curve=e,this.x=t,this.y=n,this.z=null==o?Nb.ONE:o,this.zinv=null}getX(){return null===this.zinv&&(this.zinv=this.z.modInverse(this.curve.q)),this.curve.fromBigInteger(this.x.toBigInteger().multiply(this.zinv).mod(this.curve.q))}getY(){return null===this.zinv&&(this.zinv=this.z.modInverse(this.curve.q)),this.curve.fromBigInteger(this.y.toBigInteger().multiply(this.zinv).mod(this.curve.q))}equals(e){if(e===this)return!0;if(this.isInfinity())return e.isInfinity();if(e.isInfinity())return this.isInfinity();if(!e.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(e.z)).mod(this.curve.q).equals(Nb.ZERO))return!1;return e.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(e.z)).mod(this.curve.q).equals(Nb.ZERO)}isInfinity(){return null===this.x&&null===this.y||this.z.equals(Nb.ZERO)&&!this.y.toBigInteger().equals(Nb.ZERO)}negate(){return new Hb(this.curve,this.x,this.y.negate(),this.z)}add(e){if(this.isInfinity())return e;if(e.isInfinity())return this;const t=this.x.toBigInteger(),n=this.y.toBigInteger(),o=this.z,r=e.x.toBigInteger(),i=e.y.toBigInteger(),s=e.z,a=this.curve.q,l=t.multiply(s).mod(a),c=r.multiply(o).mod(a),u=l.subtract(c),f=n.multiply(s).mod(a),d=i.multiply(o).mod(a),h=f.subtract(d);if(Nb.ZERO.equals(u))return Nb.ZERO.equals(h)?this.twice():this.curve.infinity;const p=l.add(c),g=o.multiply(s).mod(a),m=u.square().mod(a),v=u.multiply(m).mod(a),y=g.multiply(h.square()).subtract(p.multiply(m)).mod(a),b=u.multiply(y).mod(a),_=h.multiply(m.multiply(l).subtract(y)).subtract(f.multiply(v)).mod(a),w=v.multiply(g).mod(a);return new Hb(this.curve,this.curve.fromBigInteger(b),this.curve.fromBigInteger(_),w)}twice(){if(this.isInfinity())return this;if(!this.y.toBigInteger().signum())return this.curve.infinity;const e=this.x.toBigInteger(),t=this.y.toBigInteger(),n=this.z,o=this.curve.q,r=this.curve.a.toBigInteger(),i=e.square().multiply(qb).add(r.multiply(n.square())).mod(o),s=t.shiftLeft(1).multiply(n).mod(o),a=t.square().mod(o),l=a.multiply(e).multiply(n).mod(o),c=s.square().mod(o),u=i.square().subtract(l.shiftLeft(3)).mod(o),f=s.multiply(u).mod(o),d=i.multiply(l.shiftLeft(2).subtract(u)).subtract(c.shiftLeft(1).multiply(a)).mod(o),h=s.multiply(c).mod(o);return new Hb(this.curve,this.curve.fromBigInteger(f),this.curve.fromBigInteger(d),h)}multiply(e){if(this.isInfinity())return this;if(!e.signum())return this.curve.infinity;const t=e.multiply(qb),n=this.negate();let o=this;for(let r=t.bitLength()-2;r>0;r--){o=o.twice();const i=t.testBit(r);i!==e.testBit(r)&&(o=o.add(i?this:n))}return o}}var Vb={ECPointFp:Hb,ECCurveFp:class{constructor(e,t,n){this.q=e,this.a=this.fromBigInteger(t),this.b=this.fromBigInteger(n),this.infinity=new Hb(this,null,null)}equals(e){return e===this||this.q.equals(e.q)&&this.a.equals(e.a)&&this.b.equals(e.b)}fromBigInteger(e){return new zb(this.q,e)}decodePointHex(e){switch(parseInt(e.substr(0,2),16)){case 0:return this.infinity;case 2:case 3:const t=this.fromBigInteger(new Nb(e.substr(2),16));let n=this.fromBigInteger(t.multiply(t.square()).add(t.multiply(this.a)).add(this.b).toBigInteger().modPow(this.q.divide(new Nb("4")).add(Nb.ONE),this.q));return n.toBigInteger().mod(jb).equals(new Nb(e.substr(0,2),16).subtract(jb))||(n=n.negate()),new Hb(this,t,n);case 4:case 6:case 7:const o=(e.length-2)/2,r=e.substr(2,o),i=e.substr(o+2,o);return new Hb(this,this.fromBigInteger(new Nb(r,16)),this.fromBigInteger(new Nb(i,16)));default:return null}}}};const{BigInteger:Wb,SecureRandom:Ub}=Ab,{ECCurveFp:Yb}=Vb,Xb=new Ub,{curve:Kb,G:Gb,n:Zb}=Jb();function Jb(){const e=new Wb("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF",16),t=new Wb("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC",16),n=new Wb("28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93",16),o=new Yb(e,t,n),r=o.decodePointHex("0432C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0");return{curve:o,G:r,n:new Wb("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123",16)}}function Qb(e,t){return e.length>=t?e:new Array(t-e.length+1).join("0")+e}var e_={getGlobalCurve:function(){return Kb},generateEcparam:Jb,generateKeyPairHex:function(e,t,n){const o=(e?new Wb(e,t,n):new Wb(Zb.bitLength(),Xb)).mod(Zb.subtract(Wb.ONE)).add(Wb.ONE),r=Qb(o.toString(16),64),i=Gb.multiply(o);return{privateKey:r,publicKey:"04"+Qb(i.getX().toBigInteger().toString(16),64)+Qb(i.getY().toBigInteger().toString(16),64)}},compressPublicKeyHex:function(e){if(130!==e.length)throw new Error("Invalid public key to compress");const t=(e.length-2)/2,n=e.substr(2,t);let o="03";return new Wb(e.substr(t+2,t),16).mod(new Wb("2")).equals(Wb.ZERO)&&(o="02"),o+n},utf8ToHex:function(e){const t=(e=unescape(encodeURIComponent(e))).length,n=[];for(let r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;const o=[];for(let r=0;r<t;r++){const e=n[r>>>2]>>>24-r%4*8&255;o.push((e>>>4).toString(16)),o.push((15&e).toString(16))}return o.join("")},leftPad:Qb,arrayToHex:function(e){return e.map((e=>1===(e=e.toString(16)).length?"0"+e:e)).join("")},arrayToUtf8:function(e){const t=[];let n=0;for(let r=0;r<2*e.length;r+=2)t[r>>>3]|=parseInt(e[n],10)<<24-r%8*4,n++;try{const n=[];for(let o=0;o<e.length;o++){const e=t[o>>>2]>>>24-o%4*8&255;n.push(String.fromCharCode(e))}return decodeURIComponent(escape(n.join("")))}catch(o){throw new Error("Malformed UTF-8 data")}},hexToArray:function(e){const t=[];let n=e.length;n%2!=0&&(e=Qb(e,n+1)),n=e.length;for(let o=0;o<n;o+=2)t.push(parseInt(e.substr(o,2),16));return t},verifyPublicKey:function(e){const t=Kb.decodePointHex(e);if(!t)return!1;const n=t.getX();return t.getY().square().equals(n.multiply(n.square()).add(n.multiply(Kb.a)).add(Kb.b))},comparePublicKeyHex:function(e,t){const n=Kb.decodePointHex(e);if(!n)return!1;const o=Kb.decodePointHex(t);return!!o&&n.equals(o)}};const t_=new Uint32Array(68),n_=new Uint32Array(64);function o_(e,t){const n=31&t;return e<<n|e>>>32-n}function r_(e,t){const n=[];for(let o=e.length-1;o>=0;o--)n[o]=255&(e[o]^t[o]);return n}function i_(e){return e^o_(e,9)^o_(e,17)}function s_(e){let t=8*e.length,n=t%512;n=n>=448?512-n%448-1:448-n-1;const o=new Array((n-7)/8),r=new Array(8);for(let f=0,d=o.length;f<d;f++)o[f]=0;for(let f=0,d=r.length;f<d;f++)r[f]=0;t=t.toString(2);for(let f=7;f>=0;f--)if(t.length>8){const e=t.length-8;r[f]=parseInt(t.substr(e),2),t=t.substr(0,e)}else t.length>0&&(r[f]=parseInt(t,2),t="");const i=new Uint8Array([...e,128,...o,...r]),s=new DataView(i.buffer,0),a=i.length/64,l=new Uint32Array([1937774191,1226093241,388252375,3666478592,2842636476,372324522,3817729613,2969243214]);for(let f=0;f<a;f++){t_.fill(0),n_.fill(0);const e=16*f;for(let l=0;l<16;l++)t_[l]=s.getUint32(4*(e+l),!1);for(let s=16;s<68;s++)t_[s]=(c=t_[s-16]^t_[s-9]^o_(t_[s-3],15))^o_(c,15)^o_(c,23)^o_(t_[s-13],7)^t_[s-6];for(let s=0;s<64;s++)n_[s]=t_[s]^t_[s+4];const t=2043430169,n=2055708042;let o,r,i,a,u,d=l[0],h=l[1],p=l[2],g=l[3],m=l[4],v=l[5],y=l[6],b=l[7];for(let s=0;s<64;s++)u=s>=0&&s<=15?t:n,o=o_(o_(d,12)+m+o_(u,s),7),r=o^o_(d,12),i=(s>=0&&s<=15?d^h^p:d&h|d&p|h&p)+g+r+n_[s],a=(s>=0&&s<=15?m^v^y:m&v|~m&y)+b+o+t_[s],g=p,p=o_(h,9),h=d,d=i,b=y,y=o_(v,19),v=m,m=i_(a);l[0]^=d,l[1]^=h,l[2]^=p,l[3]^=g,l[4]^=m,l[5]^=v,l[6]^=y,l[7]^=b}var c;const u=[];for(let f=0,d=l.length;f<d;f++){const e=l[f];u.push((4278190080&e)>>>24,(16711680&e)>>>16,(65280&e)>>>8,255&e)}return u}const a_=new Uint8Array(64),l_=new Uint8Array(64);for(let XE=0;XE<64;XE++)a_[XE]=54,l_[XE]=92;var c_={sm3:s_,hmac:function(e,t){for(t.length>64&&(t=s_(t));t.length<64;)t.push(0);const n=r_(t,a_),o=r_(t,l_),r=s_([...n,...e]);return s_([...o,...r])}};const{BigInteger:u_}=Ab,{encodeDer:f_,decodeDer:d_}=Rb,h_=e_,p_=c_.sm3,{G:g_,curve:m_,n:v_}=h_.generateEcparam();function y_(e,t,n="1234567812345678"){n=h_.utf8ToHex(n);const o=h_.leftPad(g_.curve.a.toBigInteger().toRadix(16),64),r=h_.leftPad(g_.curve.b.toBigInteger().toRadix(16),64),i=h_.leftPad(g_.getX().toBigInteger().toRadix(16),64),s=h_.leftPad(g_.getY().toBigInteger().toRadix(16),64);let a,l;if(128===t.length)a=t.substr(0,64),l=t.substr(64,64);else{const e=g_.curve.decodePointHex(t);a=h_.leftPad(e.getX().toBigInteger().toRadix(16),64),l=h_.leftPad(e.getY().toBigInteger().toRadix(16),64)}const c=h_.hexToArray(n+o+r+i+s+a+l),u=4*n.length;c.unshift(255&u),c.unshift(u>>8&255);const f=p_(c);return h_.arrayToHex(p_(f.concat(h_.hexToArray(e))))}function b_(e){const t=g_.multiply(new u_(e,16));return"04"+h_.leftPad(t.getX().toBigInteger().toString(16),64)+h_.leftPad(t.getY().toBigInteger().toString(16),64)}function __(){const e=h_.generateKeyPairHex(),t=m_.decodePointHex(e.publicKey);return e.k=new u_(e.privateKey,16),e.x1=t.getX().toBigInteger(),e}var w_={generateKeyPairHex:h_.generateKeyPairHex,compressPublicKeyHex:h_.compressPublicKeyHex,comparePublicKeyHex:h_.comparePublicKeyHex,doEncrypt:function(e,t,n=1){e="string"==typeof e?h_.hexToArray(h_.utf8ToHex(e)):Array.prototype.slice.call(e),t=h_.getGlobalCurve().decodePointHex(t);const o=h_.generateKeyPairHex(),r=new u_(o.privateKey,16);let i=o.publicKey;i.length>128&&(i=i.substr(i.length-128));const s=t.multiply(r),a=h_.hexToArray(h_.leftPad(s.getX().toBigInteger().toRadix(16),64)),l=h_.hexToArray(h_.leftPad(s.getY().toBigInteger().toRadix(16),64)),c=h_.arrayToHex(p_([].concat(a,e,l)));let u=1,f=0,d=[];const h=[].concat(a,l),p=()=>{d=p_([...h,u>>24&255,u>>16&255,u>>8&255,255&u]),u++,f=0};p();for(let m=0,v=e.length;m<v;m++)f===d.length&&p(),e[m]^=255&d[f++];const g=h_.arrayToHex(e);return 0===n?i+g+c:i+c+g},doDecrypt:function(e,t,n=1,{output:o="string"}={}){t=new u_(t,16);let r=e.substr(128,64),i=e.substr(192);0===n&&(r=e.substr(e.length-64),i=e.substr(128,e.length-128-64));const s=h_.hexToArray(i),a=h_.getGlobalCurve().decodePointHex("04"+e.substr(0,128)).multiply(t),l=h_.hexToArray(h_.leftPad(a.getX().toBigInteger().toRadix(16),64)),c=h_.hexToArray(h_.leftPad(a.getY().toBigInteger().toRadix(16),64));let u=1,f=0,d=[];const h=[].concat(l,c),p=()=>{d=p_([...h,u>>24&255,u>>16&255,u>>8&255,255&u]),u++,f=0};p();for(let g=0,m=s.length;g<m;g++)f===d.length&&p(),s[g]^=255&d[f++];return h_.arrayToHex(p_([].concat(l,s,c)))===r.toLowerCase()?"array"===o?s:h_.arrayToUtf8(s):"array"===o?[]:""},doSignature:function(e,t,{pointPool:n,der:o,hash:r,publicKey:i,userId:s}={}){let a="string"==typeof e?h_.utf8ToHex(e):h_.arrayToHex(e);r&&(a=y_(a,i=i||b_(t),s));const l=new u_(t,16),c=new u_(a,16);let u=null,f=null,d=null;do{do{let e;e=n&&n.length?n.pop():__(),u=e.k,f=c.add(e.x1).mod(v_)}while(f.equals(u_.ZERO)||f.add(u).equals(v_));d=l.add(u_.ONE).modInverse(v_).multiply(u.subtract(f.multiply(l))).mod(v_)}while(d.equals(u_.ZERO));return o?f_(f,d):h_.leftPad(f.toString(16),64)+h_.leftPad(d.toString(16),64)},doVerifySignature:function(e,t,n,{der:o,hash:r,userId:i}={}){let s,a,l="string"==typeof e?h_.utf8ToHex(e):h_.arrayToHex(e);if(r&&(l=y_(l,n,i)),o){const e=d_(t);s=e.r,a=e.s}else s=new u_(t.substring(0,64),16),a=new u_(t.substring(64),16);const c=m_.decodePointHex(n),u=new u_(l,16),f=s.add(a).mod(v_);if(f.equals(u_.ZERO))return!1;const d=g_.multiply(a).add(c.multiply(f)),h=u.add(d.getX().toBigInteger()).mod(v_);return s.equals(h)},getPublicKeyFromPrivateKey:b_,getPoint:__,verifyPublicKey:h_.verifyPublicKey};const{sm3:x_,hmac:T_}=c_;function S_(e){return e.map((e=>1===(e=e.toString(16)).length?"0"+e:e)).join("")}function E_(e){const t=[];let n=e.length;var o,r;n%2!=0&&(r=n+1,e=(o=e).length>=r?o:new Array(r-o.length+1).join("0")+o),n=e.length;for(let i=0;i<n;i+=2)t.push(parseInt(e.substr(i,2),16));return t}const k_=[214,144,233,254,204,225,61,183,22,182,20,194,40,251,44,5,43,103,154,118,42,190,4,195,170,68,19,38,73,134,6,153,156,66,80,244,145,239,152,122,51,84,11,67,237,207,172,98,228,179,28,169,201,8,232,149,128,223,148,250,117,143,63,166,71,7,167,252,243,115,23,186,131,89,60,25,230,133,79,168,104,107,129,178,113,100,218,139,248,235,15,75,112,86,157,53,30,36,14,94,99,88,209,162,37,34,124,59,1,33,120,135,212,0,70,87,159,211,39,82,76,54,2,231,160,196,200,158,234,191,138,210,64,199,56,181,163,247,242,206,249,97,21,161,224,174,93,164,155,52,26,85,173,147,50,48,245,140,177,227,29,246,226,46,130,102,202,96,192,41,35,171,13,83,78,111,213,219,55,69,222,253,142,47,3,255,106,114,109,108,91,81,141,27,175,146,187,221,188,127,17,217,92,65,31,16,90,216,10,193,49,136,165,205,123,189,45,116,208,18,184,229,180,176,137,105,151,74,12,150,119,126,101,185,241,9,197,110,198,132,24,240,125,236,58,220,77,32,121,238,95,62,215,203,57,72],C_=[462357,472066609,943670861,1415275113,1886879365,2358483617,2830087869,3301692121,3773296373,4228057617,404694573,876298825,1347903077,1819507329,2291111581,2762715833,3234320085,3705924337,4177462797,337322537,808926789,1280531041,1752135293,2223739545,2695343797,3166948049,3638552301,4110090761,269950501,741554753,1213159005,1684763257];function O_(e){const t=[];for(let n=0,o=e.length;n<o;n+=2)t.push(parseInt(e.substr(n,2),16));return t}function M_(e,t){const n=31&t;return e<<n|e>>>32-n}function A_(e){return(255&k_[e>>>24&255])<<24|(255&k_[e>>>16&255])<<16|(255&k_[e>>>8&255])<<8|255&k_[255&e]}function P_(e){return e^M_(e,2)^M_(e,10)^M_(e,18)^M_(e,24)}function I_(e){return e^M_(e,13)^M_(e,23)}function $_(e,t,n){const o=new Array(4),r=new Array(4);for(let i=0;i<4;i++)r[0]=255&e[4*i],r[1]=255&e[4*i+1],r[2]=255&e[4*i+2],r[3]=255&e[4*i+3],o[i]=r[0]<<24|r[1]<<16|r[2]<<8|r[3];for(let i,s=0;s<32;s+=4)i=o[1]^o[2]^o[3]^n[s+0],o[0]^=P_(A_(i)),i=o[2]^o[3]^o[0]^n[s+1],o[1]^=P_(A_(i)),i=o[3]^o[0]^o[1]^n[s+2],o[2]^=P_(A_(i)),i=o[0]^o[1]^o[2]^n[s+3],o[3]^=P_(A_(i));for(let i=0;i<16;i+=4)t[i]=o[3-i/4]>>>24&255,t[i+1]=o[3-i/4]>>>16&255,t[i+2]=o[3-i/4]>>>8&255,t[i+3]=255&o[3-i/4]}function D_(e,t,n,{padding:o="pkcs#7",mode:r,iv:i=[],output:s="string"}={}){if("cbc"===r&&("string"==typeof i&&(i=O_(i)),16!==i.length))throw new Error("iv is invalid");if("string"==typeof t&&(t=O_(t)),16!==t.length)throw new Error("key is invalid");if(e="string"==typeof e?0!==n?function(e){const t=[];for(let n=0,o=e.length;n<o;n++){const o=e.codePointAt(n);if(o<=127)t.push(o);else if(o<=2047)t.push(192|o>>>6),t.push(128|63&o);else if(o<=55295||o>=57344&&o<=65535)t.push(224|o>>>12),t.push(128|o>>>6&63),t.push(128|63&o);else{if(!(o>=65536&&o<=1114111))throw t.push(o),new Error("input is not supported");n++,t.push(240|o>>>18&28),t.push(128|o>>>12&63),t.push(128|o>>>6&63),t.push(128|63&o)}}return t}(e):O_(e):[...e],("pkcs#5"===o||"pkcs#7"===o)&&0!==n){const t=16-e.length%16;for(let n=0;n<t;n++)e.push(t)}const a=new Array(32);!function(e,t,n){const o=new Array(4),r=new Array(4);for(let i=0;i<4;i++)r[0]=255&e[0+4*i],r[1]=255&e[1+4*i],r[2]=255&e[2+4*i],r[3]=255&e[3+4*i],o[i]=r[0]<<24|r[1]<<16|r[2]<<8|r[3];o[0]^=2746333894,o[1]^=1453994832,o[2]^=1736282519,o[3]^=2993693404;for(let i,s=0;s<32;s+=4)i=o[1]^o[2]^o[3]^C_[s+0],t[s+0]=o[0]^=I_(A_(i)),i=o[2]^o[3]^o[0]^C_[s+1],t[s+1]=o[1]^=I_(A_(i)),i=o[3]^o[0]^o[1]^C_[s+2],t[s+2]=o[2]^=I_(A_(i)),i=o[0]^o[1]^o[2]^C_[s+3],t[s+3]=o[3]^=I_(A_(i));if(0===n)for(let i,s=0;s<16;s++)i=t[s],t[s]=t[31-s],t[31-s]=i}(t,a,n);const l=[];let c=i,u=e.length,f=0;for(;u>=16;){const t=e.slice(f,f+16),o=new Array(16);if("cbc"===r)for(let e=0;e<16;e++)0!==n&&(t[e]^=c[e]);$_(t,o,a);for(let e=0;e<16;e++)"cbc"===r&&0===n&&(o[e]^=c[e]),l[f+e]=o[e];"cbc"===r&&(c=0!==n?o:t),u-=16,f+=16}if(("pkcs#5"===o||"pkcs#7"===o)&&0===n){const e=l.length,t=l[e-1];for(let n=1;n<=t;n++)if(l[e-n]!==t)throw new Error("padding is invalid");l.splice(e-t,t)}return"array"!==s?0!==n?l.map((e=>1===(e=e.toString(16)).length?"0"+e:e)).join(""):function(e){const t=[];for(let n=0,o=e.length;n<o;n++)e[n]>=240&&e[n]<=247?(t.push(String.fromCodePoint(((7&e[n])<<18)+((63&e[n+1])<<12)+((63&e[n+2])<<6)+(63&e[n+3]))),n+=3):e[n]>=224&&e[n]<=239?(t.push(String.fromCodePoint(((15&e[n])<<12)+((63&e[n+1])<<6)+(63&e[n+2]))),n+=2):e[n]>=192&&e[n]<=223?(t.push(String.fromCodePoint(((31&e[n])<<6)+(63&e[n+1]))),n++):t.push(String.fromCodePoint(e[n]));return t.join("")}(l):l}var L_={encrypt:(e,t,n)=>D_(e,t,1,n),decrypt:(e,t,n)=>D_(e,t,0,n)},B_={sm2:w_,sm3:function(e,t){if(e="string"==typeof e?function(e){const t=[];for(let n=0,o=e.length;n<o;n++){const o=e.codePointAt(n);if(o<=127)t.push(o);else if(o<=2047)t.push(192|o>>>6),t.push(128|63&o);else if(o<=55295||o>=57344&&o<=65535)t.push(224|o>>>12),t.push(128|o>>>6&63),t.push(128|63&o);else{if(!(o>=65536&&o<=1114111))throw t.push(o),new Error("input is not supported");n++,t.push(240|o>>>18&28),t.push(128|o>>>12&63),t.push(128|o>>>6&63),t.push(128|63&o)}}return t}(e):Array.prototype.slice.call(e),t){if("hmac"!==(t.mode||"hmac"))throw new Error("invalid mode");let n=t.key;if(!n)throw new Error("invalid key");return n="string"==typeof n?E_(n):Array.prototype.slice.call(n),S_(T_(e,n))}return S_(x_(e))},sm4:L_};const F_=B_.sm2,R_=B_.sm3,N_=B_.sm4,j_="04298364ec840088475eae92a591e01284d1abefcda348b47eb324bb521bb03b0b2a5bc393f6b71dabb8f15c99a0050818b56b23f31743b93df9cf8948f15ddb54",q_="3037723d47292171677ec8bd7dc9af696c7472bc5f251b2cec07e65fdef22e25",z_="0123456789abcdeffedcba9876543210",H_={doSm2Encrypt:e=>F_.doEncrypt(e,j_,1),doSm2Decrypt:e=>F_.doDecrypt(e,q_,1),doSm2ArrayEncrypt:e=>F_.doEncrypt(e,j_,1),doSm2ArrayDecrypt:e=>F_.doDecrypt(e,q_,1,{output:"array"}),doSm3Hash:e=>R_(e),doSm4Encrypt:e=>N_.encrypt(e,z_),doSm4CbcEncrypt:e=>N_.encrypt(e,z_,{mode:"cbc",iv:"fedcba98765432100123456789abcdef"}),doSm4Decrypt:e=>N_.decrypt(e,z_),doSm4CbcDecrypt:e=>N_.decrypt(e,z_,{mode:"cbc",iv:"fedcba98765432100123456789abcdef"})},V_={template:"",title:"Snowy-Mobile",router:{mode:"history",base:""},devServer:{port:8081,https:!1},optimization:{treeShaking:{enable:!0}}};function W_(e,t){const{search:n,searchQuery:o,hashQuery:r}=uni.$xeu.parseUrl(location.href);return uni.$xeu.isEmpty(t)||t==gb.NO_TOKEN_BACK_URL||-1!=n.indexOf("redirect=")?e+`${n}`:(e+=`?redirect=${t}`,uni.$xeu.isEmpty(o)||(e+=`&${uni.$xeu.serialize(o)}`),uni.$xeu.isEmpty(r)||(e+=`&${uni.$xeu.serialize(r)}`),e)}function U_(){const{hashKey:e,pathname:t}=uni.$xeu.parseUrl(location.href);return uni.$xeu.isEmpty(e)?V_.router.base.length>0?t.substring(V_.router.base.length-1):t:e}function Y_(){return rm("App-Token")}function X_(e){if(Y_()){if(gb.NO_TOKEN_WHITE_LIST.includes(e)||gb.HAS_TOKEN_WHITE_LIST.includes(e))return!0;return!!uni.$xeu.findTree(dw.getters.userMobileMenus,(t=>{if("MENU"===t.category&&"MENU"===t.menuType){const n=t.path;if("NO"===t.regType)return e===n;if("YES"===t.regType){return new RegExp("^"+n.substr(0,n.lastIndexOf("/")+1)).test(e)}}}))||(uni.$snowy.modal.alert("页面【"+e+"】需要进行授权，才能进行访问！"),Dm({url:gb.HAS_TOKEN_BACK_URL}),!1)}return!!gb.NO_TOKEN_WHITE_LIST.includes(e)||(uni.$snowy.modal.alert("页面【"+e+"】需要进行登录，才能进行访问！"),Dm({url:W_(gb.NO_TOKEN_BACK_URL,e)}),!1)}const K_=[401,1011007,1011008],G_={400:"发出的请求有错误，服务器没有进行新建或修改数据的操作。",401:"用户没有权限（令牌、用户名、密码错误）。",403:"用户得到授权，但是访问是被禁止的。",404:"发出的请求针对的是不存在的记录，服务器没有进行操作。",406:"请求的格式不可得。",410:"请求的资源被永久删除，且不会再得到的。",422:"当创建一个对象时，发生一个验证错误。",500:"服务器发生错误，请检查服务器。",502:"网关错误。",503:"服务不可用，服务器暂时过载或维护。",504:"网关超时。",default:"系统未知错误，请反馈给管理员"},Z_=e=>e,{TIMEOUT:J_,TOKEN_NAME:Q_,TOKEN_PREFIX:ew,NO_TOKEN_BACK_URL:tw}=gb,nw=e=>{e.url=e.url;const t=!1===(e.extConf||{}).isToken;if(e.header=e.header||{},Y_()&&!t&&(e.header[Q_]=ew+Y_()),e.header.Domain=dw.getters.allEnv[dw.getters.envKey].tenantDomain,e.params){let t=e.url+"?"+function(e){let t="";for(const o of Object.keys(e)){const r=e[o];var n=encodeURIComponent(o)+"=";if(null!==r&&""!==r&&void 0!==r)if("object"==typeof r)for(const e of Object.keys(r))null!==r[e]&&""!==r[e]&&void 0!==r[e]&&(t+=encodeURIComponent(o+"["+e+"]")+"="+encodeURIComponent(r[e])+"&");else t+=n+encodeURIComponent(r)+"&"}return t}(e.params);t=t.slice(0,-1),e.url=t}return new Promise(((t,n)=>{uni.$snowy.modal.loading("努力加载中"),Tm({method:e.method||"get",timeout:e.timeout||J_,url:(e.baseUrl||dw.getters.allEnv[dw.getters.envKey].baseUrl)+e.url,data:e.data,header:e.header,dataType:"json"}).then((e=>{const o=e.data.code||200,r=e.data.msg||G_[o]||G_.default;K_.includes(o)?(uni.$snowy.modal.confirm(r||"登录状态已过期，您可以清除缓存，重新进行登录?").then((()=>{dw.commit("CLEAR_cache"),uni.$snowy.tab.reLaunch(W_(tw,U_()))})),n("无效的会话，或者会话已过期，请重新登录。")):200!==o&&(uni.$snowy.modal.alert(r),n(o)),t(e.data)})).catch((e=>{let{errMsg:t}=e;"Network Error"===t?t="后端接口连接异常":t.includes("timeout")?t="系统接口请求超时":t.includes("Request failed with status code")?t="系统接口"+t.substr(t.length-3)+"异常":t.includes("request:fail")&&(t="请求失败"),uni.$snowy.modal.alert(t),n(e)})).finally((()=>{uni.$snowy.modal.closeLoading()}))}))};function ow(){return nw({url:"/auth/b/getPicCaptcha",extConf:{isToken:!1},method:"get",timeout:2e4})}function rw(e){return nw({url:"/sys/userCenter/updateUserInfo",method:"post",data:e})}function iw(e){return nw({url:"/sys/userCenter/updatePassword",method:"post",data:e})}function sw(e){return nw({url:"/sys/userCenter/loginUnreadMessagePage",method:"get",data:e})}function aw(e){return nw({url:"/sys/userCenter/loginUnreadMessageDetail",method:"get",data:e})}function lw(e){return nw({url:"/sys/userCenter/getPositionListByIdList",method:"post",data:e})}function cw(e){return nw({url:"/sys/userCenter/getUserListByIdList",method:"post",data:e})}const uw={DEFAULT_ENV_KEY:"local",DEFAULT_ALL_ENV:{local:{name:"本地环境",baseUrl:"http://*************:82",tenantDomain:"http://localhost:81"},pro:{name:"生产环境",baseUrl:"https://snowyapi.xiaonuo.vip",tenantDomain:"https://snowy.xiaonuo.vip"}}},fw={state:{envKey:kb(mb)||uw.DEFAULT_ENV_KEY,allEnv:kb(vb)||uw.DEFAULT_ALL_ENV,token:Y_(),homeConfigs:kb(bb)||gb.HOME_CONFIGS,userMobileMenus:kb(wb),userInfo:kb(_b),sysBaseConfig:kb(yb)||gb.SYS_BASE_CONFIG,dictTypeTreeData:kb(xb)},mutations:{SET_envKey:(e,t)=>{e.envKey=t,Eb(mb,t)},SET_allEnv:(e,t)=>{e.allEnv=t,Eb(vb,t)},SET_token:(e,t)=>{e.token=t,function(e){nm("App-Token",e)}(t)},SET_homeConfigs:(e,t)=>{e.homeConfigs=t,Eb(bb,t)},SET_userMobileMenus:(e,t)=>{e.userMobileMenus=t,Eb(wb,t)},SET_userInfo:(e,t)=>{e.userInfo=t,Eb(_b,t)},SET_sysBaseConfig:(e,t)=>{e.sysBaseConfig=t,Eb(yb,t)},SET_dictTypeTreeData:(e,t)=>{e.dictTypeTreeData=t,Eb(xb,t)},CLEAR_cache:e=>{e.token="",im("App-Token"),e.userMobileMenus={},Cb(wb),e.userInfo={},Cb(_b),e.dictTypeTreeData={},Cb(xb)}},actions:{Login({commit:e},t){const n={account:t.account.trim(),password:H_.doSm2Encrypt(t.password),validCode:t.validCode,validCodeReqNo:t.validCodeReqNo};return new Promise(((t,o)=>{var r;(r=n,nw({url:"/auth/b/doLogin",extConf:{isToken:!1},method:"post",data:r})).then((n=>{e("SET_token",n.data),t(n.data)})).catch((e=>{o(e)}))}))},GetUserInfo:({commit:e,state:t})=>new Promise(((t,n)=>{nw({url:"/auth/b/getLoginUser",method:"get"}).then((n=>{e("SET_userInfo",n.data),t(n.data)})).catch((e=>{n(e)}))})),GetUserLoginMenu:({commit:e,state:t})=>new Promise(((t,n)=>{nw({url:"/sys/userCenter/loginMobileMenu",method:"get"}).then((n=>{e("SET_userMobileMenus",n.data),t(n.data)})).catch((e=>{n(e)}))})),GetDictTypeTreeData:({commit:e,state:t})=>new Promise(((t,n)=>{var o;nw({url:"/dev/dict/tree",method:"get",data:o}).then((n=>{n.data&&(e("SET_dictTypeTreeData",n.data),t(n.data))})).catch((e=>{n(e)}))})),GetSysBaseConfig:({commit:e,state:t})=>new Promise(((t,n)=>{let o={};nw({url:"/dev/config/sysBaseList",extConf:{isToken:!1},method:"get",timeout:2e4}).then((n=>{n.data&&(n.data.forEach((e=>{o[e.configKey]=e.configValue})),e("SET_sysBaseConfig",o)),t(o)}))})).catch((e=>{reject(e)})),LogOut:({commit:e,state:t,dispatch:n})=>new Promise(((t,n)=>{nw({url:"/auth/b/doLogout",method:"get"}).then((()=>{e("CLEAR_cache"),t()})).catch((e=>{n(e)}))}))}},dw=new hb({modules:{global:fw},getters:{envKey:e=>e.global.envKey,allEnv:e=>e.global.allEnv,homeConfigs:e=>e.global.homeConfigs,token:e=>e.global.token,userMobileMenus:e=>e.global.userMobileMenus,userInfo:e=>e.global.userInfo,sysBaseConfig:e=>e.global.sysBaseConfig,dictTypeTreeData:e=>e.global.dictTypeTreeData}});const hw={__name:"App",setup:e=>(X_(U_()),dw.dispatch("GetSysBaseConfig"),Ny((()=>{})),()=>{})};Pg(hw,{init:Mg,setup(e){const t=Wp(),n=()=>{var n;n=e,Object.keys(yd).forEach((e=>{yd[e].forEach((t=>{Co(e,t,n)}))}));const{onLaunch:o,onShow:r,onPageNotFound:i,onError:s}=e,a=function({path:e,query:t}){return T(rh,{path:e,query:t}),T(ih,rh),T({},rh)}({path:t.path.slice(1)||__uniRoutes[0].meta.route,query:be(t.query)});if(o&&K(o,a),r&&K(r,a),!t.matched.length){const e={notFound:!0,openType:"appLaunch",path:t.path,query:{},scene:1001};i&&K(i,e)}s&&(e.appContext.config.errorHandler=e=>{K(s,e)})};return Wn(Qa).isReady().then(n),Ao((()=>{window.addEventListener("resize",xe($g,50,{setTimeout:setTimeout,clearTimeout:clearTimeout})),window.addEventListener("message",Dg),document.addEventListener("visibilitychange",Lg),function(){let e=null;try{e=window.matchMedia("(prefers-color-scheme: dark)")}catch(t){}if(e){let t=e=>{Pv.emit("onThemeChange",{theme:e.matches?"dark":"light"})};e.addEventListener?e.addEventListener("change",t):e.addListener(t)}}()})),t.query},before(e){e.mpType="app";const{setup:t}=e,n=()=>(Fr(),zr(_v));e.setup=(e,o)=>{const r=t&&t(e,o);return P(r)?n:r},e.render=n}});["navigateTo","redirectTo","reLaunch","switchTab"].forEach((e=>{Of(e,{invoke:e=>X_(e.url.split("?")[0]),fail(e){console.log(e)}})}));const{toString:pw}=Object.prototype;function gw(e){return"[object Array]"===pw.call(e)}function mw(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),gw(e))for(let n=0,o=e.length;n<o;n++)t.call(null,e[n],n,e);else for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.call(null,e[n],n,e)}function vw(){const e={};function t(t,n){"object"==typeof e[n]&&"object"==typeof t?e[n]=vw(e[n],t):e[n]="object"==typeof t?vw({},t):t}for(let n=0,o=arguments.length;n<o;n++)mw(arguments[n],t);return e}function yw(e){return void 0===e}function bw(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function _w(e,t){if(!t)return e;let n;if(o=t,"undefined"!=typeof URLSearchParams&&o instanceof URLSearchParams)n=t.toString();else{const e=[];mw(t,((t,n)=>{null!=t&&(gw(t)?n=`${n}[]`:t=[t],mw(t,(t=>{!function(e){return"[object Date]"===pw.call(e)}(t)?function(e){return null!==e&&"object"==typeof e}(t)&&(t=JSON.stringify(t)):t=t.toISOString(),e.push(`${bw(n)}=${bw(t)}`)})))})),n=e.join("&")}var o;if(n){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e}const ww=(e,t)=>{const n={};return e.forEach((e=>{yw(t[e])||(n[e]=t[e])})),n},xw=e=>(e=>new Promise(((t,n)=>{const o=_w((r=e.baseURL,i=e.url,r&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(i)?function(e,t){return t?`${e.replace(/\/+$/,"")}/${t.replace(/^\/+/,"")}`:e}(r,i):i),e.params);var r,i;const s={url:o,header:e.header,complete:r=>{e.fullPath=o,r.config=e;try{"string"==typeof r.data&&(r.data=JSON.parse(r.data))}catch(i){}!function(e,t,n){const{validateStatus:o}=n.config,r=n.statusCode;!r||o&&!o(r)?t(n):e(n)}(t,n,r)}};let a;if("UPLOAD"===e.method){delete s.header["content-type"],delete s.header["Content-Type"];const t={filePath:e.filePath,name:e.name},n=["files","file","timeout","formData"];a=Mm({...s,...t,...ww(n,e)})}else if("DOWNLOAD"===e.method)yw(e.timeout)||(s.timeout=e.timeout),a=Cm(s);else{const t=["data","method","timeout","dataType","responseType","withCredentials"];a=Tm({...s,...ww(t,e)})}e.getTask&&e.getTask(a,e)})))(e);function Tw(){this.handlers=[]}Tw.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},Tw.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},Tw.prototype.forEach=function(e){this.handlers.forEach((t=>{null!==t&&e(t)}))};const Sw=(e,t,n)=>{const o={};return e.forEach((e=>{yw(n[e])?yw(t[e])||(o[e]=t[e]):o[e]=n[e]})),o},Ew={baseURL:"",header:{},method:"GET",dataType:"json",responseType:"text",custom:{},timeout:6e4,withCredentials:!1,validateStatus:function(e){return e>=200&&e<300}};var kw=function(){function e(e,t){return null!=t&&e instanceof t}var t,n,o;try{t=Map}catch(a){t=function(){}}try{n=Set}catch(a){n=function(){}}try{o=Promise}catch(a){o=function(){}}function r(i,a,l,c,u){"object"==typeof a&&(l=a.depth,c=a.prototype,u=a.includeNonEnumerable,a=a.circular);var f=[],d=[],h="undefined"!=typeof Buffer;return void 0===a&&(a=!0),void 0===l&&(l=1/0),function i(l,p){if(null===l)return null;if(0===p)return l;var g,m;if("object"!=typeof l)return l;if(e(l,t))g=new t;else if(e(l,n))g=new n;else if(e(l,o))g=new o((function(e,t){l.then((function(t){e(i(t,p-1))}),(function(e){t(i(e,p-1))}))}));else if(r.__isArray(l))g=[];else if(r.__isRegExp(l))g=new RegExp(l.source,s(l)),l.lastIndex&&(g.lastIndex=l.lastIndex);else if(r.__isDate(l))g=new Date(l.getTime());else{if(h&&Buffer.isBuffer(l))return Buffer.from?g=Buffer.from(l):(g=new Buffer(l.length),l.copy(g)),g;e(l,Error)?g=Object.create(l):void 0===c?(m=Object.getPrototypeOf(l),g=Object.create(m)):(g=Object.create(c),m=c)}if(a){var v=f.indexOf(l);if(-1!=v)return d[v];f.push(l),d.push(g)}for(var y in e(l,t)&&l.forEach((function(e,t){var n=i(t,p-1),o=i(e,p-1);g.set(n,o)})),e(l,n)&&l.forEach((function(e){var t=i(e,p-1);g.add(t)})),l){Object.getOwnPropertyDescriptor(l,y)&&(g[y]=i(l[y],p-1));try{if("undefined"===Object.getOwnPropertyDescriptor(l,y).set)continue;g[y]=i(l[y],p-1)}catch(S){if(S instanceof TypeError)continue;if(S instanceof ReferenceError)continue}}if(Object.getOwnPropertySymbols){var b=Object.getOwnPropertySymbols(l);for(y=0;y<b.length;y++){var _=b[y];(!(x=Object.getOwnPropertyDescriptor(l,_))||x.enumerable||u)&&(g[_]=i(l[_],p-1),Object.defineProperty(g,_,x))}}if(u){var w=Object.getOwnPropertyNames(l);for(y=0;y<w.length;y++){var x,T=w[y];(x=Object.getOwnPropertyDescriptor(l,T))&&x.enumerable||(g[T]=i(l[T],p-1),Object.defineProperty(g,T,x))}}return g}(i,l)}function i(e){return Object.prototype.toString.call(e)}function s(e){var t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),t}return r.clonePrototype=function(e){if(null===e)return null;var t=function(){};return t.prototype=e,new t},r.__objToStr=i,r.__isDate=function(e){return"object"==typeof e&&"[object Date]"===i(e)},r.__isArray=function(e){return"object"==typeof e&&"[object Array]"===i(e)},r.__isRegExp=function(e){return"object"==typeof e&&"[object RegExp]"===i(e)},r.__getRegExpFlags=s,r}();function Cw(e){return/^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(e)}function Ow(e){switch(typeof e){case"undefined":return!0;case"string":if(0==e.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g,"").length)return!0;break;case"boolean":if(!e)return!0;break;case"number":if(0===e||isNaN(e))return!0;break;case"object":if(null===e||0===e.length)return!0;for(const t in e)return!1;return!0}return!1}function Mw(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e)}function Aw(e){return"[object Object]"===Object.prototype.toString.call(e)}function Pw(e){return"function"==typeof e}function Iw(e){const t=e.split("?")[0];return/\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i.test(t)}const $w=Object.freeze(Object.defineProperty({__proto__:null,amount:function(e){return/^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(e)},array:Mw,carNo:function(e){const t=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/,n=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;return 7===e.length?n.test(e):8===e.length&&t.test(e)},chinese:function(e){return/^[\u4e00-\u9fa5]+$/gi.test(e)},code:function(e,t=6){return new RegExp(`^\\d{${t}}$`).test(e)},contains:function(e,t){return e.indexOf(t)>=0},date:function(e){return!!e&&(Cw(e)&&(e=+e),!/Invalid|NaN/.test(new Date(e).toString()))},dateISO:function(e){return/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(e)},digits:function(e){return/^\d+$/.test(e)},email:function(e){return/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(e)},empty:Ow,enOrNum:function(e){return/^[0-9a-zA-Z]*$/g.test(e)},func:Pw,idCard:function(e){return/^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(e)},image:Iw,jsonString:function(e){if("string"==typeof e)try{const t=JSON.parse(e);return!("object"!=typeof t||!t)}catch(t){return!1}return!1},landline:function(e){return/^\d{3,4}-\d{7,8}(-\d{3,4})?$/.test(e)},letter:function(e){return/^[a-zA-Z]*$/.test(e)},mobile:function(e){return/^1([3589]\d|4[5-9]|6[1-2,4-7]|7[0-8])\d{8}$/.test(e)},number:Cw,object:Aw,promise:function(e){return Aw(e)&&Pw(e.then)&&Pw(e.catch)},range:function(e,t){return e>=t[0]&&e<=t[1]},rangeLength:function(e,t){return e.length>=t[0]&&e.length<=t[1]},regExp:function(e){return e&&"[object RegExp]"===Object.prototype.toString.call(e)},string:function(e){return"string"==typeof e},url:function(e){return/^((https|http|ftp|rtsp|mms):\/\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\/?)|(\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\/?)$/.test(e)},video:function(e){return/\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8)/i.test(e)}},Symbol.toStringTag,{value:"Module"}));function Dw(e,t=15){return+parseFloat(Number(e).toPrecision(t))}function Lw(e){const t=e.toString().split(/[eE]/),n=(t[0].split(".")[1]||"").length-+(t[1]||0);return n>0?n:0}function Bw(e){if(-1===e.toString().indexOf("e"))return Number(e.toString().replace(".",""));const t=Lw(e);return t>0?Dw(Number(e)*Math.pow(10,t)):Number(e)}function Fw(e){(e>Number.MAX_SAFE_INTEGER||e<Number.MIN_SAFE_INTEGER)&&console.warn(`${e} 超出了精度限制，结果可能不正确`)}function Rw(e,t){const[n,o,...r]=e;let i=t(n,o);return r.forEach((e=>{i=t(i,e)})),i}function Nw(...e){if(e.length>2)return Rw(e,Nw);const[t,n]=e,o=Bw(t),r=Bw(n),i=Lw(t)+Lw(n),s=o*r;return Fw(s),s/Math.pow(10,i)}function jw(...e){if(e.length>2)return Rw(e,jw);const[t,n]=e,o=Bw(t),r=Bw(n);return Fw(o),Fw(r),Nw(o/r,Dw(Math.pow(10,Lw(n)-Lw(t))))}function qw(e){let t=this.$parent;for(;t;){if(!t.$options||t.$options.name===e)return t;t=t.$parent}return!1}function zw(e,t=new WeakMap){if(null===e||"object"!=typeof e)return e;if(t.has(e))return t.get(e);let n;if(e instanceof Date)n=new Date(e.getTime());else if(e instanceof RegExp)n=new RegExp(e);else if(e instanceof Map)n=new Map(Array.from(e,(([e,n])=>[e,zw(n,t)])));else if(e instanceof Set)n=new Set(Array.from(e,(e=>zw(e,t))));else if(Array.isArray(e))n=e.map((e=>zw(e,t)));else if("[object Object]"===Object.prototype.toString.call(e)){n=Object.create(Object.getPrototypeOf(e)),t.set(e,n);for(const[o,r]of Object.entries(e))n[o]=zw(r,t)}else n=Object.assign({},e);return t.set(e,n),n}function Hw(e={},t={}){if("object"!=typeof(e=zw(e))||null===e||"object"!=typeof t||null===t)return e;const n=Array.isArray(e)?e.slice():Object.assign({},e);for(const o in t){if(!t.hasOwnProperty(o))continue;const e=t[o],r=n[o];e instanceof Date?n[o]=new Date(e):e instanceof RegExp?n[o]=new RegExp(e):e instanceof Map?n[o]=new Map(e):e instanceof Set?n[o]=new Set(e):n[o]="object"==typeof e&&null!==e?Hw(r,e):e}return n}function Vw(e=null,t="yyyy-mm-dd"){let n;n=e?/^\d{10}$/.test(null==e?void 0:e.toString().trim())?new Date(1e3*e):"string"==typeof e&&/^\d+$/.test(e.trim())?new Date(Number(e)):"string"==typeof e&&e.includes("-")&&!e.includes("T")?new Date(e.replace(/-/g,"/")):new Date(e):new Date;const o={y:n.getFullYear().toString(),m:(n.getMonth()+1).toString().padStart(2,"0"),d:n.getDate().toString().padStart(2,"0"),h:n.getHours().toString().padStart(2,"0"),M:n.getMinutes().toString().padStart(2,"0"),s:n.getSeconds().toString().padStart(2,"0")};for(const r in o){const[e]=new RegExp(`${r}+`).exec(t)||[];if(e){const n="y"===r&&2===e.length?2:0;t=t.replace(e,o[r].slice(n))}}return t}function Ww(e,t="both"){return e=String(e),"both"==t?e.replace(/^\s+|\s+$/g,""):"left"==t?e.replace(/^\s*/,""):"right"==t?e.replace(/(\s*$)/g,""):"all"==t?e.replace(/\s+/g,""):e}function Uw(e={},t=!0,n="brackets"){const o=t?"?":"",r=[];-1==["indices","brackets","repeat","comma"].indexOf(n)&&(n="brackets");for(const i in e){const t=e[i];if(!(["",void 0,null].indexOf(t)>=0))if(t.constructor===Array)switch(n){case"indices":for(let n=0;n<t.length;n++)r.push(`${i}[${n}]=${t[n]}`);break;case"brackets":default:t.forEach((e=>{r.push(`${i}[]=${e}`)}));break;case"repeat":t.forEach((e=>{r.push(`${i}=${e}`)}));break;case"comma":let e="";t.forEach((t=>{e+=(e?",":"")+t})),r.push(`${i}=${e}`)}else r.push(`${i}=${t}`)}return r.length?o+r.join("&"):""}function Yw(){var e;const t=rg(),n=null==(e=t[t.length-1])?void 0:e.route;return`/${n||""}`}String.prototype.padStart||(String.prototype.padStart=function(e,t=" "){if("[object String]"!==Object.prototype.toString.call(t))throw new TypeError("fillString must be String");const n=this;if(n.length>=e)return String(n);const o=e-n.length;let r=Math.ceil(o/t.length);for(;r>>=1;)t+=t,1===r&&(t+=t);return t.slice(0,o)+n});const Xw=Object.freeze(Object.defineProperty({__proto__:null,$parent:qw,addStyle:function(e,t="object"){if(Ow(e)||"object"==typeof e&&"object"===t||"string"===t&&"string"==typeof e)return e;if("object"===t){const t=(e=Ww(e)).split(";"),n={};for(let e=0;e<t.length;e++)if(t[e]){const o=t[e].split(":");n[Ww(o[0])]=Ww(o[1])}return n}let n="";for(const o in e){n+=`${o.replace(/([A-Z])/g,"-$1").toLowerCase()}:${e[o]};`}return Ww(n)},addUnit:function(e="auto",t=((e=>{return null==(e=null==(t=null==uni?void 0:uni.$uv)?void 0:t.config)?void 0:e.unit;var t})()?(e=>{return null==(e=null==(t=null==uni?void 0:uni.$uv)?void 0:t.config)?void 0:e.unit;var t})():"px")){return Cw(e=String(e))?`${e}${t}`:e},deepClone:zw,deepMerge:Hw,error:function(e){},formValidate:function(e,t){const n=qw.call(e,"uv-form-item"),o=qw.call(e,"uv-form");n&&o&&o.validateField(n.prop,(()=>{}),t)},getDuration:function(e,t=!0){const n=parseInt(e);return t?/s$/.test(e)?e:e>30?`${e}ms`:`${e}s`:/ms$/.test(e)?n:/s$/.test(e)?n>30?n:1e3*n:n},getHistoryPage:function(e=0){const t=rg();return t[t.length-1+e]},getProperty:function(e,t){if(e){if("string"!=typeof t||""===t)return"";if(-1!==t.indexOf(".")){const n=t.split(".");let o=e[n[0]]||{};for(let e=1;e<n.length;e++)o&&(o=o[n[e]]);return o}return e[t]}},getPx:function(e,t=!1){return Cw(e)?t?`${e}px`:Number(e):/(rpx|upx)$/.test(e)?t?`${kf(parseInt(e))}px`:Number(kf(parseInt(e))):t?`${parseInt(e)}px`:parseInt(e)},guid:function(e=32,t=!0,n=null){const o="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),r=[];if(n=n||o.length,e)for(let i=0;i<e;i++)r[i]=o[0|Math.random()*n];else{let e;r[8]=r[13]=r[18]=r[23]="-",r[14]="4";for(let t=0;t<36;t++)r[t]||(e=0|16*Math.random(),r[t]=o[19==t?3&e|8:e])}return t?(r.shift(),`u${r.join("")}`):r.join("")},os:function(){return em().platform.toLowerCase()},padZero:function(e){return`00${e}`.slice(-2)},page:Yw,pages:function(){return rg()},priceFormat:function(e,t=0,n=".",o=","){e=`${e}`.replace(/[^0-9+-Ee.]/g,"");const r=isFinite(+e)?+e:0,i=isFinite(+t)?Math.abs(t):0,s=void 0===o?",":o,a=void 0===n?".":n;let l="";l=(i?function(e,t){const n=Math.pow(10,t);let o=jw(Math.round(Math.abs(Nw(e,n))),n);return e<0&&0!==o&&(o=Nw(o,-1)),o}(r,i)+"":`${Math.round(r)}`).split(".");const c=/(-?\d+)(\d{3})/;for(;c.test(l[0]);)l[0]=l[0].replace(c,`$1${s}$2`);return(l[1]||"").length<i&&(l[1]=l[1]||"",l[1]+=new Array(i-l[1].length+1).join("0")),l.join(a)},queryParams:Uw,random:function(e,t){if(e>=0&&t>0&&t>=e){const n=t-e+1;return Math.floor(Math.random()*n+e)}return 0},randomArray:function(e=[]){return e.sort((()=>Math.random()-.5))},range:function(e=0,t=0,n=0){return Math.max(e,Math.min(t,Number(n)))},setConfig:function({props:e={},config:t={},color:n={},zIndex:o={}}){const{deepMerge:r}=uni.$uv;uni.$uv.config=r(uni.$uv.config,t),uni.$uv.props=r(uni.$uv.props,e),uni.$uv.color=r(uni.$uv.color,n),uni.$uv.zIndex=r(uni.$uv.zIndex,o)},setProperty:function(e,t,n){if(!e)return;const o=function(e,t,n){if(1!==t.length)for(;t.length>1;){const r=t[0];e[r]&&"object"==typeof e[r]||(e[r]={}),t.shift(),o(e[r],t,n)}else e[t[0]]=n};if("string"!=typeof t||""===t);else if(-1!==t.indexOf(".")){const r=t.split(".");o(e,r,n)}else e[t]=n},sleep:function(e=30){return new Promise((t=>{setTimeout((()=>{t()}),e)}))},sys:function(){return em()},timeFormat:Vw,timeFrom:function(e=null,t="yyyy-mm-dd"){null==e&&(e=Number(new Date)),10==(e=parseInt(e)).toString().length&&(e*=1e3);let n=(new Date).getTime()-e;n=parseInt(n/1e3);let o="";switch(!0){case n<300:o="刚刚";break;case n>=300&&n<3600:o=`${parseInt(n/60)}分钟前`;break;case n>=3600&&n<86400:o=`${parseInt(n/3600)}小时前`;break;case n>=86400&&n<2592e3:o=`${parseInt(n/86400)}天前`;break;default:o=!1===t?n>=2592e3&&n<31536e3?`${parseInt(n/2592e3)}个月前`:`${parseInt(n/31536e3)}年前`:Vw(e,t)}return o},toast:function(e,t=2e3){nv({title:String(e),icon:"none",duration:t})},trim:Ww,type2icon:function(e="success",t=!1){-1==["primary","info","error","warning","success"].indexOf(e)&&(e="success");let n="";switch(e){case"primary":case"info":n="info-circle";break;case"error":n="close-circle";break;case"warning":n="error-circle";break;default:n="checkmark-circle"}return t&&(n+="-fill"),n}},Symbol.toStringTag,{value:"Module"})),Kw={props:{customStyle:{type:[Object,String],default:()=>({})},customClass:{type:String,default:""},url:{type:String,default:""},linkType:{type:String,default:"navigateTo"}},data:()=>({}),onLoad(){this.$uv.getRect=this.$uvGetRect},created(){this.$uv.getRect=this.$uvGetRect},computed:{$uv(){var e,t;return{...Xw,test:$w,unit:null==(t=null==(e=null==uni?void 0:uni.$uv)?void 0:e.config)?void 0:t.unit}},bem:()=>function(e,t,n){const o=`uv-${e}--`,r={};return t&&t.map((e=>{r[o+this[e]]=!0})),n&&n.map((e=>{this[e]?r[o+e]=this[e]:delete r[o+e]})),Object.keys(r)}},methods:{openPage(e="url"){const t=this[e];t&&uni[this.linkType]({url:t})},$uvGetRect(e,t){return new Promise((n=>{cd().in(this)[t?"selectAll":"select"](e).boundingClientRect((e=>{t&&Array.isArray(e)&&e.length&&n(e),!t&&e&&n(e)})).exec()}))},getParentData(e=""){this.parent||(this.parent={}),this.parent=this.$uv.$parent.call(this,e),this.parent.children&&-1===this.parent.children.indexOf(this)&&this.parent.children.push(this),this.parent&&this.parentData&&Object.keys(this.parentData).map((e=>{this.parentData[e]=this.parent[e]}))},preventEvent(e){e&&"function"==typeof e.stopPropagation&&e.stopPropagation()},noop(e){this.preventEvent(e)}},onReachBottom(){$f("uvOnReachBottom")},beforeDestroy(){if(this.parent&&Mw(this.parent.children)){const e=this.parent.children;e.map(((t,n)=>{t===this&&e.splice(n,1)}))}}},Gw={};const Zw=(new class{constructor(){this.config={type:"navigateTo",url:"",delta:1,params:{},animationType:"pop-in",animationDuration:300,intercept:!1,events:{}},this.route=this.route.bind(this)}addRootPath(e){return"/"===e[0]?e:`/${e}`}mixinParam(e,t){e=e&&this.addRootPath(e);let n="";return/.*\/.*\?.*=.*/.test(e)?(n=Uw(t,!1),e+`&${n}`):(n=Uw(t),e+n)}async route(e={},t={}){let n={};if("string"==typeof e?(n.url=this.mixinParam(e,t),n.type="navigateTo"):(n=Hw(this.config,e),n.url=this.mixinParam(e.url,e.params)),n.url!==Yw())if(t.intercept&&(n.intercept=t.intercept),n.params=t,n=Hw(this.config,n),"function"==typeof n.intercept){await new Promise(((e,t)=>{n.intercept(n,e)}))&&this.openPage(n)}else this.openPage(n)}openPage(e){const{url:t,type:n,delta:o,animationType:r,animationDuration:i,events:s}=e;"navigateTo"!=e.type&&"to"!=e.type||Im({url:t,animationType:r,animationDuration:i,events:s}),"redirectTo"!=e.type&&"redirect"!=e.type||$m({url:t}),"switchTab"!=e.type&&"tab"!=e.type||Bm({url:t}),"reLaunch"!=e.type&&"launch"!=e.type||Dm({url:t}),"navigateBack"!=e.type&&"back"!=e.type||Am({delta:o})}}).route;let Jw,Qw=null;function ex(e="rgb(0, 0, 0)",t="rgb(255, 255, 255)",n=10){const o=tx(e,!1),r=o[0],i=o[1],s=o[2],a=tx(t,!1),l=(a[0]-r)/n,c=(a[1]-i)/n,u=(a[2]-s)/n,f=[];for(let d=0;d<n;d++){let o=nx(`rgb(${Math.round(l*d+r)},${Math.round(c*d+i)},${Math.round(u*d+s)})`);0===d&&(o=nx(e)),d===n-1&&(o=nx(t)),f.push(o)}return f}function tx(e,t=!0){if((e=String(e).toLowerCase())&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(e)){if(4===e.length){let t="#";for(let n=1;n<4;n+=1)t+=e.slice(n,n+1).concat(e.slice(n,n+1));e=t}const n=[];for(let t=1;t<7;t+=2)n.push(parseInt(`0x${e.slice(t,t+2)}`));return t?`rgb(${n[0]},${n[1]},${n[2]})`:n}if(/^(rgb|RGB)/.test(e)){return e.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",").map((e=>Number(e)))}return e}function nx(e){const t=e;if(/^(rgb|RGB)/.test(t)){const e=t.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",");let n="#";for(let t=0;t<e.length;t++){let o=Number(e[t]).toString(16);o=1==String(o).length?`0${o}`:o,"0"===o&&(o+=o),n+=o}return 7!==n.length&&(n=t),n}if(!/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(t))return t;{const e=t.replace(/#/,"").split("");if(6===e.length)return t;if(3===e.length){let t="#";for(let n=0;n<e.length;n+=1)t+=e[n]+e[n];return t}}}let ox="none";ox="vue3",ox="h5";const rx={route:Zw,config:{v:"1.1.14",version:"1.1.14",type:["primary","success","info","error","warning"],color:{"uv-primary":"#2979ff","uv-warning":"#ff9900","uv-success":"#19be6b","uv-error":"#fa3534","uv-info":"#909399","uv-main-color":"#303133","uv-content-color":"#606266","uv-tips-color":"#909399","uv-light-color":"#c0c4cc"},unit:"px"},test:$w,date:Vw,...Xw,colorGradient:ex,hexToRgb:tx,rgbToHex:nx,colorToRgba:function(e,t){e=nx(e);let n=String(e).toLowerCase();if(n&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(n)){if(4===n.length){let e="#";for(let t=1;t<4;t+=1)e+=n.slice(t,t+1).concat(n.slice(t,t+1));n=e}const e=[];for(let t=1;t<7;t+=2)e.push(parseInt(`0x${n.slice(t,t+2)}`));return`rgba(${e.join(",")},${t})`}return n},http:new class{constructor(e={}){var t;t=e,"[object Object]"!==Object.prototype.toString.call(t)&&(e={},console.warn("设置全局参数必须接收一个Object")),this.config=kw({...Ew,...e}),this.interceptors={request:new Tw,response:new Tw}}setConfig(e){this.config=e(this.config)}middleware(e){e=((e,t={})=>{const n=t.method||e.method||"GET";let o={baseURL:e.baseURL||"",method:n,url:t.url||"",params:t.params||{},custom:{...e.custom||{},...t.custom||{}},header:vw(e.header||{},t.header||{})};if(o={...o,...Sw(["getTask","validateStatus"],e,t)},"DOWNLOAD"===n)yw(t.timeout)?yw(e.timeout)||(o.timeout=e.timeout):o.timeout=t.timeout;else if("UPLOAD"===n)delete o.header["content-type"],delete o.header["Content-Type"],["files","file","filePath","name","timeout","formData"].forEach((e=>{yw(t[e])||(o[e]=t[e])})),yw(o.timeout)&&!yw(e.timeout)&&(o.timeout=e.timeout);else{const n=["data","timeout","dataType","responseType","withCredentials"];o={...o,...Sw(n,e,t)}}return o})(this.config,e);const t=[xw,void 0];let n=Promise.resolve(e);for(this.interceptors.request.forEach((e=>{t.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((e=>{t.push(e.fulfilled,e.rejected)}));t.length;)n=n.then(t.shift(),t.shift());return n}request(e={}){return this.middleware(e)}get(e,t={}){return this.middleware({url:e,method:"GET",...t})}post(e,t,n={}){return this.middleware({url:e,data:t,method:"POST",...n})}put(e,t,n={}){return this.middleware({url:e,data:t,method:"PUT",...n})}delete(e,t,n={}){return this.middleware({url:e,data:t,method:"DELETE",...n})}connect(e,t,n={}){return this.middleware({url:e,data:t,method:"CONNECT",...n})}head(e,t,n={}){return this.middleware({url:e,data:t,method:"HEAD",...n})}options(e,t,n={}){return this.middleware({url:e,data:t,method:"OPTIONS",...n})}trace(e,t,n={}){return this.middleware({url:e,data:t,method:"TRACE",...n})}upload(e,t={}){return t.url=e,t.method="UPLOAD",this.middleware(t)}download(e,t={}){return t.url=e,t.method="DOWNLOAD",this.middleware(t)}},debounce:function(e,t=500,n=!1){if(null!==Qw&&clearTimeout(Qw),n){const n=!Qw;Qw=setTimeout((()=>{Qw=null}),t),n&&"function"==typeof e&&e()}else Qw=setTimeout((()=>{"function"==typeof e&&e()}),t)},throttle:function(e,t=500,n=!0){n?Jw||(Jw=!0,"function"==typeof e&&e(),setTimeout((()=>{Jw=!1}),t)):Jw||(Jw=!0,setTimeout((()=>{Jw=!1,"function"==typeof e&&e()}),t))},platform:"h5",mixin:Kw,mpMixin:Gw};uni.$uv=rx;const ix={install:(e,t={})=>{var n,o;const r=zw(Kw);null==(n=null==r?void 0:r.props)||delete n.customClass,null==(o=null==r?void 0:r.props)||delete o.customStyle,e.mixin(r),e.config.globalProperties.$uv=rx}};var sx={cookies:{path:"/"},treeOptions:{parentKey:"parentId",key:"id",children:"children"},parseDateFormat:"yyyy-MM-dd HH:mm:ss",firstDayOfWeek:1};function ax(e,t,n){if(e)if(e.forEach)e.forEach(t,n);else for(var o=0,r=e.length;o<r;o++)t.call(n,e[o],o,e)}var lx=Object.prototype.toString;function cx(e){return function(t){return"[object "+e+"]"===lx.call(t)}}var ux=Array.isArray||cx("Array");function fx(e,t){return!(!e||!e.hasOwnProperty)&&e.hasOwnProperty(t)}function dx(e,t,n){if(e)for(var o in e)fx(e,o)&&t.call(n,e[o],o,e)}function hx(e,t,n){return e?(ux(e)?ax:dx)(e,t,n):e}function px(e){return function(t){return typeof t===e}}var gx=px("function");function mx(e,t){var n=Object[e];return function(e){var o=[];if(e){if(n)return n(e);hx(e,t>1?function(t){o.push([""+t,e[t]])}:function(){o.push(arguments[t])})}return o}}var vx=mx("keys",1);function yx(e,t){var n=e.__proto__.constructor;return t?new n(t):new n}function bx(e,t){return t?_x(e,t):e}function _x(e,t){if(e)switch(lx.call(e)){case"[object Object]":var n=Object.create(e.__proto__);return dx(e,(function(e,o){n[o]=bx(e,t)})),n;case"[object Date]":case"[object RegExp]":return yx(e,e.valueOf());case"[object Array]":case"[object Arguments]":var o=[];return ax(e,(function(e){o.push(bx(e,t))})),o;case"[object Set]":var r=yx(e);return r.forEach((function(e){r.add(bx(e,t))})),r;case"[object Map]":var i=yx(e);return i.forEach((function(e,n){i.set(n,bx(e,t))})),i}return e}function wx(e,t){return e?_x(e,t):e}var xx=Object.assign;function Tx(e,t,n){for(var o,r=t.length,i=1;i<r;i++)o=t[i],ax(vx(t[i]),n?function(t){e[t]=wx(o[t],n)}:function(t){e[t]=o[t]});return e}var Sx=function(e){if(e){var t=arguments;if(!0!==e)return xx?xx.apply(Object,t):Tx(e,t);if(t.length>1)return Tx(e=ux(e[1])?[]:{},t,!0)}return e},Ex=function(){};function kx(e,t,n){for(var o=e.length-1;o>=0;o--)t.call(n,e[o],o,e)}function Cx(e,t,n){kx(vx(e),(function(o){t.call(n,e[o],o,e)}))}function Ox(e){return null===e}function Mx(e,t){return function(n){return Ox(n)?t:n[e]}}function Ax(e){return!!e&&e.constructor===Object}function Px(e,t){return Ax(e)&&Ax(t)||ux(e)&&ux(t)?(hx(t,(function(t,n){e[n]=Px(e[n],t)})),e):t}Ex.VERSION="3.5.12",Ex.mixin=function(){ax(arguments,(function(e){hx(e,(function(e,t){Ex[t]=gx(e)?function(){var t=e.apply(Ex.$context,arguments);return Ex.$context=null,t}:e}))}))},Ex.setup=function(e){return Sx(sx,e)};function Ix(e,t,n){var o=[];if(e&&arguments.length>1){if(e.map)return e.map(t,n);hx(e,(function(){o.push(t.apply(n,arguments))}))}return o}function $x(e,t,n,o,r){return function(i,s,a){if(i&&s){if(e&&i[e])return i[e](s,a);if(t&&ux(i)){for(var l=0,c=i.length;l<c;l++)if(!!s.call(a,i[l],l,i)===o)return[!0,!1,l,i[l]][n]}else for(var u in i)if(fx(i,u)&&!!s.call(a,i[u],u,i)===o)return[!0,!1,u,i[u]][n]}return r}}var Dx=$x("some",1,0,!0,!1),Lx=$x("every",1,1,!1,!0);function Bx(e,t){if(e){if(e.includes)return e.includes(t);for(var n in e)if(fx(e,n)&&t===e[n])return!0}return!1}function Fx(e,t){var n,o=0;if(ux(e)&&ux(t)){for(n=t.length;o<n;o++)if(!Bx(e,t[o]))return!1;return!0}return Bx(e,t)}function Rx(e,t,n){var o=[];if(t){gx(t)||(t=Mx(t));var r,i={};hx(e,(function(s,a){r=t.call(n,s,a,e),i[r]||(i[r]=1,o.push(s))}))}else hx(e,(function(e){Bx(o,e)||o.push(e)}));return o}function Nx(e){return Ix(e,(function(e){return e}))}var jx=px("undefined");function qx(e){return Ox(e)||jx(e)}var zx=/(.+)?\[(\d+)\]$/;function Hx(e){return e?e.splice&&e.join?e:(""+e).replace(/(\[\d+\])\.?/g,"$1.").replace(/\.$/,"").split("."):[]}function Vx(e,t,n){if(qx(e))return n;var o=function(e,t){if(e){var n,o,r,i=0;if(e[t]||fx(e,t))return e[t];if(r=(o=Hx(t)).length)for(n=e;i<r;i++)if(qx(n=Wx(n,o[i])))return i===r-1?n:void 0;return n}}(e,t);return jx(o)?n:o}function Wx(e,t){var n=t?t.match(zx):"";return n?n[1]?e[n[1]]?e[n[1]][n[2]]:void 0:e[n[2]]:e[t]}function Ux(e,t){return jx(e)?1:Ox(e)?jx(t)?-1:1:e&&e.localeCompare?e.localeCompare(t):e>t?1:-1}function Yx(e,t,n){return function(o,r){var i=o[e],s=r[e];return i===s?n?n(o,r):0:"desc"===t.order?Ux(s,i):Ux(i,s)}}function Xx(e,t,n){if(e){if(qx(t))return Nx(e).sort(Ux);for(var o,r=Ix(e,(function(e){return{data:e}})),i=function(e,t,n,o){var r=[];return ax(n=ux(n)?n:[n],(function(n,i){if(n){var s,a=n;ux(n)?(a=n[0],s=n[1]):Ax(n)&&(a=n.field,s=n.order),r.push({field:a,order:s||"asc"}),ax(t,gx(a)?function(t,n){t[i]=a.call(o,t.data,n,e)}:function(e){e[i]=a?Vx(e.data,a):e.data})}})),r}(e,r,t,n),s=i.length-1;s>=0;)o=Yx(s,i[s],o),s--;return o&&(r=r.sort(o)),Ix(r,Mx("data"))}return[]}var Kx=Xx;function Gx(e,t){return e>=t?e:(e>>=0)+Math.round(Math.random()*((t||9)-e))}var Zx=mx("values",0);function Jx(e){for(var t,n=[],o=Zx(e),r=o.length-1;r>=0;r--)t=r>0?Gx(0,r):0,n.push(o[t]),o.splice(t,1);return n}function Qx(e){return function(t){if(t){var n=e(t);if(!isNaN(n))return n}return 0}}var eT=Qx(parseFloat);function tT(e,t,n){var o=[],r=arguments.length;if(e){if(t=r>=2?eT(t):0,n=r>=3?eT(n):e.length,e.slice)return e.slice(t,n);for(;t<n;t++)o.push(e[t])}return o}var nT=$x("",0,2,!0),oT=$x("find",1,3,!0);function rT(e,t){return Ix(e,Mx(t))}function iT(e){return function(t,n){var o,r;return t&&t.length?(ax(t,(function(i,s){n&&(i=gx(n)?n(i,s,t):Vx(i,n)),qx(i)||!qx(o)&&!e(o,i)||(r=s,o=i)})),t[r]):o}}var sT=iT((function(e,t){return e<t}));function aT(e){var t,n,o,r=[];if(e&&e.length)for(t=0,o=(n=sT(e,(function(e){return e?e.length:0})))?n.length:0;t<o;t++)r.push(rT(e,t));return r}function lT(e,t){var n=[];return ax(e,(function(e){n=n.concat(ux(e)?t?lT(e,t):e:[e])})),n}function cT(e,t){for(var n=0,o=t.length;e&&n<o;)e=e[t[n++]];return o&&e?e:0}function uT(e,t){try{delete e[t]}catch(n){e[t]=void 0}}function fT(e,t,n){return e?(ux(e)?kx:Cx)(e,t,n):e}var dT=px("object");function hT(e,t,n){if(e){var o,r=arguments.length>1&&(Ox(t)||!dT(t)),i=r?n:t;if(Ax(e))dx(e,r?function(n,o){e[o]=t}:function(t,n){uT(e,n)}),i&&Sx(e,i);else if(ux(e)){if(r)for(o=e.length;o>0;)o--,e[o]=t;else e.length=0;i&&e.push.apply(e,i)}}return e}function pT(e,t,n){if(e){if(!qx(t)){var o=[],r=[];return gx(t)||(i=t,t=function(e,t){return t===i}),hx(e,(function(e,r,i){t.call(n,e,r,i)&&o.push(r)})),ux(e)?fT(o,(function(t,n){r.push(e[t]),e.splice(t,1)})):(r={},ax(o,(function(t){r[t]=e[t],uT(e,t)}))),r}return hT(e)}var i;return e}function gT(e,t,n){var o=n.children,r=n.data,i=n.clear;return hx(t,(function(t){var s=t[o];r&&(t=t[r]),e.push(t),s&&s.length&&gT(e,s,n),i&&delete t[o]})),e}function mT(e){return function(t,n,o,r){var i=o||{},s=i.children||"children";return e(null,t,n,r,[],[],s,i)}}var vT=mT((function e(t,n,o,r,i,s,a,l){var c,u,f,d,h,p;if(n)for(u=0,f=n.length;u<f;u++){if(c=n[u],d=i.concat([""+u]),h=s.concat([c]),o.call(r,c,u,n,d,t,h))return{index:u,item:c,path:d,items:n,parent:t,nodes:h};if(a&&c&&(p=e(c,c[a],o,r,d.concat([a]),h,a)))return p}}));var yT=mT((function e(t,n,o,r,i,s,a,l){var c,u;hx(n,(function(l,f){c=i.concat([""+f]),u=s.concat([l]),o.call(r,l,f,n,c,t,u),l&&a&&(c.push(a),e(l,l[a],o,r,c,u,a))}))}));var bT=mT((function e(t,n,o,r,i,s,a,l){var c,u,f,d=l.mapChildren||a;return Ix(n,(function(h,p){return c=i.concat([""+p]),u=s.concat([h]),(f=o.call(r,h,p,n,c,t,u))&&h&&a&&h[a]&&(f[d]=e(h,h[a],o,r,c,u,a,l)),f}))}));function _T(e,t,n,o,r,i,s,a,l){var c,u,f,d,h,p=[],g=l.original,m=l.data,v=l.mapChildren||a;return ax(n,(function(y,b){c=i.concat([""+b]),u=s.concat([y]),d=e||o.call(r,y,b,n,c,t,u),h=a&&y[a],d||h?(g?f=y:(f=Sx({},y),m&&(f[m]=y)),f[v]=_T(d,y,y[a],o,r,c,u,a,l),(d||f[v].length)&&p.push(f)):d&&p.push(f)})),p}var wT=mT((function(e,t,n,o,r,i,s,a){return _T(0,e,t,n,o,r,i,s,a)}));function xT(e,t){if(e.indexOf)return e.indexOf(t);for(var n=0,o=e.length;n<o;n++)if(t===e[n])return n}function TT(e,t){if(e.lastIndexOf)return e.lastIndexOf(t);for(var n=e.length-1;n>=0;n--)if(t===e[n])return n;return-1}var ST=px("number");var ET=px("string"),kT=cx("Date"),CT=parseInt;function OT(e){return Date.UTC(e.y,e.M||0,e.d||1,e.H||0,e.m||0,e.s||0,e.S||0)}function MT(e){return e.getTime()}function AT(e){return"(\\d{"+e+"})"}function PT(e){return isNaN(e)?e:CT(e)}for(var IT=AT(2),$T=AT("1,2"),DT=AT("1,7"),LT=AT("3,4"),BT=".{1}"+$T,FT="(([zZ])|([-+]\\d{2}:?\\d{2}))",RT=[LT,BT,BT,BT,BT,BT,".{1}"+DT,FT],NT=[],jT=RT.length-1;jT>=0;jT--){for(var qT="",zT=0;zT<jT+1;zT++)qT+=RT[zT];NT.push(new RegExp("^"+qT+"$"))}var HT=[["yyyy",LT],["yy",IT],["MM",IT],["M",$T],["dd",IT],["d",$T],["HH",IT],["H",$T],["mm",IT],["m",$T],["ss",IT],["s",$T],["SSS",AT(3)],["S",DT],["Z",FT]],VT={},WT=["\\[([^\\]]+)\\]"];for(zT=0;zT<HT.length;zT++){var UT=HT[zT];VT[UT[0]]=UT[1]+"?",WT.push(UT[0])}var YT=new RegExp(WT.join("|"),"g"),XT={};function KT(e,t){if(e){var n=kT(e);if(n||!t&&/^[0-9]{11,15}$/.test(e))return new Date(n?MT(e):CT(e));if(ET(e)){var o=t?function(e,t){var n=XT[t];if(!n){var o=[],r=t.replace(/([$(){}*+.?\\^|])/g,"\\$1").replace(YT,(function(e,t){var n=e.charAt(0);return"["===n?t:(o.push(n),VT[e])}));n=XT[t]={_i:o,_r:new RegExp(r)}}var i={},s=e.match(n._r);if(s){for(var a=n._i,l=1,c=s.length;l<c;l++)i[a[l-1]]=s[l];return i}return i}(e,t):function(e){for(var t,n={},o=0,r=NT.length;o<r;o++)if(t=e.match(NT[o])){n.y=t[1],n.M=t[2],n.d=t[3],n.H=t[4],n.m=t[5],n.s=t[6],n.S=t[7],n.Z=t[8];break}return n}(e);if(o.y)return o.M&&(o.M=PT(o.M)-1),o.S&&(o.S=(r=PT(o.S.substring(0,3)))<10?100*r:r<100?10*r:r),o.Z?function(e){if(/^[zZ]/.test(e.Z))return new Date(OT(e));var t=e.Z.match(/([-+])(\d{2}):?(\d{2})/);return t?new Date(OT(e)-("-"===t[1]?-1:1)*CT(t[2])*36e5+6e4*CT(t[3])):new Date("")}(o):new Date(o.y,o.M||0,o.d||1,o.H||0,o.m||0,o.s||0,o.S||0)}}var r;return new Date("")}function GT(){return new Date}function ZT(e){var t,n=e?KT(e):GT();return!!kT(n)&&((t=n.getFullYear())%4==0&&(t%100!=0||t%400==0))}function JT(e,t){return function(n,o){if(n){if(n[e])return n[e](o);if(ET(n)||ux(n))return t(n,o);for(var r in n)if(fx(n,r)&&o===n[r])return r}return-1}}var QT=JT("indexOf",xT),eS=JT("lastIndexOf",TT);function tS(e){var t=0;return ET(e)||ux(e)?e.length:(hx(e,(function(){t++})),t)}var nS=function(e){return!Ox(e)&&!isNaN(e)&&!ux(e)&&e%1==0};var oS=px("boolean"),rS=cx("RegExp"),iS=cx("Error");function sS(e){for(var t in e)return!1;return!0}var aS="undefined"!=typeof Symbol;function lS(e){return aS&&Symbol.isSymbol?Symbol.isSymbol(e):"symbol"==typeof e}var cS=cx("Arguments");var uS="undefined"==typeof document?0:document;var fS="undefined"==typeof window?0:window;var dS="undefined"!=typeof FormData;var hS="undefined"!=typeof Map;var pS="undefined"!=typeof WeakMap;var gS="undefined"!=typeof Set;var mS="undefined"!=typeof WeakSet;function vS(e){return function(t,n,o){if(t&&gx(n)){if(ux(t)||ET(t))return e(t,n,o);for(var r in t)if(fx(t,r)&&n.call(o,t[r],r,t))return r}return-1}}var yS=vS((function(e,t,n){for(var o=0,r=e.length;o<r;o++)if(t.call(n,e[o],o,e))return o;return-1}));function bS(e,t,n,o,r,i,s){if(e===t)return!0;if(e&&t&&!ST(e)&&!ST(t)&&!ET(e)&&!ET(t)){if(rS(e))return n(""+e,""+t,r,i,s);if(kT(e)||oS(e))return n(+e,+t,r,i,s);var a,l,c,u=ux(e),f=ux(t);if(u||f?u&&f:e.constructor===t.constructor)return l=vx(e),c=vx(t),o&&(a=o(e,t,r)),l.length===c.length&&(jx(a)?Lx(l,(function(r,i){return r===c[i]&&bS(e[r],t[c[i]],n,o,u||f?i:r,e,t)})):!!a)}return n(e,t,r,i,s)}function _S(e,t){return e===t}function wS(e,t){return bS(e,t,_S)}var xS=0;var TS=vS((function(e,t,n){for(var o=e.length-1;o>=0;o--)if(t.call(n,e[o],o,e))return o;return-1}));var SS=mx("entries",2);function ES(e,t){return function(n,o){var r,i,s={},a=[],l=this,c=arguments,u=c.length;if(!gx(o)){for(i=1;i<u;i++)r=c[i],a.push.apply(a,ux(r)?r:[r]);o=0}return hx(n,(function(r,i){((o?o.call(l,r,i,n):yS(a,(function(e){return e===i}))>-1)?e:t)&&(s[i]=r)})),s}}var kS=ES(1,0),CS=ES(0,1);var OS=/(.+)?\[(\d+)\]$/;function MS(e,t,n,o,r){if(!e[t]){var i,s,a=t?t.match(OS):null;if(n)s=r;else{var l=o?o.match(OS):null;s=l&&!l[1]?new Array(CT(l[2])+1):{}}return a?a[1]?(i=CT(a[2]),e[a[1]]?n?e[a[1]][i]=s:e[a[1]][i]?s=e[a[1]][i]:e[a[1]][i]=s:(e[a[1]]=new Array(i+1),e[a[1]][i]=s)):e[a[2]]=s:e[t]=s,s}return n&&(e[t]=r),e[t]}function AS(e){return"__proto__"===e||"constructor"===e||"prototype"===e}function PS(e,t,n){var o,r={};return e&&(t&&dT(t)?t=function(e){return function(){return sS(e)}}(t):gx(t)||(t=Mx(t)),hx(e,(function(i,s){o=t?t.call(n,i,s,e):i,r[o]?r[o].push(i):r[o]=[i]}))),r}var IS=iT((function(e,t){return e>t}));function $S(e){return(e.split(".")[1]||"").length}function DS(e,t){if(e.repeat)return e.repeat(t);var n=isNaN(t)?[]:new Array(CT(t));return n.join(e)+(n.length>0?e:"")}function LS(e,t){return e.substring(0,t)+"."+e.substring(t,e.length)}function BS(e){var t=""+e,n=t.match(/^([-+]?)((\d+)|((\d+)?[.](\d+)?))e([-+]{1})([0-9]+)$/);if(n){var o=e<0?"-":"",r=n[3]||"",i=n[5]||"",s=n[6]||"",a=n[7],l=n[8],c=l-s.length,u=l-r.length,f=l-i.length;return"+"===a?r?o+r+DS("0",l):c>0?o+i+s+DS("0",c):o+i+LS(s,l):r?u>0?o+"0."+DS("0",Math.abs(u))+r:o+LS(r,u):f>0?o+"0."+DS("0",Math.abs(f))+i+s:o+LS(i,f)+s}return t}function FS(e,t){var n=BS(e),o=BS(t);return parseInt(n.replace(".",""))*parseInt(o.replace(".",""))/Math.pow(10,$S(n)+$S(o))}function RS(e){return function(t,n){var o=eT(t),r=o;if(o){n>>=0;var i=BS(o).split("."),s=i[0],a=i[1]||"",l=a.substring(0,n+1),c=s+(l?"."+l:"");if(n>=a.length)return eT(c);if(c=o,n>0){var u=Math.pow(10,n);r=Math[e](FS(c,u))/u}else r=Math[e](c)}return r}}var NS=RS("round"),jS=RS("ceil"),qS=RS("floor");function zS(e){return ST(e)?BS(e):""+(qx(e)?"":e)}function HS(e,t){var n=zS(NS(e,t>>=0)).split("."),o=n[0],r=n[1]||"",i=t-r.length;return t?i>0?o+"."+r+DS("0",i):o+LS(r,Math.abs(i)):o}var VS=Qx(CT);function WS(e,t){return FS(eT(e),eT(t))}function US(e,t){var n=BS(e),o=BS(t),r=Math.pow(10,Math.max($S(n),$S(o)));return(WS(e,r)+WS(t,r))/r}function YS(e,t){var n=BS(e),o=BS(t),r=$S(n),i=$S(o)-r,s=i<0,a=Math.pow(10,s?Math.abs(i):i);return WS(n.replace(".","")/o.replace(".",""),s?1/a:a)}function XS(e,t,n){var o=0;return hx(e,t?gx(t)?function(){o=US(o,t.apply(n,arguments))}:function(e){o=US(o,Vx(e,t))}:function(e){o=US(o,e)}),o}function KS(e){return e.getFullYear()}function GS(e){return e.getMonth()}function ZS(e){return kT(e)&&!isNaN(MT(e))}function JS(e,t,n){var o=t&&!isNaN(t)?t:0;if(ZS(e=KT(e))){if("first"===n)return new Date(KS(e),GS(e)+o,1);if("last"===n)return new Date(MT(JS(e,o+1,"first"))-1);if(ST(n)&&e.setDate(n),o){var r=e.getDate();if(e.setMonth(GS(e)+o),r!==e.getDate())return e.setDate(1),new Date(MT(e)-864e5)}}return e}function QS(e,t,n){var o;if(ZS(e=KT(e))&&(t&&(o=t&&!isNaN(t)?t:0,e.setFullYear(KS(e)+o)),n||!isNaN(n))){if("first"===n)return new Date(KS(e),0,1);if("last"===n)return e.setMonth(11),JS(e,0,"last");e.setMonth(n)}return e}function eE(e,t,n){if(ZS(e=KT(e))&&!isNaN(t)){if(e.setDate(e.getDate()+CT(t)),"first"===n)return new Date(KS(e),GS(e),e.getDate());if("last"===n)return new Date(MT(eE(e,1,"first"))-1)}return e}function tE(e){return e.toUpperCase()}function nE(e,t,n,o){if(ZS(e=KT(e))){var r=ST(n),i=ST(o),s=MT(e);if(r||i){var a=i?o:sx.firstDayOfWeek,l=e.getDay(),c=r?n:l;if(l!==c){var u=0;a>l?u=-(7-a+l):a<l&&(u=a-l),s+=c>a?864e5*((0===c?7:c)-a+u):c<a?864e5*(7-a+c+u):864e5*u}}return t&&!isNaN(t)&&(s+=6048e5*t),new Date(s)}return e}function oE(e){return function(t,n){var o=ST(n)?n:sx.firstDayOfWeek,r=nE(t,0,o,o);if(ZS(r)){var i=new Date(r.getFullYear(),r.getMonth(),r.getDate()),s=e(r),a=s.getDay();return a>o&&s.setDate(7-a+o+1),a<o&&s.setDate(o-a+1),Math.floor((MT(i)-MT(s))/6048e5+1)}return NaN}}var rE=oE((function(e){return new Date(e.getFullYear(),0,1)}));function iE(e){return MT(function(e){return new Date(KS(e),GS(e),e.getDate())}(e))}function sE(e){return ZS(e=KT(e))?Math.floor((iE(e)-iE(QS(e,0,"first")))/864e5)+1:NaN}function aE(e,t,n){var o=zS(e);return t>>=0,n=jx(n)?" ":""+n,o.padStart?o.padStart(t,n):t>o.length?((t-=o.length)>n.length&&(n+=DS(n,t/n.length)),n.slice(0,t)+o):o}function lE(e,t,n,o){var r=t[n];return r?gx(r)?r(o,n,e):r[o]:o}var cE=/\[([^\]]+)]|y{2,4}|M{1,2}|d{1,2}|H{1,2}|h{1,2}|m{1,2}|s{1,2}|S{1,3}|Z{1,2}|W{1,2}|D{1,3}|[aAeEq]/g;function uE(e,t,n){if(e){if(ZS(e=KT(e))){var o=t||sx.parseDateFormat||sx.formatString,r=e.getHours(),i=r<12?"am":"pm",s=Sx({},sx.parseDateRules||sx.formatStringMatchs,n?n.formats:null),a=function(t,n){return(""+KS(e)).substr(4-n)},l=function(t,n){return aE(GS(e)+1,n,"0")},c=function(t,n){return aE(e.getDate(),n,"0")},u=function(e,t){return aE(r,t,"0")},f=function(e,t){return aE(r<=12?r:r-12,t,"0")},d=function(t,n){return aE(e.getMinutes(),n,"0")},h=function(t,n){return aE(e.getSeconds(),n,"0")},p=function(t,n){return aE(e.getMilliseconds(),n,"0")},g=function(t,n){var o=e.getTimezoneOffset()/60*-1;return lE(e,s,t,(o>=0?"+":"-")+aE(o,2,"0")+(1===n?":":"")+"00")},m=function(t,o){return aE(lE(e,s,t,rE(e,(n?n.firstDay:null)||sx.firstDayOfWeek)),o,"0")},v=function(t,n){return aE(lE(e,s,t,sE(e)),n,"0")},y={yyyy:a,yy:a,MM:l,M:l,dd:c,d:c,HH:u,H:u,hh:f,h:f,mm:d,m:d,ss:h,s:h,SSS:p,S:p,ZZ:g,Z:g,WW:m,W:m,DDD:v,D:v,a:function(t){return lE(e,s,t,i)},A:function(t){return lE(e,s,t,tE(i))},e:function(t){return lE(e,s,t,e.getDay())},E:function(t){return lE(e,s,t,e.getDay())},q:function(t){return lE(e,s,t,Math.floor((GS(e)+3)/3))}};return o.replace(cE,(function(e,t){return t||(y[e]?y[e](e,e.length):e)}))}return"Invalid Date"}return""}var fE=Date.now||function(){return MT(GT())};var dE=oE((function(e){return new Date(e.getFullYear(),e.getMonth(),1)}));var hE=[["yyyy",31536e6],["MM",2592e6],["dd",864e5],["HH",36e5],["mm",6e4],["ss",1e3],["S",0]];function pE(e){return e&&e.trimRight?e.trimRight():zS(e).replace(/[\s\uFEFF\xA0]+$/g,"")}function gE(e){return e&&e.trimLeft?e.trimLeft():zS(e).replace(/^[\s\uFEFF\xA0]+/g,"")}function mE(e){return e&&e.trim?e.trim():pE(gE(e))}var vE={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"};function yE(e){var t=new RegExp("(?:"+vx(e).join("|")+")","g");return function(n){return zS(n).replace(t,(function(t){return e[t]}))}}var bE=yE(vE),_E={};hx(vE,(function(e,t){_E[vE[t]]=t}));var wE=yE(_E);function xE(e,t,n){return e.substring(t,n)}function TE(e){return e.toLowerCase()}var SE={};var EE={};function kE(e,t,n){return zS(e).replace((n||sx).tmplRE||/\{{2}([.\w[\]\s]+)\}{2}/g,(function(e,n){return Vx(t,mE(n))}))}var CE=decodeURIComponent;function OE(e){var t,n={};return e&&ET(e)&&ax(e.split("&"),(function(e){t=e.split("="),n[CE(t[0])]=CE(t[1]||"")})),n}var ME=encodeURIComponent;function AE(e,t,n){var o,r=[];return hx(e,(function(e,i){o=ux(e),Ax(e)||o?r=r.concat(AE(e,t+"["+i+"]",o)):r.push(ME(t+"["+(n?"":i)+"]")+"="+ME(Ox(e)?"":e))})),r}var PE="undefined"==typeof location?0:location;function IE(){return PE?PE.origin||PE.protocol+"//"+PE.host:""}function $E(e){return OE(e.split("?")[1]||"")}function DE(e){var t,n,o,r,i=""+e;return 0===i.indexOf("//")?i=(PE?PE.protocol:"")+i:0===i.indexOf("/")&&(i=IE()+i),o=i.replace(/#.*/,"").match(/(\?.*)/),(r={href:i,hash:"",host:"",hostname:"",protocol:"",port:"",search:o&&o[1]&&o[1].length>1?o[1]:""}).path=i.replace(/^([a-z0-9.+-]*:)\/\//,(function(e,t){return r.protocol=t,""})).replace(/^([a-z0-9.+-]*)(:\d+)?\/?/,(function(e,t,o){return n=o||"",r.port=n.replace(":",""),r.hostname=t,r.host=t+n,"/"})).replace(/(#.*)/,(function(e,t){return r.hash=t.length>1?t:"",""})),t=r.hash.match(/#((.*)\?|(.*))/),r.pathname=r.path.replace(/(\?|#.*).*/,""),r.origin=r.protocol+"//"+r.host,r.hashKey=t&&(t[2]||t[1])||"",r.hashQuery=$E(r.hash),r.searchQuery=$E(r.search),r}function LE(e,t){var n=parseFloat(t),o=GT(),r=MT(o);switch(e){case"y":return MT(QS(o,n));case"M":return MT(JS(o,n));case"d":return MT(eE(o,n));case"h":case"H":return r+60*n*60*1e3;case"m":return r+60*n*1e3;case"s":return r+1e3*n}return r}function BE(e){return(kT(e)?e:new Date(e)).toUTCString()}function FE(e,t,n){if(uS){var o,r,i,s,a,l,c=[],u=arguments;return ux(e)?c=e:u.length>1?c=[Sx({name:e,value:t},n)]:dT(e)&&(c=[e]),c.length>0?(ax(c,(function(e){o=Sx({},sx.cookies,e),i=[],o.name&&(r=o.expires,i.push(ME(o.name)+"="+ME(dT(o.value)?JSON.stringify(o.value):o.value)),r&&(r=isNaN(r)?r.replace(/^([0-9]+)(y|M|d|H|h|m|s)$/,(function(e,t,n){return BE(LE(n,t))})):/^[0-9]{11,13}$/.test(r)||kT(r)?BE(r):BE(LE("d",r)),o.expires=r),ax(["expires","path","domain","secure"],(function(e){jx(o[e])||i.push(o[e]&&"secure"===e?e:e+"="+o[e])}))),uS.cookie=i.join("; ")})),!0):(s={},(a=uS.cookie)&&ax(a.split("; "),(function(e){l=e.indexOf("="),s[CE(e.substring(0,l))]=CE(e.substring(l+1)||"")})),1===u.length?s[e]:s)}return!1}function RE(e){return FE(e)}function NE(e,t,n){return FE(e,t,n),FE}function jE(e,t){FE(e,"",Sx({expires:-1},sx.cookies,t))}function qE(){return vx(FE())}function zE(e){try{var t="__xe_t";return e.setItem(t,1),e.removeItem(t),!0}catch(n){return!1}}function HE(e){return navigator.userAgent.indexOf(e)>-1}Sx(FE,{has:function(e){return Bx(qE(),e)},set:NE,setItem:NE,get:RE,getItem:RE,remove:jE,removeItem:jE,keys:qE,getJSON:function(){return FE()}}),Sx(Ex,{assign:Sx,objectEach:dx,lastObjectEach:Cx,objectMap:function(e,t,n){var o={};if(e){if(!t)return e;gx(t)||(t=Mx(t)),hx(e,(function(r,i){o[i]=t.call(n,r,i,e)}))}return o},merge:function(e){e||(e={});for(var t,n=arguments,o=n.length,r=1;r<o;r++)(t=n[r])&&Px(e,t);return e},uniq:Rx,union:function(){for(var e=arguments,t=[],n=0,o=e.length;n<o;n++)t=t.concat(Nx(e[n]));return Rx(t)},sortBy:Kx,orderBy:Xx,shuffle:Jx,sample:function(e,t){var n=Jx(e);return arguments.length<=1?n[0]:(t<n.length&&(n.length=t||0),n)},some:Dx,every:Lx,slice:tT,filter:function(e,t,n){var o=[];if(e&&t){if(e.filter)return e.filter(t,n);hx(e,(function(r,i){t.call(n,r,i,e)&&o.push(r)}))}return o},find:oT,findLast:function(e,t,n){if(e){ux(e)||(e=Zx(e));for(var o=e.length-1;o>=0;o--)if(t.call(n,e[o],o,e))return e[o]}},findKey:nT,includes:Bx,arrayIndexOf:xT,arrayLastIndexOf:TT,map:Ix,reduce:function(e,t,n){if(e){var o,r,i=0,s=null,a=n,l=arguments.length>2,c=vx(e);if(e.length&&e.reduce)return r=function(){return t.apply(s,arguments)},l?e.reduce(r,a):e.reduce(r);for(l&&(i=1,a=e[c[0]]),o=c.length;i<o;i++)a=t.call(s,a,e[c[i]],i,e);return a}},copyWithin:function(e,t,n,o){if(ux(e)&&e.copyWithin)return e.copyWithin(t,n,o);var r,i,s=t>>0,a=n>>0,l=e.length,c=arguments.length>3?o>>0:l;if(s<l&&(s=s>=0?s:l+s)>=0&&(a=a>=0?a:l+a)<(c=c>=0?c:l+c))for(r=0,i=e.slice(a,c);s<l&&!(i.length<=r);s++)e[s]=i[r++];return e},chunk:function(e,t){var n,o=[],r=t>>0||1;if(ux(e))if(r>=0&&e.length>r)for(n=0;n<e.length;)o.push(e.slice(n,n+r)),n+=r;else o=e.length?[e]:e;return o},zip:function(){return aT(arguments)},unzip:aT,zipObject:function(e,t){var n={};return t=t||[],hx(Zx(e),(function(e,o){n[e]=t[o]})),n},flatten:function(e,t){return ux(e)?lT(e,t):[]},toArray:Nx,includeArrays:Fx,pluck:rT,invoke:function(e,t){for(var n,o=arguments,r=[],i=[],s=2,a=o.length;s<a;s++)r.push(o[s]);if(ux(t)){for(a=t.length-1,s=0;s<a;s++)i.push(t[s]);t=t[a]}return Ix(e,(function(e){if(i.length&&(e=cT(e,i)),(n=e[t]||t)&&n.apply)return n.apply(e,r)}))},arrayEach:ax,lastArrayEach:kx,toArrayTree:function(e,t){var n,o,r,i=Sx({},sx.treeOptions,t),s=i.strict,a=i.key,l=i.parentKey,c=i.children,u=i.mapChildren,f=i.sortKey,d=i.reverse,h=i.data,p=[],g={},m={};return f&&(e=Xx(wx(e),f),d&&(e=e.reverse())),hx(e,(function(e){n=e[a],m[n]=!0})),hx(e,(function(e){n=e[a],h?(o={})[h]=e:o=e,r=e[l],g[n]=g[n]||[],g[r]=g[r]||[],g[r].push(o),o[a]=n,o[l]=r,o[c]=g[n],u&&(o[u]=g[n]),(!s||s&&qx(r))&&(m[r]||p.push(o))})),s&&function(e,t){hx(e,(function(e){e[t]&&!e[t].length&&pT(e,t)}))}(e,c),p},toTreeArray:function(e,t){return gT([],e,Sx({},sx.treeOptions,t))},findTree:vT,eachTree:yT,mapTree:bT,filterTree:function(e,t,n,o){var r=[];return e&&t&&yT(e,(function(e,n,i,s,a,l){t.call(o,e,n,i,s,a,l)&&r.push(e)}),n),r},searchTree:wT,hasOwnProp:fx,eqNull:qx,isNaN:function(e){return ST(e)&&isNaN(e)},isFinite:function(e){return ST(e)&&isFinite(e)},isUndefined:jx,isArray:ux,isFloat:function(e){return!(Ox(e)||isNaN(e)||ux(e)||nS(e))},isInteger:nS,isFunction:gx,isBoolean:oS,isString:ET,isNumber:ST,isRegExp:rS,isObject:dT,isPlainObject:Ax,isDate:kT,isError:iS,isTypeError:function(e){return!!e&&e.constructor===TypeError},isEmpty:sS,isNull:Ox,isSymbol:lS,isArguments:cS,isElement:function(e){return!!(e&&ET(e.nodeName)&&ST(e.nodeType))},isDocument:function(e){return!(!e||!uS||9!==e.nodeType)},isWindow:function(e){return fS&&!(!e||e!==e.window)},isFormData:function(e){return dS&&e instanceof FormData},isMap:function(e){return hS&&e instanceof Map},isWeakMap:function(e){return pS&&e instanceof WeakMap},isSet:function(e){return gS&&e instanceof Set},isWeakSet:function(e){return mS&&e instanceof WeakSet},isLeapYear:ZT,isMatch:function(e,t){var n=vx(e),o=vx(t);return!o.length||(Fx(n,o)?Dx(o,(function(o){return yS(n,(function(n){return n===o&&wS(e[n],t[o])}))>-1})):wS(e,t))},isEqual:wS,isEqualWith:function(e,t,n){return gx(n)?bS(e,t,(function(e,t,o,r,i){var s=n(e,t,o,r,i);return jx(s)?_S(e,t):!!s}),n):bS(e,t,_S)},getType:function(e){return Ox(e)?"null":lS(e)?"symbol":kT(e)?"date":ux(e)?"array":rS(e)?"regexp":iS(e)?"error":typeof e},uniqueId:function(e){return[e,++xS].join("")},getSize:tS,indexOf:QT,lastIndexOf:eS,findIndexOf:yS,findLastIndexOf:TS,toStringJSON:function(e){if(Ax(e))return e;if(ET(e))try{return JSON.parse(e)}catch(t){}return{}},toJSONString:function(e){return qx(e)?"":JSON.stringify(e)},keys:vx,values:Zx,entries:SS,pick:kS,omit:CS,first:function(e){return Zx(e)[0]},last:function(e){var t=Zx(e);return t[t.length-1]},each:hx,forOf:function(e,t,n){if(e)if(ux(e))for(var o=0,r=e.length;o<r&&!1!==t.call(n,e[o],o,e);o++);else for(var i in e)if(fx(e,i)&&!1===t.call(n,e[i],i,e))break},lastForOf:function(e,t,n){var o,r;if(e)if(ux(e))for(o=e.length-1;o>=0&&!1!==t.call(n,e[o],o,e);o--);else for(o=(r=fx(e)).length-1;o>=0&&!1!==t.call(n,e[r[o]],r[o],e);o--);},lastEach:fT,has:function(e,t){if(e){if(fx(e,t))return!0;var n,o,r,i,s,a,l=Hx(t),c=0,u=l.length;for(s=e;c<u&&(a=!1,(i=(n=l[c])?n.match(zx):"")?(o=i[1],r=i[2],o?s[o]&&fx(s[o],r)&&(a=!0,s=s[o][r]):fx(s,r)&&(a=!0,s=s[r])):fx(s,n)&&(a=!0,s=s[n]),a);c++)if(c===u-1)return!0}return!1},get:Vx,set:function(e,t,n){if(e)if(!e[t]&&!fx(e,t)||AS(t)){for(var o=e,r=Hx(t),i=r.length,s=0;s<i;s++)if(!AS(r[s])){var a=s===i-1;o=MS(o,r[s],a,a?null:r[s+1],n)}}else e[t]=n;return e},groupBy:PS,countBy:function(e,t,n){var o=PS(e,t,n||this);return dx(o,(function(e,t){o[t]=e.length})),o},clone:wx,clear:hT,remove:pT,range:function(e,t,n){var o,r,i=[],s=arguments;if(s.length<2&&(t=s[0],e=0),r=t>>0,(o=e>>0)<t)for(n=n>>0||1;o<r;o+=n)i.push(o);return i},destructuring:function(e,t){if(e&&t){var n=Sx.apply(this,[{}].concat(tT(arguments,1))),o=vx(n);ax(vx(e),(function(t){Bx(o,t)&&(e[t]=n[t])}))}return e},random:Gx,min:IS,max:sT,commafy:function(e,t){var n,o,r,i,s,a=Sx({},sx.commafyOptions,t),l=a.digits;return ST(e)?(n=(a.ceil?jS:a.floor?qS:NS)(e,l),i=(o=BS(l?HS(n,l):n).split("."))[0],s=o[1],(r=i&&n<0)&&(i=i.substring(1,i.length))):i=(o=(n=zS(e).replace(/,/g,""))?[n]:[])[0],o.length?(r?"-":"")+i.replace(new RegExp("(?=(?!(\\b))(.{"+(a.spaceNumber||3)+"})+$)","g"),a.separator||",")+(s?"."+s:""):n},round:NS,ceil:jS,floor:qS,toFixed:HS,toNumber:eT,toNumberString:BS,toInteger:VS,add:function(e,t){return US(eT(e),eT(t))},subtract:function(e,t){var n=eT(e),o=eT(t),r=BS(n),i=BS(o),s=$S(r),a=$S(i),l=Math.pow(10,Math.max(s,a));return parseFloat(HS((n*l-o*l)/l,s>=a?s:a))},multiply:WS,divide:function(e,t){return YS(eT(e),eT(t))},sum:XS,mean:function(e,t,n){return YS(XS(e,t,n),tS(e))},now:fE,timestamp:function(e,t){if(e){var n=KT(e,t);return kT(n)?MT(n):n}return fE()},isValidDate:ZS,isDateSame:function(e,t,n){return!(!e||!t)&&("Invalid Date"!==(e=uE(e,n))&&e===uE(t,n))},toStringDate:KT,toDateString:uE,getWhatYear:QS,getWhatQuarter:function(e,t,n){var o,r=t&&!isNaN(t)?3*t:0;return ZS(e=KT(e))?(o=3*(function(e){var t=e.getMonth();return t<3?1:t<6?2:t<9?3:4}(e)-1),e.setMonth(o),JS(e,r,n)):e},getWhatMonth:JS,getWhatWeek:nE,getWhatDay:eE,getYearDay:sE,getYearWeek:rE,getMonthWeek:dE,getDayOfYear:function(e,t){return ZS(e=KT(e))?ZT(QS(e,t))?366:365:NaN},getDayOfMonth:function(e,t){return ZS(e=KT(e))?Math.floor((MT(JS(e,t,"last"))-MT(JS(e,t,"first")))/864e5)+1:NaN},getDateDiff:function(e,t){var n,o,r,i,s,a,l={done:!1,time:0};if(e=KT(e),t=t?KT(t):GT(),ZS(e)&&ZS(t)&&(n=MT(e))<(o=MT(t)))for(i=l.time=o-n,l.done=!0,a=0,s=hE.length;a<s;a++)i>=(r=hE[a])[1]?a===s-1?l[r[0]]=i||0:(l[r[0]]=Math.floor(i/r[1]),i-=l[r[0]]*r[1]):l[r[0]]=0;return l},trim:mE,trimLeft:gE,trimRight:pE,escape:bE,unescape:wE,camelCase:function(e){if(e=zS(e),SE[e])return SE[e];var t=e.length,n=e.replace(/([-]+)/g,(function(e,n,o){return o&&o+n.length<t?"-":""}));return t=n.length,n=n.replace(/([A-Z]+)/g,(function(e,n,o){var r=n.length;return n=TE(n),o?r>2&&o+r<t?tE(xE(n,0,1))+xE(n,1,r-1)+tE(xE(n,r-1,r)):tE(xE(n,0,1))+xE(n,1,r):r>1&&o+r<t?xE(n,0,r-1)+tE(xE(n,r-1,r)):n})).replace(/(-[a-zA-Z])/g,(function(e,t){return tE(xE(t,1,t.length))})),SE[e]=n,n},kebabCase:function(e){if(e=zS(e),EE[e])return EE[e];if(/^[A-Z]+$/.test(e))return TE(e);var t=e.replace(/^([a-z])([A-Z]+)([a-z]+)$/,(function(e,t,n,o){var r=n.length;return r>1?t+"-"+TE(xE(n,0,r-1))+"-"+TE(xE(n,r-1,r))+o:TE(t+"-"+n+o)})).replace(/^([A-Z]+)([a-z]+)?$/,(function(e,t,n){var o=t.length;return TE(xE(t,0,o-1)+"-"+xE(t,o-1,o)+(n||""))})).replace(/([a-z]?)([A-Z]+)([a-z]?)/g,(function(e,t,n,o,r){var i=n.length;return i>1&&(t&&(t+="-"),o)?(t||"")+TE(xE(n,0,i-1))+"-"+TE(xE(n,i-1,i))+o:(t||"")+(r?"-":"")+TE(n)+(o||"")}));return t=t.replace(/([-]+)/g,(function(e,n,o){return o&&o+n.length<t.length?"-":""})),EE[e]=t,t},repeat:function(e,t){return DS(zS(e),t)},padStart:aE,padEnd:function(e,t,n){var o=zS(e);return t>>=0,n=jx(n)?" ":""+n,o.padEnd?o.padEnd(t,n):t>o.length?((t-=o.length)>n.length&&(n+=DS(n,t/n.length)),o+n.slice(0,t)):o},startsWith:function(e,t,n){var o=zS(e);return 0===(1===arguments.length?o:o.substring(n)).indexOf(t)},endsWith:function(e,t,n){var o=zS(e),r=arguments.length;return r>1&&(r>2?o.substring(0,n).indexOf(t)===n-1:o.indexOf(t)===o.length-1)},template:kE,toFormatString:function(e,t){return kE(e,t,{tmplRE:/\{([.\w[\]\s]+)\}/g})},toString:zS,toValueString:zS,noop:function(){},property:Mx,bind:function(e,t){var n=tT(arguments,2);return function(){return e.apply(t,tT(arguments).concat(n))}},once:function(e,t){var n=!1,o=null,r=tT(arguments,2);return function(){return n||(o=e.apply(t,tT(arguments).concat(r)),n=!0),o}},after:function(e,t,n){var o=0,r=[];return function(){var i=arguments;++o<=e&&r.push(i[0]),o>=e&&t.apply(n,[r].concat(tT(i)))}},before:function(e,t,n){var o=0,r=[];return n=n||this,function(){var i=arguments;++o<e&&(r.push(i[0]),t.apply(n,[r].concat(tT(i))))}},throttle:function(e,t,n){var o,r,i=n||{},s=!1,a=0,l=!("leading"in i)||i.leading,c="trailing"in i&&i.trailing,u=function(){s=!0,e.apply(r,o),a=setTimeout(f,t)},f=function(){a=0,s||!0!==c||u()},d=function(){o=arguments,r=this,s=!1,0===a&&(!0===l?u():!0===c&&(a=setTimeout(f,t)))};return d.cancel=function(){var e=0!==a;return clearTimeout(a),o=null,r=null,s=!1,a=0,e},d},debounce:function(e,t,n){var o,r,i=n||{},s=!1,a=0,l="boolean"==typeof n,c="leading"in i?i.leading:l,u="trailing"in i?i.trailing:!l,f=function(){s=!0,a=0,e.apply(r,o)},d=function(){!0===c&&(a=0),s||!0!==u||f()},h=function(){s=!1,o=arguments,r=this,0===a?!0===c&&f():clearTimeout(a),a=setTimeout(d,t)};return h.cancel=function(){var e=0!==a;return clearTimeout(a),o=null,r=null,a=0,e},h},delay:function(e,t){var n=tT(arguments,2),o=this;return setTimeout((function(){e.apply(o,n)}),t)},unserialize:OE,serialize:function(e){var t,n=[];return hx(e,(function(e,o){jx(e)||(t=ux(e),Ax(e)||t?n=n.concat(AE(e,o,t)):n.push(ME(o)+"="+ME(Ox(e)?"":e)))})),n.join("&").replace(/%20/g,"+")},parseUrl:DE,getBaseURL:function(){if(PE){var e=PE.pathname,t=eS(e,"/")+1;return IE()+(t===e.length?e:e.substring(0,t))}return""},locat:function(){return PE?DE(PE.href):{}},browse:function(){var e,t,n,o=!1,r={isNode:!1,isMobile:o,isPC:!1,isDoc:!!uS};return fS||"undefined"==typeof process?(n=HE("Edge"),t=HE("Chrome"),o=/(Android|webOS|iPhone|iPad|iPod|SymbianOS|BlackBerry|Windows Phone)/.test(navigator.userAgent),r.isDoc&&(e=uS.body||uS.documentElement,ax(["webkit","khtml","moz","ms","o"],(function(t){r["-"+t]=!!e[t+"MatchesSelector"]}))),Sx(r,{edge:n,firefox:HE("Firefox"),msie:!n&&r["-ms"],safari:!t&&!n&&HE("Safari"),isMobile:o,isPC:!o,isLocalStorage:zE(fS.localStorage),isSessionStorage:zE(fS.sessionStorage)})):r.isNode=!0,r},cookie:FE});const VE={reLaunch:e=>Dm({url:e}),switchTab:e=>Bm({url:e}),redirectTo:e=>$m({url:e}),navigateTo:e=>Im({url:e}),navigateBack:()=>Am()},WE={msg(e){nv({title:e,icon:"none"})},msgError(e){nv({title:e,icon:"error"})},msgSuccess(e){nv({title:e,icon:"success"})},hideMsg(e){iv()},alert(e,t){Um({title:t||"警告提示",content:e,showCancel:!1})},confirm:e=>new Promise(((t,n)=>{Um({title:"系统提示",content:e,cancelText:"取消",confirmText:"确定",success:function(e){e.confirm&&t(e.confirm)}})})),showToast(e){nv("object"==typeof e?e:{title:e,icon:"none",duration:2500})},loading(e){rv({title:e,icon:"none"})},closeLoading(){sv()}},UE={};UE.dictTypeData=(e,t)=>{const n=dw.getters.dictTypeTreeData;if(!n)return"需重新登录";const o=n.find((t=>t.dictValue===e));if(!o)return"无此字典";const r=o.children.find((e=>e.dictValue===t));return(null==r?void 0:r.name)||"无此字典"},UE.dictList=e=>{const t=dw.getters.dictTypeTreeData;if(!t)return[];const n=t.find((t=>t.dictValue===e));return n?n.children.map((e=>({value:e.dictValue,text:e.name}))):[]};const YE={tab:VE,modal:WE,tool:UE,hasPerm:function(e,t="or"){if(!e)return!1;const n=dw.getters.userInfo;if(!n)return!1;const{mobileButtonCodeList:o}=n;if(!o)return!1;if(Array.isArray(e)){return e["or"===t?"some":"every"]((e=>o.includes(e)))}return o.includes(e)}};(function(){const e=Ss(hw);return e.use(dw),e.use(ix),e.config.globalProperties.$snowy=YE,e.config.globalProperties.$xeu=Ex,uni.$snowy=YE,uni.$xeu=Ex,{app:e}})().app.use(_g).mount("#app");export{ed as $,Of as A,fm as B,rv as C,sv as D,nv as E,Um as F,Ed as G,kd as H,Wh as I,Tm as J,Mm as K,nm as L,rm as M,im as N,sm as O,Im as P,$m as Q,Bm as R,Dm as S,tm as T,Sd as U,vd as V,bd as W,qo as X,gd as Y,md as Z,cd as _,qt as a,td as a0,No as a1,_s as a2,vh as a3,nw as a4,xh as a5,Ov as a6,qr as a7,Pr as a8,Uo as a9,kf as aA,Pf as aB,cw as aC,c as aD,Gr as aE,kp as aF,Xn as aG,Tn as aH,lw as aI,pp as aJ,am as aK,oi as aL,rw as aM,$s as aN,bp as aO,iw as aP,pd as aQ,li as aa,Gw as ab,Kw as ac,ex as ad,l as ae,Bf as af,Ng as ag,yp as ah,mp as ai,Ny as aj,zy as ak,qy as al,sw as am,fv as an,Iw as ao,bs as ap,jy as aq,aw as ar,gb as as,Af as at,$f as au,If as av,uw as aw,Am as ax,uv as ay,zg as az,zr as b,vi as c,ow as d,Kr as e,Jr as f,em as g,Qr as h,Cp as i,kh as j,xp as k,Fy as l,Ho as m,gu as n,Fr as o,Z_ as p,o as q,tn as r,dw as s,p as t,sn as u,Yo as v,Rn as w,hm as x,xm as y,rg as z};
