import{a as e,_ as a,b as r}from"./uv-form.bce81edd.js";import{r as o,o as s,b as l,w as d,i as t,e as u,f as n,aP as i,l as m,m as p}from"./index-58695647.js";import{_ as w}from"./tui-button.600cfc04.js";import{_ as f}from"./_plugin-vue_export-helper.1b428a4d.js";import"./uv-icon.019f93fe.js";import"./uv-transition.8392ab8b.js";const g=f({__name:"index",setup(f){const g=o(),c=o({oldPassword:"",newPassword:"",confirmPassword:""}),v={oldPassword:[{type:"string",required:!0,message:"旧密码不能为空",trigger:["blur","change"]}],newPassword:[{type:"string",required:!0,message:"新密码不能为空",trigger:["blur","change"]},{minLength:6,maxLength:20,message:"长度在 6 到 20 个字符"}],confirmPassword:[{type:"string",required:!0,message:"确认密码不能为空",trigger:["blur","change"]},{validator:(e,a,r)=>c.value.newPassword===a,message:"两次输入的密码不一致",trigger:["blur","change"]}]},P=()=>{g.value.validate().then((e=>{i({password:c.value.oldPassword,newPassword:c.value.newPassword}).then((e=>{}))}))};return(o,i)=>{const f=m(p("uv-input"),e),_=m(p("uv-form-item"),a),h=m(p("tui-button"),w),b=m(p("uv-form"),r),y=t;return s(),l(y,{class:"pwd-container snowy-shadow"},{default:d((()=>[u(b,{ref_key:"formRef",ref:g,model:c.value,rules:v,"label-position":"top",labelWidth:"auto",labelStyle:{marginBottom:"25rpx",fontSize:"27rpx",color:"#606266"}},{default:d((()=>[u(_,{prop:"oldPassword",label:"旧密码",required:""},{default:d((()=>[u(f,{type:"password",modelValue:c.value.oldPassword,"onUpdate:modelValue":i[0]||(i[0]=e=>c.value.oldPassword=e),placeholder:"请输入旧密码"},null,8,["modelValue"])])),_:1}),u(_,{prop:"newPassword",label:"新密码",required:""},{default:d((()=>[u(f,{type:"password",modelValue:c.value.newPassword,"onUpdate:modelValue":i[1]||(i[1]=e=>c.value.newPassword=e),placeholder:"请输入新密码"},null,8,["modelValue"])])),_:1}),u(_,{prop:"confirmPassword",label:"确认密码",required:""},{default:d((()=>[u(f,{type:"password",modelValue:c.value.confirmPassword,"onUpdate:modelValue":i[2]||(i[2]=e=>c.value.confirmPassword=e),placeholder:"请确认新密码"},null,8,["modelValue"])])),_:1}),u(h,{margin:"50rpx 0",preventClick:!0,shadow:!0,onClick:P},{default:d((()=>[n("提交")])),_:1})])),_:1},8,["model"])])),_:1})}}},[["__scopeId","data-v-70dee2a4"]]);export{g as default};
