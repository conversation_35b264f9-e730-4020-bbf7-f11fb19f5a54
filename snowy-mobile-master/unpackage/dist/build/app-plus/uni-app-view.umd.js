!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){"use strict";function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var t={exports:{}},n={exports:{}},r={exports:{}},i=r.exports={version:"2.6.12"};"number"==typeof __e&&(__e=i);var a=r.exports,o={exports:{}};o.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),"number"==typeof __g&&(__g=window);var s=o.exports,l=a,u="__core-js_shared__",c=window[u]||(window[u]={});(n.exports=function(e,t){return c[e]||(c[e]=void 0!==t?t:{})})("versions",[]).push({version:l.version,mode:"window",copyright:"© 2020 <PERSON> (zloirock.ru)"});var d=n.exports,h=0,f=Math.random(),p=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++h+f).toString(36))},v=d("wks"),g=p,m=s.Symbol,_="function"==typeof m;(t.exports=function(e){return v[e]||(v[e]=_&&m[e]||(_?m:g)("Symbol."+e))}).store=v;var y,b,w=t.exports,x={},S=function(e){return"object"==typeof e?null!==e:"function"==typeof e},k=S,T=function(e){if(!k(e))throw TypeError(e+" is not an object!");return e},E=function(e){try{return!!e()}catch(t){return!0}},C=!E((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}));function M(){if(b)return y;b=1;var e=S,t=s.document,n=e(t)&&e(t.createElement);return y=function(e){return n?t.createElement(e):{}}}var O=!C&&!E((function(){return 7!=Object.defineProperty(M()("div"),"a",{get:function(){return 7}}).a})),I=S,L=T,A=O,B=function(e,t){if(!I(e))return e;var n,r;if(t&&"function"==typeof(n=e.toString)&&!I(r=n.call(e)))return r;if("function"==typeof(n=e.valueOf)&&!I(r=n.call(e)))return r;if(!t&&"function"==typeof(n=e.toString)&&!I(r=n.call(e)))return r;throw TypeError("Can't convert object to primitive value")},N=Object.defineProperty;x.f=C?Object.defineProperty:function(e,t,n){if(L(e),t=B(t,!0),L(n),A)try{return N(e,t,n)}catch(r){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[t]=n.value),e};var R=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},P=x,D=R,z=C?function(e,t,n){return P.f(e,t,D(1,n))}:function(e,t,n){return e[t]=n,e},F=w("unscopables"),$=Array.prototype;null==$[F]&&z($,F,{});var V={},j={}.toString,W=function(e){return j.call(e).slice(8,-1)},U=function(e){if(null==e)throw TypeError("Can't call method on  "+e);return e},H=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==W(e)?e.split(""):Object(e)},q=U,Y=function(e){return H(q(e))},X={exports:{}},Z={}.hasOwnProperty,G=function(e,t){return Z.call(e,t)},K=d("native-function-to-string",Function.toString),J=z,Q=G,ee=p("src"),te=K,ne="toString",re=(""+te).split(ne);a.inspectSource=function(e){return te.call(e)},(X.exports=function(e,t,n,r){var i="function"==typeof n;i&&(Q(n,"name")||J(n,"name",t)),e[t]!==n&&(i&&(Q(n,ee)||J(n,ee,e[t]?""+e[t]:re.join(String(t)))),e===window?e[t]=n:r?e[t]?e[t]=n:J(e,t,n):(delete e[t],J(e,t,n)))})(Function.prototype,ne,(function(){return"function"==typeof this&&this[ee]||te.call(this)}));var ie=X.exports,ae=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e},oe=ae,se=a,le=z,ue=ie,ce=function(e,t,n){if(oe(e),void 0===t)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,r){return e.call(t,n,r)};case 3:return function(n,r,i){return e.call(t,n,r,i)}}return function(){return e.apply(t,arguments)}},de=function(e,t,n){var r,i,a,o,s=e&de.F,l=e&de.G,u=e&de.S,c=e&de.P,d=e&de.B,h=l?window:u?window[t]||(window[t]={}):(window[t]||{}).prototype,f=l?se:se[t]||(se[t]={}),p=f.prototype||(f.prototype={});for(r in l&&(n=t),n)a=((i=!s&&h&&void 0!==h[r])?h:n)[r],o=d&&i?ce(a,window):c&&"function"==typeof a?ce(Function.call,a):a,h&&ue(h,r,a,e&de.U),f[r]!=a&&le(f,r,o),c&&p[r]!=a&&(p[r]=a)};window.core=se,de.F=1,de.G=2,de.S=4,de.P=8,de.B=16,de.W=32,de.U=64,de.R=128;var he,fe,pe,ve=de,ge=Math.ceil,me=Math.floor,_e=function(e){return isNaN(e=+e)?0:(e>0?me:ge)(e)},ye=_e,be=Math.min,we=_e,xe=Math.max,Se=Math.min,ke=Y,Te=function(e){return e>0?be(ye(e),9007199254740991):0},Ee=function(e,t){return(e=we(e))<0?xe(e+t,0):Se(e,t)},Ce=d("keys"),Me=p,Oe=function(e){return Ce[e]||(Ce[e]=Me(e))},Ie=G,Le=Y,Ae=(he=!1,function(e,t,n){var r,i=ke(e),a=Te(i.length),o=Ee(n,a);if(he&&t!=t){for(;a>o;)if((r=i[o++])!=r)return!0}else for(;a>o;o++)if((he||o in i)&&i[o]===t)return he||o||0;return!he&&-1}),Be=Oe("IE_PROTO"),Ne="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(","),Re=function(e,t){var n,r=Le(e),i=0,a=[];for(n in r)n!=Be&&Ie(r,n)&&a.push(n);for(;t.length>i;)Ie(r,n=t[i++])&&(~Ae(a,n)||a.push(n));return a},Pe=Ne,De=Object.keys||function(e){return Re(e,Pe)},ze=x,Fe=T,$e=De,Ve=C?Object.defineProperties:function(e,t){Fe(e);for(var n,r=$e(t),i=r.length,a=0;i>a;)ze.f(e,n=r[a++],t[n]);return e};var je=T,We=Ve,Ue=Ne,He=Oe("IE_PROTO"),qe=function(){},Ye=function(){var e,t=M()("iframe"),n=Ue.length;for(t.style.display="none",function(){if(pe)return fe;pe=1;var e=s.document;return fe=e&&e.documentElement}().appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),Ye=e.F;n--;)delete Ye.prototype[Ue[n]];return Ye()},Xe=Object.create||function(e,t){var n;return null!==e?(qe.prototype=je(e),n=new qe,qe.prototype=null,n[He]=e):n=Ye(),void 0===t?n:We(n,t)},Ze=x.f,Ge=G,Ke=w("toStringTag"),Je=function(e,t,n){e&&!Ge(e=n?e:e.prototype,Ke)&&Ze(e,Ke,{configurable:!0,value:t})},Qe=Xe,et=R,tt=Je,nt={};z(nt,w("iterator"),(function(){return this}));var rt=U,it=function(e){return Object(rt(e))},at=G,ot=it,st=Oe("IE_PROTO"),lt=Object.prototype,ut=Object.getPrototypeOf||function(e){return e=ot(e),at(e,st)?e[st]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?lt:null},ct=ve,dt=ie,ht=z,ft=V,pt=function(e,t,n){e.prototype=Qe(nt,{next:et(1,n)}),tt(e,t+" Iterator")},vt=Je,gt=ut,mt=w("iterator"),_t=!([].keys&&"next"in[].keys()),yt="keys",bt="values",wt=function(){return this},xt=function(e){$[F][e]=!0},St=function(e,t){return{value:t,done:!!e}},kt=V,Tt=Y,Et=function(e,t,n,r,i,a,o){pt(n,t,r);var s,l,u,c=function(e){if(!_t&&e in p)return p[e];switch(e){case yt:case bt:return function(){return new n(this,e)}}return function(){return new n(this,e)}},d=t+" Iterator",h=i==bt,f=!1,p=e.prototype,v=p[mt]||p["@@iterator"]||i&&p[i],g=v||c(i),m=i?h?c("entries"):g:void 0,_="Array"==t&&p.entries||v;if(_&&(u=gt(_.call(new e)))!==Object.prototype&&u.next&&(vt(u,d,!0),"function"!=typeof u[mt]&&ht(u,mt,wt)),h&&v&&v.name!==bt&&(f=!0,g=function(){return v.call(this)}),(_t||f||!p[mt])&&ht(p,mt,g),ft[t]=g,ft[d]=wt,i)if(s={values:h?g:c(bt),keys:a?g:c(yt),entries:m},o)for(l in s)l in p||dt(p,l,s[l]);else ct(ct.P+ct.F*(_t||f),t,s);return s}(Array,"Array",(function(e,t){this._t=Tt(e),this._i=0,this._k=t}),(function(){var e=this._t,t=this._k,n=this._i++;return!e||n>=e.length?(this._t=void 0,St(1)):St(0,"keys"==t?n:"values"==t?e[n]:[n,e[n]])}),"values");kt.Arguments=kt.Array,xt("keys"),xt("values"),xt("entries");for(var Ct=Et,Mt=De,Ot=ie,It=z,Lt=V,At=w,Bt=At("iterator"),Nt=At("toStringTag"),Rt=Lt.Array,Pt={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},Dt=Mt(Pt),zt=0;zt<Dt.length;zt++){var Ft,$t=Dt[zt],Vt=Pt[$t],jt=window[$t],Wt=jt&&jt.prototype;if(Wt&&(Wt[Bt]||It(Wt,Bt,Rt),Wt[Nt]||It(Wt,Nt,$t),Lt[$t]=Rt,Vt))for(Ft in Ct)Wt[Ft]||Ot(Wt,Ft,Ct[Ft],!0)}function Ut(e,t){for(var n=Object.create(null),r=e.split(","),i=0;i<r.length;i++)n[r[i]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}function Ht(e){if(gn(e)){for(var t={},n=0;n<e.length;n++){var r=e[n],i=wn(r)?Zt(r):Ht(r);if(i)for(var a in i)t[a]=i[a]}return t}return wn(e)||Sn(e)?e:void 0}var qt=/;(?![^(]*\))/g,Yt=/:([^]+)/,Xt=/\/\*[\s\S]*?\*\//g;function Zt(e){var t={};return e.replace(Xt,"").split(qt).forEach((e=>{if(e){var n=e.split(Yt);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function Gt(e){var t="";if(wn(e))t=e;else if(gn(e))for(var n=0;n<e.length;n++){var r=Gt(e[n]);r&&(t+=r+" ")}else if(Sn(e))for(var i in e)e[i]&&(t+=i+" ");return t.trim()}var Kt=Ut("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function Jt(e){return!!e||""===e}function Qt(e,t){if(e===t)return!0;var n=yn(e),r=yn(t);if(n||r)return!(!n||!r)&&e.getTime()===t.getTime();if(n=xn(e),r=xn(t),n||r)return e===t;if(n=gn(e),r=gn(t),n||r)return!(!n||!r)&&function(e,t){if(e.length!==t.length)return!1;for(var n=!0,r=0;n&&r<e.length;r++)n=Qt(e[r],t[r]);return n}(e,t);if(n=Sn(e),r=Sn(t),n||r){if(!n||!r)return!1;if(Object.keys(e).length!==Object.keys(t).length)return!1;for(var i in e){var a=e.hasOwnProperty(i),o=t.hasOwnProperty(i);if(a&&!o||!a&&o||!Qt(e[i],t[i]))return!1}}return String(e)===String(t)}function en(e,t){return e.findIndex((e=>Qt(e,t)))}var tn,nn,rn,an={},on=[],sn=()=>{},ln=()=>!1,un=/^on[^a-z]/,cn=e=>un.test(e),dn=e=>e.startsWith("onUpdate:"),hn=Object.assign,fn=(e,t)=>{var n=e.indexOf(t);n>-1&&e.splice(n,1)},pn=Object.prototype.hasOwnProperty,vn=(e,t)=>pn.call(e,t),gn=Array.isArray,mn=e=>"[object Map]"===En(e),_n=e=>"[object Set]"===En(e),yn=e=>"[object Date]"===En(e),bn=e=>"function"==typeof e,wn=e=>"string"==typeof e,xn=e=>"symbol"==typeof e,Sn=e=>null!==e&&"object"==typeof e,kn=e=>Sn(e)&&bn(e.then)&&bn(e.catch),Tn=Object.prototype.toString,En=e=>Tn.call(e),Cn=e=>"[object Object]"===En(e),Mn=e=>wn(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,On=Ut(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),In=e=>{var t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Ln=/-(\w)/g,An=In((e=>e.replace(Ln,((e,t)=>t?t.toUpperCase():"")))),Bn=/\B([A-Z])/g,Nn=In((e=>e.replace(Bn,"-$1").toLowerCase())),Rn=In((e=>e.charAt(0).toUpperCase()+e.slice(1))),Pn=In((e=>e?"on".concat(Rn(e)):"")),Dn=(e,t)=>!Object.is(e,t),zn=(e,t)=>{for(var n=0;n<e.length;n++)e[n](t)},Fn=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},$n=e=>{var t=parseFloat(e);return isNaN(t)?e:t};var Vn=ve,jn=ae,Wn=it,Un=E,Hn=[].sort,qn=[1,2,3];Vn(Vn.P+Vn.F*(Un((function(){qn.sort(void 0)}))||!Un((function(){qn.sort(null)}))||!function(){if(rn)return nn;rn=1;var e=E;return nn=function(t,n){return!!t&&e((function(){n?t.call(null,(function(){}),1):t.call(null)}))}}()(Hn)),"Array",{sort:function(e){return void 0===e?Hn.call(Wn(this)):Hn.call(Wn(this),jn(e))}});var Yn="\n",Xn="#007aff",Zn=/^([a-z-]+:)?\/\//i,Gn=/^data:.*,.*/,Kn="wxs://",Jn="json://",Qn="onThemeChange",er=0;function tr(e){var t=Date.now(),n=er?t-er:0;er=t;for(var r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];return"[".concat(t,"][").concat(n,"ms][").concat(e,"]：").concat(i.map((e=>JSON.stringify(e))).join(" "))}function nr(e){return function(e){return 0===e.indexOf("/")}(e)?e:"/"+e}function rr(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return function(){if(e){for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];t=e.apply(n,i),e=null}return t}}function ir(e,t){if(wn(t)){var n=(t=t.replace(/\[(\d+)\]/g,".$1")).split("."),r=n[0];return e||(e={}),1===n.length?e[r]:ir(e[r],n.slice(1).join("."))}}function ar(e){return An(e.substring(5))}var or=rr((()=>{var e=HTMLElement.prototype,t=e.setAttribute;e.setAttribute=function(e,n){e.startsWith("data-")&&this.tagName.startsWith("UNI-")&&((this.__uniDataset||(this.__uniDataset={}))[ar(e)]=n);t.call(this,e,n)};var n=e.removeAttribute;e.removeAttribute=function(e){this.__uniDataset&&e.startsWith("data-")&&this.tagName.startsWith("UNI-")&&delete this.__uniDataset[ar(e)],n.call(this,e)}}));function sr(e){return hn({},e.dataset,e.__uniDataset)}var lr=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function ur(e){return{passive:e}}function cr(e){var{id:t,offsetTop:n,offsetLeft:r}=e;return{id:t,dataset:sr(e),offsetTop:n,offsetLeft:r}}function dr(e){if(bn(e))return window.plus?e():void document.addEventListener("plusready",e)}var hr=/(?:Once|Passive|Capture)$/;function fr(e){var t,n;if(hr.test(e))for(t={};n=e.match(hr);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0;return[Nn(e.slice(2)),t]}var pr=(()=>({stop:1,prevent:2,self:4}))(),vr="class",gr="style",mr=".vShow",_r=".vOwnerId",yr=".vRenderjs",br="change:";var wr=function(){};wr.prototype={on:function(e,t,n){var r=this.e||(this.e={});return(r[e]||(r[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){var r=this;function i(){r.off(e,i),t.apply(n,arguments)}return i._=t,this.on(e,i,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),r=0,i=n.length;r<i;r++)n[r].fn.apply(n[r].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),r=n[e],i=[];if(r&&t)for(var a=0,o=r.length;a<o;a++)r[a].fn!==t&&r[a].fn._!==t&&i.push(r[a]);return i.length?n[e]=i:delete n[e],this}};var xr=wr,Sr=["{","}"];var kr=/^(?:\d)+/,Tr=/^(?:\w)+/;var Er="zh-Hans",Cr="zh-Hant",Mr="en",Or="fr",Ir="es",Lr=Object.prototype.hasOwnProperty,Ar=(e,t)=>Lr.call(e,t),Br=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Sr;if(!t)return[e];var r=this._caches[e];return r||(r=function(e,t){var[n,r]=t,i=[],a=0,o="";for(;a<e.length;){var s=e[a++];if(s===n){o&&i.push({type:"text",value:o}),o="";var l="";for(s=e[a++];void 0!==s&&s!==r;)l+=s,s=e[a++];var u=s===r,c=kr.test(l)?"list":u&&Tr.test(l)?"named":"unknown";i.push({value:l,type:c})}else o+=s}return o&&i.push({type:"text",value:o}),i}(e,n),this._caches[e]=r),function(e,t){var n=[],r=0,i=Array.isArray(t)?"list":(a=t,null!==a&&"object"==typeof a?"named":"unknown");var a;if("unknown"===i)return n;for(;r<e.length;){var o=e[r];switch(o.type){case"text":n.push(o.value);break;case"list":n.push(t[parseInt(o.value,10)]);break;case"named":"named"===i&&n.push(t[o.value])}r++}return n}(r,t)}};function Nr(e,t){if(e){if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return Er;if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?Er:e.indexOf("-hant")>-1?Cr:(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?Cr:Er);var n,r=[Mr,Or,Ir];t&&Object.keys(t).length>0&&(r=Object.keys(t));var i=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,r);return i||void 0}}class Rr{constructor(e){var{locale:t,fallbackLocale:n,messages:r,watcher:i,formater:a}=e;this.locale=Mr,this.fallbackLocale=Mr,this.message={},this.messages={},this.watchers=[],n&&(this.fallbackLocale=n),this.formater=a||Br,this.messages=r||{},this.setLocale(t||Mr),i&&this.watchLocale(i)}setLocale(e){var t=this.locale;this.locale=Nr(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){var t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=this.messages[e];r?n?Object.assign(r,t):Object.keys(t).forEach((e=>{Ar(r,e)||(r[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){var r=this.message;return"string"==typeof t?(t=Nr(t,this.messages))&&(r=this.messages[t]):n=t,Ar(r,e)?this.formater.interpolate(r[e],n).join(""):(console.warn("Cannot translate the value of keypath ".concat(e,". Use the value of keypath as default.")),e)}}function Pr(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}function Dr(){return"undefined"!=typeof uni&&uni.getLocale?uni.getLocale():"undefined"!=typeof window&&window.getLocale?window.getLocale():Mr}var zr,Fr=rr((()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length));function $r(){var e;if(!zr&&(e="function"==typeof getApp?weex.requireModule("plus").getLanguage():plus.webview.currentWebview().getStyle().locale,zr=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0;"string"!=typeof e&&([e,t]=[t,e]),"string"!=typeof e&&(e=Dr()),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||Mr);var i=new Rr({locale:e,fallbackLocale:n,messages:t,watcher:r}),a=(e,t)=>{if("function"!=typeof getApp)a=function(e,t){return i.t(e,t)};else{var n=!1;a=function(e,t){var r=getApp().$vm;return r&&(r.$locale,n||(n=!0,Pr(r,i))),i.t(e,t)}}return a(e,t)};return{i18n:i,f:(e,t,n)=>i.f(e,t,n),t:(e,t)=>a(e,t),add(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return i.add(e,t,n)},watch:e=>i.watchLocale(e),getLocale:()=>i.getLocale(),setLocale:e=>i.setLocale(e)}}(e),Fr())){var t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach((e=>zr.add(e,__uniConfig.locales[e]))),zr.setLocale(e)}return zr}function Vr(e,t,n){return t.reduce(((t,r,i)=>(t[e+r]=n[i],t)),{})}var jr=rr((()=>{var e="uni.picker.",t=["done","cancel"];$r().add(Mr,Vr(e,t,["Done","Cancel"]),!1),$r().add(Ir,Vr(e,t,["OK","Cancelar"]),!1),$r().add(Or,Vr(e,t,["OK","Annuler"]),!1),$r().add(Er,Vr(e,t,["完成","取消"]),!1),$r().add(Cr,Vr(e,t,["完成","取消"]),!1)})),Wr=rr((()=>{var e="uni.button.",t=["feedback.title","feedback.send"];$r().add(Mr,Vr(e,t,["feedback","send"]),!1),$r().add(Ir,Vr(e,t,["realimentación","enviar"]),!1),$r().add(Or,Vr(e,t,["retour d'information","envoyer"]),!1),$r().add(Er,Vr(e,t,["问题反馈","发送"]),!1),$r().add(Cr,Vr(e,t,["問題反饋","發送"]),!1)}));function Ur(e){var t=new xr;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return t.emit(e,...r)},subscribe(n,r){t[arguments.length>2&&void 0!==arguments[2]&&arguments[2]?"once":"on"]("".concat(e,".").concat(n),r)},unsubscribe(n,r){t.off("".concat(e,".").concat(n),r)},subscribeHandler(n,r,i){t.emit("".concat(e,".").concat(n),r,i)}}}var Hr="invokeViewApi",qr="invokeServiceApi",Yr=1,Xr=Object.create(null);function Zr(e,t){return e+"."+t}function Gr(e,t,n){t=Zr(e,t),Xr[t]||(Xr[t]=n)}function Kr(e,t){var{id:n,name:r,args:i}=e;r=Zr(t,r);var a=e=>{n&&UniViewJSBridge.publishHandler("invokeViewApi."+n,e)},o=Xr[r];o?o(i,a):a({})}var Jr,Qr=hn(Ur("service"),{invokeServiceMethod:(e,t,n)=>{var{subscribe:r,publishHandler:i}=UniViewJSBridge,a=n?Yr++:0;n&&r("invokeServiceApi."+a,n,!0),i(qr,{id:a,name:e,args:t})}}),ei=ur(!0);function ti(){Jr&&(clearTimeout(Jr),Jr=null)}var ni,ri=0,ii=0;function ai(e){if(ti(),1===e.touches.length){var{pageX:t,pageY:n}=e.touches[0];ri=t,ii=n,Jr=setTimeout((function(){var t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)}),350)}}function oi(e){if(Jr){if(1!==e.touches.length)return ti();var{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-ri)>10||Math.abs(n-ii)>10?ti():void 0}}function si(e,t){var n=Number(e);return isNaN(n)?t:n}function li(){var e=__uniConfig.globalStyle||{},t=si(e.rpxCalcMaxDeviceWidth,960),n=si(e.rpxCalcBaseDeviceWidth,375);function r(){var e,r,i,a=(e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,r=e&&90===Math.abs(window.orientation),i=e?Math[r?"max":"min"](screen.width,screen.height):screen.width,Math.min(window.innerWidth,document.documentElement.clientWidth,i)||i);a=a<=t?a:n,document.documentElement.style.fontSize=a/23.4375+"px"}r(),document.addEventListener("DOMContentLoaded",r),window.addEventListener("load",r),window.addEventListener("resize",r)}function ui(){li(),or(),window.addEventListener("touchstart",ai,ei),window.addEventListener("touchmove",oi,ei),window.addEventListener("touchend",ti,ei),window.addEventListener("touchcancel",ti,ei)}class ci{constructor(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=ni,!e&&ni&&(this.index=(ni.scopes||(ni.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){var t=ni;try{return ni=this,e()}finally{ni=t}}}on(){ni=this}off(){ni=this.parent}stop(e){if(this._active){var t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){var r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0,this._active=!1}}}function di(){return ni}var hi,fi=e=>{var t=new Set(e);return t.w=0,t.n=0,t},pi=e=>(e.w&_i)>0,vi=e=>(e.n&_i)>0,gi=new WeakMap,mi=0,_i=1,yi=Symbol(""),bi=Symbol("");class wi{constructor(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2?arguments[2]:void 0;this.fn=e,this.scheduler=t,this.active=!0,this.deps=[],this.parent=void 0,function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ni;t&&t.active&&t.effects.push(e)}(this,n)}run(){if(!this.active)return this.fn();for(var e=hi,t=Si;e;){if(e===this)return;e=e.parent}try{return this.parent=hi,hi=this,Si=!0,_i=1<<++mi,mi<=30?(e=>{var{deps:t}=e;if(t.length)for(var n=0;n<t.length;n++)t[n].w|=_i})(this):xi(this),this.fn()}finally{mi<=30&&(e=>{var{deps:t}=e;if(t.length){for(var n=0,r=0;r<t.length;r++){var i=t[r];pi(i)&&!vi(i)?i.delete(e):t[n++]=i,i.w&=~_i,i.n&=~_i}t.length=n}})(this),_i=1<<--mi,hi=this.parent,Si=t,this.parent=void 0,this.deferStop&&this.stop()}}stop(){hi===this?this.deferStop=!0:this.active&&(xi(this),this.onStop&&this.onStop(),this.active=!1)}}function xi(e){var{deps:t}=e;if(t.length){for(var n=0;n<t.length;n++)t[n].delete(e);t.length=0}}var Si=!0,ki=[];function Ti(){ki.push(Si),Si=!1}function Ei(){var e=ki.pop();Si=void 0===e||e}function Ci(e,t,n){if(Si&&hi){var r=gi.get(e);r||gi.set(e,r=new Map);var i=r.get(n);i||r.set(n,i=fi()),Mi(i)}}function Mi(e,t){var n=!1;mi<=30?vi(e)||(e.n|=_i,n=!pi(e)):n=!e.has(hi),n&&(e.add(hi),hi.deps.push(e))}function Oi(e,t,n,r,i,a){var o=gi.get(e);if(o){var s=[];if("clear"===t)s=[...o.values()];else if("length"===n&&gn(e)){var l=Number(r);o.forEach(((e,t)=>{("length"===t||t>=l)&&s.push(e)}))}else switch(void 0!==n&&s.push(o.get(n)),t){case"add":gn(e)?Mn(n)&&s.push(o.get("length")):(s.push(o.get(yi)),mn(e)&&s.push(o.get(bi)));break;case"delete":gn(e)||(s.push(o.get(yi)),mn(e)&&s.push(o.get(bi)));break;case"set":mn(e)&&s.push(o.get(yi))}if(1===s.length)s[0]&&Ii(s[0]);else{var u=[];for(var c of s)c&&u.push(...c);Ii(fi(u))}}}function Ii(e,t){var n=gn(e)?e:[...e];for(var r of n)r.computed&&Li(r);for(var i of n)i.computed||Li(i)}function Li(e,t){(e!==hi||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}var Ai=Ut("__proto__,__v_isRef,__isVue"),Bi=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(xn)),Ni=$i(),Ri=$i(!1,!0),Pi=$i(!0),Di=zi();function zi(){var e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(){for(var e=Ta(this),n=0,r=this.length;n<r;n++)Ci(e,0,n+"");for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];var s=e[t](...a);return-1===s||!1===s?e[t](...a.map(Ta)):s}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(){Ti();for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];var i=Ta(this)[t].apply(this,n);return Ei(),i}})),e}function Fi(e){var t=Ta(this);return Ci(t,0,e),t.hasOwnProperty(e)}function $i(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return function(n,r,i){if("__v_isReactive"===r)return!e;if("__v_isReadonly"===r)return e;if("__v_isShallow"===r)return t;if("__v_raw"===r&&i===(e?t?va:pa:t?fa:ha).get(n))return n;var a=gn(n);if(!e){if(a&&vn(Di,r))return Reflect.get(Di,r,i);if("hasOwnProperty"===r)return Fi}var o=Reflect.get(n,r,i);return(xn(r)?Bi.has(r):Ai(r))?o:(e||Ci(n,0,r),t?o:La(o)?a&&Mn(r)?o:o.value:Sn(o)?e?ya(o):ma(o):o)}}function Vi(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return function(t,n,r,i){var a=t[n];if(xa(a)&&La(a)&&!La(r))return!1;if(!e&&(Sa(r)||xa(r)||(a=Ta(a),r=Ta(r)),!gn(t)&&La(a)&&!La(r)))return a.value=r,!0;var o=gn(t)&&Mn(n)?Number(n)<t.length:vn(t,n),s=Reflect.set(t,n,r,i);return t===Ta(i)&&(o?Dn(r,a)&&Oi(t,"set",n,r):Oi(t,"add",n,r)),s}}var ji={get:Ni,set:Vi(),deleteProperty:function(e,t){var n=vn(e,t);e[t];var r=Reflect.deleteProperty(e,t);return r&&n&&Oi(e,"delete",t,void 0),r},has:function(e,t){var n=Reflect.has(e,t);return xn(t)&&Bi.has(t)||Ci(e,0,t),n},ownKeys:function(e){return Ci(e,0,gn(e)?"length":yi),Reflect.ownKeys(e)}},Wi={get:Pi,set:(e,t)=>!0,deleteProperty:(e,t)=>!0},Ui=hn({},ji,{get:Ri,set:Vi(!0)}),Hi=e=>e,qi=e=>Reflect.getPrototypeOf(e);function Yi(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=Ta(e=e.__v_raw),a=Ta(t);n||(t!==a&&Ci(i,0,t),Ci(i,0,a));var{has:o}=qi(i),s=r?Hi:n?Ma:Ca;return o.call(i,t)?s(e.get(t)):o.call(i,a)?s(e.get(a)):void(e!==i&&e.get(t))}function Xi(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.__v_raw,r=Ta(n),i=Ta(e);return t||(e!==i&&Ci(r,0,e),Ci(r,0,i)),e===i?n.has(e):n.has(e)||n.has(i)}function Zi(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e=e.__v_raw,!t&&Ci(Ta(e),0,yi),Reflect.get(e,"size",e)}function Gi(e){e=Ta(e);var t=Ta(this);return qi(t).has.call(t,e)||(t.add(e),Oi(t,"add",e,e)),this}function Ki(e,t){t=Ta(t);var n=Ta(this),{has:r,get:i}=qi(n),a=r.call(n,e);a||(e=Ta(e),a=r.call(n,e));var o=i.call(n,e);return n.set(e,t),a?Dn(t,o)&&Oi(n,"set",e,t):Oi(n,"add",e,t),this}function Ji(e){var t=Ta(this),{has:n,get:r}=qi(t),i=n.call(t,e);i||(e=Ta(e),i=n.call(t,e)),r&&r.call(t,e);var a=t.delete(e);return i&&Oi(t,"delete",e,void 0),a}function Qi(){var e=Ta(this),t=0!==e.size,n=e.clear();return t&&Oi(e,"clear",void 0,void 0),n}function ea(e,t){return function(n,r){var i=this,a=i.__v_raw,o=Ta(a),s=t?Hi:e?Ma:Ca;return!e&&Ci(o,0,yi),a.forEach(((e,t)=>n.call(r,s(e),s(t),i)))}}function ta(e,t,n){return function(){var r=this.__v_raw,i=Ta(r),a=mn(i),o="entries"===e||e===Symbol.iterator&&a,s="keys"===e&&a,l=r[e](...arguments),u=n?Hi:t?Ma:Ca;return!t&&Ci(i,0,s?bi:yi),{next(){var{value:e,done:t}=l.next();return t?{value:e,done:t}:{value:o?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function na(e){return function(){return"delete"!==e&&this}}function ra(){var e={get(e){return Yi(this,e)},get size(){return Zi(this)},has:Xi,add:Gi,set:Ki,delete:Ji,clear:Qi,forEach:ea(!1,!1)},t={get(e){return Yi(this,e,!1,!0)},get size(){return Zi(this)},has:Xi,add:Gi,set:Ki,delete:Ji,clear:Qi,forEach:ea(!1,!0)},n={get(e){return Yi(this,e,!0)},get size(){return Zi(this,!0)},has(e){return Xi.call(this,e,!0)},add:na("add"),set:na("set"),delete:na("delete"),clear:na("clear"),forEach:ea(!0,!1)},r={get(e){return Yi(this,e,!0,!0)},get size(){return Zi(this,!0)},has(e){return Xi.call(this,e,!0)},add:na("add"),set:na("set"),delete:na("delete"),clear:na("clear"),forEach:ea(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((i=>{e[i]=ta(i,!1,!1),n[i]=ta(i,!0,!1),t[i]=ta(i,!1,!0),r[i]=ta(i,!0,!0)})),[e,n,t,r]}var[ia,aa,oa,sa]=ra();function la(e,t){var n=t?e?sa:oa:e?aa:ia;return(t,r,i)=>"__v_isReactive"===r?!e:"__v_isReadonly"===r?e:"__v_raw"===r?t:Reflect.get(vn(n,r)&&r in t?n:t,r,i)}var ua={get:la(!1,!1)},ca={get:la(!1,!0)},da={get:la(!0,!1)},ha=new WeakMap,fa=new WeakMap,pa=new WeakMap,va=new WeakMap;function ga(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>En(e).slice(8,-1))(e))}function ma(e){return xa(e)?e:ba(e,!1,ji,ua,ha)}function _a(e){return ba(e,!1,Ui,ca,fa)}function ya(e){return ba(e,!0,Wi,da,pa)}function ba(e,t,n,r,i){if(!Sn(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;var a=i.get(e);if(a)return a;var o=ga(e);if(0===o)return e;var s=new Proxy(e,2===o?r:n);return i.set(e,s),s}function wa(e){return xa(e)?wa(e.__v_raw):!(!e||!e.__v_isReactive)}function xa(e){return!(!e||!e.__v_isReadonly)}function Sa(e){return!(!e||!e.__v_isShallow)}function ka(e){return wa(e)||xa(e)}function Ta(e){var t=e&&e.__v_raw;return t?Ta(t):e}function Ea(e){return Fn(e,"__v_skip",!0),e}var Ca=e=>Sn(e)?ma(e):e,Ma=e=>Sn(e)?ya(e):e;function Oa(e){Si&&hi&&Mi((e=Ta(e)).dep||(e.dep=fi()))}function Ia(e,t){var n=(e=Ta(e)).dep;n&&Ii(n)}function La(e){return!(!e||!0!==e.__v_isRef)}function Aa(e){return Na(e,!1)}function Ba(e){return Na(e,!0)}function Na(e,t){return La(e)?e:new Ra(e,t)}class Ra{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:Ta(e),this._value=t?e:Ca(e)}get value(){return Oa(this),this._value}set value(e){var t=this.__v_isShallow||Sa(e)||xa(e);e=t?e:Ta(e),Dn(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:Ca(e),Ia(this))}}var Pa,Da={get:(e,t,n)=>{return La(r=Reflect.get(e,t,n))?r.value:r;var r},set:(e,t,n,r)=>{var i=e[t];return La(i)&&!La(n)?(i.value=n,!0):Reflect.set(e,t,n,r)}};function za(e){return wa(e)?e:new Proxy(e,Da)}class Fa{constructor(e,t,n,r){this._setter=t,this.dep=void 0,this.__v_isRef=!0,this[Pa]=!1,this._dirty=!0,this.effect=new wi(e,(()=>{this._dirty||(this._dirty=!0,Ia(this))})),this.effect.computed=this,this.effect.active=this._cacheable=!r,this.__v_isReadonly=n}get value(){var e=Ta(this);return Oa(e),!e._dirty&&e._cacheable||(e._dirty=!1,e._value=e.effect.run()),e._value}set value(e){this._setter(e)}}function $a(e,t,n,r){var i;try{i=r?e(...r):e()}catch(a){ja(a,t,n)}return i}function Va(e,t,n,r){if(bn(e)){var i=$a(e,t,n,r);return i&&kn(i)&&i.catch((e=>{ja(e,t,n)})),i}for(var a=[],o=0;o<e.length;o++)a.push(Va(e[o],t,n,r));return a}function ja(e,t,n){if(t&&t.vnode,t){for(var r=t.parent,i=t.proxy,a=n;r;){var o=r.ec;if(o)for(var s=0;s<o.length;s++)if(!1===o[s](e,i,a))return;r=r.parent}var l=t.appContext.config.errorHandler;if(l)return void $a(l,null,10,[e,i,a])}!function(e,t,n){e instanceof Error?console.error(e.message+"\n"+e.stack):console.error(e)}(e)}Pa="__v_isReadonly";var Wa=!1,Ua=!1,Ha=[],qa=0,Ya=[],Xa=null,Za=0,Ga=Promise.resolve(),Ka=null;function Ja(e){var t=Ka||Ga;return e?t.then(this?e.bind(this):e):t}function Qa(e){Ha.length&&Ha.includes(e,Wa&&e.allowRecurse?qa+1:qa)||(null==e.id?Ha.push(e):Ha.splice(function(e){for(var t=qa+1,n=Ha.length;t<n;){var r=t+n>>>1;ro(Ha[r])<e?t=r+1:n=r}return t}(e.id),0,e),eo())}function eo(){Wa||Ua||(Ua=!0,Ka=Ga.then(ao))}function to(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Wa?qa+1:0;t<Ha.length;t++){var n=Ha[t];n&&n.pre&&(Ha.splice(t,1),t--,n())}}function no(e){if(Ya.length){var t=[...new Set(Ya)];if(Ya.length=0,Xa)return void Xa.push(...t);for((Xa=t).sort(((e,t)=>ro(e)-ro(t))),Za=0;Za<Xa.length;Za++)Xa[Za]();Xa=null,Za=0}}var ro=e=>null==e.id?1/0:e.id,io=(e,t)=>{var n=ro(e)-ro(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function ao(e){Ua=!1,Wa=!0,Ha.sort(io);try{for(qa=0;qa<Ha.length;qa++){var t=Ha[qa];t&&!1!==t.active&&$a(t,null,14)}}finally{qa=0,Ha.length=0,no(),Wa=!1,Ka=null,(Ha.length||Ya.length)&&ao()}}function oo(e,t){if(!e.isUnmounted){for(var n=e.vnode.props||an,r=arguments.length,i=new Array(r>2?r-2:0),a=2;a<r;a++)i[a-2]=arguments[a];var o,s=i,l=t.startsWith("update:"),u=l&&t.slice(7);if(u&&u in n){var c="".concat("modelValue"===u?"model":u,"Modifiers"),{number:d,trim:h}=n[c]||an;h&&(s=i.map((e=>wn(e)?e.trim():e))),d&&(s=i.map($n))}var f=n[o=Pn(t)]||n[o=Pn(An(t))];!f&&l&&(f=n[o=Pn(Nn(t))]),f&&Va(f,e,6,s);var p=n[o+"Once"];if(p){if(e.emitted){if(e.emitted[o])return}else e.emitted={};e.emitted[o]=!0,Va(p,e,6,s)}}}function so(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=t.emitsCache,i=r.get(e);if(void 0!==i)return i;var a=e.emits,o={},s=!1;if(!bn(e)){var l=e=>{var n=so(e,t,!0);n&&(s=!0,hn(o,n))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return a||s?(gn(a)?a.forEach((e=>o[e]=null)):hn(o,a),Sn(e)&&r.set(e,o),o):(Sn(e)&&r.set(e,null),null)}function lo(e,t){return!(!e||!cn(t))&&(t=t.slice(2).replace(/Once$/,""),vn(e,t[0].toLowerCase()+t.slice(1))||vn(e,Nn(t))||vn(e,t))}var uo=null,co=null;function ho(e){var t=uo;return uo=e,co=e&&e.type.__scopeId||null,t}function fo(e){var t,n,{type:r,vnode:i,proxy:a,withProxy:o,props:s,propsOptions:[l],slots:u,attrs:c,emit:d,render:h,renderCache:f,data:p,setupState:v,ctx:g,inheritAttrs:m}=e,_=ho(e);try{if(4&i.shapeFlag){var y=o||a;t=Ys(h.call(y,y,f,s,v,p,g)),n=c}else{var b=r;0,t=Ys(b.length>1?b(s,{attrs:c,slots:u,emit:d}):b(s,null)),n=r.props?c:po(c)}}catch(k){ja(k,e,1),t=Ws(Ns)}var w=t;if(n&&!1!==m){var x=Object.keys(n),{shapeFlag:S}=w;x.length&&7&S&&(l&&x.some(dn)&&(n=vo(n,l)),w=Hs(w,n))}return i.dirs&&((w=Hs(w)).dirs=w.dirs?w.dirs.concat(i.dirs):i.dirs),i.transition&&(w.transition=i.transition),t=w,ho(_),t}var po=e=>{var t;for(var n in e)("class"===n||"style"===n||cn(n))&&((t||(t={}))[n]=e[n]);return t},vo=(e,t)=>{var n={};for(var r in e)dn(r)&&r.slice(9)in t||(n[r]=e[r]);return n};function go(e,t,n){var r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(var i=0;i<r.length;i++){var a=r[i];if(t[a]!==e[a]&&!lo(n,a))return!0}return!1}var mo=e=>e.__isSuspense;function _o(e,t){if(el){var n=el.provides,r=el.parent&&el.parent.provides;r===n&&(n=el.provides=Object.create(r)),n[e]=t}else;}function yo(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=el||uo;if(r){var i=null==r.parent?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides;if(i&&e in i)return i[e];if(arguments.length>1)return n&&bn(t)?t.call(r.proxy):t}}function bo(e,t){return So(e,null,t)}var wo={};function xo(e,t,n){return So(e,t,n)}function So(e,t){var n,r,{immediate:i,deep:a,flush:o,onTrack:s,onTrigger:l}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:an,u=di()===(null==el?void 0:el.scope)?el:null,c=!1,d=!1;if(La(e)?(n=()=>e.value,c=Sa(e)):wa(e)?(n=()=>e,a=!0):gn(e)?(d=!0,c=e.some((e=>wa(e)||Sa(e))),n=()=>e.map((e=>La(e)?e.value:wa(e)?Eo(e):bn(e)?$a(e,u,2):void 0))):n=bn(e)?t?()=>$a(e,u,2):()=>{if(!u||!u.isUnmounted)return r&&r(),Va(e,u,3,[p])}:sn,t&&a){var h=n;n=()=>Eo(h())}var f,p=e=>{r=y.onStop=()=>{$a(e,u,4)}};if(al){if(p=sn,t?i&&Va(t,u,3,[n(),d?[]:void 0,p]):n(),"sync"!==o)return sn;var v=pl();f=v.__watcherHandles||(v.__watcherHandles=[])}var g,m=d?new Array(e.length).fill(wo):wo,_=()=>{if(y.active)if(t){var e=y.run();(a||c||(d?e.some(((e,t)=>Dn(e,m[t]))):Dn(e,m)))&&(r&&r(),Va(t,u,3,[e,m===wo?void 0:d&&m[0]===wo?[]:m,p]),m=e)}else y.run()};_.allowRecurse=!!t,"sync"===o?g=_:"post"===o?g=()=>Cs(_,u&&u.suspense):(_.pre=!0,u&&(_.id=u.uid),g=()=>Qa(_));var y=new wi(n,g);t?i?_():m=y.run():"post"===o?Cs(y.run.bind(y),u&&u.suspense):y.run();var b=()=>{y.stop(),u&&u.scope&&fn(u.scope.effects,y)};return f&&f.push(b),b}function ko(e,t,n){var r,i=this.proxy,a=wn(e)?e.includes(".")?To(i,e):()=>i[e]:e.bind(i,i);bn(t)?r=t:(r=t.handler,n=t);var o=el;nl(this);var s=So(a,r.bind(i),n);return o?nl(o):rl(),s}function To(e,t){var n=t.split(".");return()=>{for(var t=e,r=0;r<n.length&&t;r++)t=t[n[r]];return t}}function Eo(e,t){if(!Sn(e)||e.__v_skip)return e;if((t=t||new Set).has(e))return e;if(t.add(e),La(e))Eo(e.value,t);else if(gn(e))for(var n=0;n<e.length;n++)Eo(e[n],t);else if(_n(e)||mn(e))e.forEach((e=>{Eo(e,t)}));else if(Cn(e))for(var r in e)Eo(e[r],t);return e}var Co=e=>!!e.type.__asyncLoader,Mo=e=>e.type.__isKeepAlive;function Oo(e,t){Lo(e,"a",t)}function Io(e,t){Lo(e,"da",t)}function Lo(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:el,r=e.__wdc||(e.__wdc=()=>{for(var t=n;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(Bo(t,r,n),n)for(var i=n.parent;i&&i.parent;)Mo(i.parent.vnode)&&Ao(r,t,n,i),i=i.parent}function Ao(e,t,n,r){var i=Bo(t,e,r,!0);$o((()=>{fn(r[t],i)}),n)}function Bo(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:el,r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(n){var i=n[e]||(n[e]=[]),a=t.__weh||(t.__weh=function(){if(!n.isUnmounted){Ti(),nl(n);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];var o=Va(t,n,e,i);return rl(),Ei(),o}});return r?i.unshift(a):i.push(a),a}}var No=e=>function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:el;return(!al||"sp"===e)&&Bo(e,(function(){return t(...arguments)}),n)},Ro=No("bm"),Po=No("m"),Do=No("bu"),zo=No("u"),Fo=No("bum"),$o=No("um"),Vo=No("sp"),jo=No("rtg"),Wo=No("rtc");function Uo(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:el;Bo("ec",e,t)}function Ho(e,t){var n=uo;if(null===n)return e;for(var r=ul(n)||n.proxy,i=e.dirs||(e.dirs=[]),a=0;a<t.length;a++){var[o,s,l,u=an]=t[a];o&&(bn(o)&&(o={mounted:o,updated:o}),o.deep&&Eo(s),i.push({dir:o,instance:r,value:s,oldValue:void 0,arg:l,modifiers:u}))}return e}function qo(e,t,n,r){for(var i=e.dirs,a=t&&t.dirs,o=0;o<i.length;o++){var s=i[o];a&&(s.oldValue=a[o].value);var l=s.dir[r];l&&(Ti(),Va(l,n,8,[e.el,s,e,t]),Ei())}}var Yo=Symbol(),Xo=e=>e?il(e)?ul(e)||e.proxy:Xo(e.parent):null,Zo=hn(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Xo(e.parent),$root:e=>Xo(e.root),$emit:e=>e.emit,$options:e=>ns(e),$forceUpdate:e=>e.f||(e.f=()=>Qa(e.update)),$nextTick:e=>e.n||(e.n=Ja.bind(e.proxy)),$watch:e=>ko.bind(e)}),Go=(e,t)=>e!==an&&!e.__isScriptSetup&&vn(e,t),Ko={get(e,t){var n,{_:r}=e,{ctx:i,setupState:a,data:o,props:s,accessCache:l,type:u,appContext:c}=r;if("$"!==t[0]){var d=l[t];if(void 0!==d)switch(d){case 1:return a[t];case 2:return o[t];case 4:return i[t];case 3:return s[t]}else{if(Go(a,t))return l[t]=1,a[t];if(o!==an&&vn(o,t))return l[t]=2,o[t];if((n=r.propsOptions[0])&&vn(n,t))return l[t]=3,s[t];if(i!==an&&vn(i,t))return l[t]=4,i[t];Jo&&(l[t]=0)}}var h,f,p=Zo[t];return p?("$attrs"===t&&Ci(r,0,t),p(r)):(h=u.__cssModules)&&(h=h[t])?h:i!==an&&vn(i,t)?(l[t]=4,i[t]):(f=c.config.globalProperties,vn(f,t)?f[t]:void 0)},set(e,t,n){var{_:r}=e,{data:i,setupState:a,ctx:o}=r;return Go(a,t)?(a[t]=n,!0):i!==an&&vn(i,t)?(i[t]=n,!0):!vn(r.props,t)&&(("$"!==t[0]||!(t.slice(1)in r))&&(o[t]=n,!0))},has(e,t){var n,{_:{data:r,setupState:i,accessCache:a,ctx:o,appContext:s,propsOptions:l}}=e;return!!a[t]||r!==an&&vn(r,t)||Go(i,t)||(n=l[0])&&vn(n,t)||vn(o,t)||vn(Zo,t)||vn(s.config.globalProperties,t)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:vn(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}},Jo=!0;function Qo(e){var t=ns(e),n=e.proxy,r=e.ctx;Jo=!1,t.beforeCreate&&es(t.beforeCreate,e,"bc");var i,{data:a,computed:o,methods:s,watch:l,provide:u,inject:c,created:d,beforeMount:h,mounted:f,beforeUpdate:p,updated:v,activated:g,deactivated:m,beforeDestroy:_,beforeUnmount:y,destroyed:b,unmounted:w,render:x,renderTracked:S,renderTriggered:k,errorCaptured:T,serverPrefetch:E,expose:C,inheritAttrs:M,components:O,directives:I,filters:L}=t;if(c&&function(e,t){var n=arguments.length>3&&void 0!==arguments[3]&&arguments[3];gn(e)&&(e=os(e));var r=function(r){var i=e[r],a=void 0;La(a=Sn(i)?"default"in i?yo(i.from||r,i.default,!0):yo(i.from||r):yo(i))&&n?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>a.value,set:e=>a.value=e}):t[r]=a};for(var i in e)r(i)}(c,r,null,e.appContext.config.unwrapInjectedRef),s)for(var A in s){var B=s[A];bn(B)&&(r[A]=B.bind(n))}if(a&&(i=a.call(n,n),Sn(i)&&(e.data=ma(i))),Jo=!0,o){var N=function(e){var t=o[e],i=bn(t)?t.bind(n,n):bn(t.get)?t.get.bind(n,n):sn,a=!bn(t)&&bn(t.set)?t.set.bind(n):sn,s=dl({get:i,set:a});Object.defineProperty(r,e,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e})};for(var R in o)N(R)}if(l)for(var P in l)ts(l[P],r,n,P);if(u){var D=bn(u)?u.call(n):u;Reflect.ownKeys(D).forEach((e=>{_o(e,D[e])}))}function z(e,t){gn(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(d&&es(d,e,"c"),z(Ro,h),z(Po,f),z(Do,p),z(zo,v),z(Oo,g),z(Io,m),z(Uo,T),z(Wo,S),z(jo,k),z(Fo,y),z($o,w),z(Vo,E),gn(C))if(C.length){var F=e.exposed||(e.exposed={});C.forEach((e=>{Object.defineProperty(F,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});x&&e.render===sn&&(e.render=x),null!=M&&(e.inheritAttrs=M),O&&(e.components=O),I&&(e.directives=I)}function es(e,t,n){Va(gn(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function ts(e,t,n,r){var i=r.includes(".")?To(n,r):()=>n[r];if(wn(e)){var a=t[e];bn(a)&&xo(i,a)}else if(bn(e))xo(i,e.bind(n));else if(Sn(e))if(gn(e))e.forEach((e=>ts(e,t,n,r)));else{var o=bn(e.handler)?e.handler.bind(n):t[e.handler];bn(o)&&xo(i,o,e)}}function ns(e){var t,n=e.type,{mixins:r,extends:i}=n,{mixins:a,optionsCache:o,config:{optionMergeStrategies:s}}=e.appContext,l=o.get(n);return l?t=l:a.length||r||i?(t={},a.length&&a.forEach((e=>rs(t,e,s,!0))),rs(t,n,s)):t=n,Sn(n)&&o.set(n,t),t}function rs(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],{mixins:i,extends:a}=t;for(var o in a&&rs(e,a,n,!0),i&&i.forEach((t=>rs(e,t,n,!0))),t)if(r&&"expose"===o);else{var s=is[o]||n&&n[o];e[o]=s?s(e[o],t[o]):t[o]}return e}var is={data:as,props:ls,emits:ls,methods:ls,computed:ls,beforeCreate:ss,created:ss,beforeMount:ss,mounted:ss,beforeUpdate:ss,updated:ss,beforeDestroy:ss,beforeUnmount:ss,destroyed:ss,unmounted:ss,activated:ss,deactivated:ss,errorCaptured:ss,serverPrefetch:ss,components:ls,directives:ls,watch:function(e,t){if(!e)return t;if(!t)return e;var n=hn(Object.create(null),e);for(var r in t)n[r]=ss(e[r],t[r]);return n},provide:as,inject:function(e,t){return ls(os(e),os(t))}};function as(e,t){return t?e?function(){return hn(bn(e)?e.call(this,this):e,bn(t)?t.call(this,this):t)}:t:e}function os(e){if(gn(e)){for(var t={},n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ss(e,t){return e?[...new Set([].concat(e,t))]:t}function ls(e,t){return e?hn(hn(Object.create(null),e),t):t}function us(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i={},a={};for(var o in Fn(a,Fs,1),e.propsDefaults=Object.create(null),cs(e,t,i,a),e.propsOptions[0])o in i||(i[o]=void 0);n?e.props=r?i:_a(i):e.type.props?e.props=i:e.props=a,e.attrs=a}function cs(e,t,n,r){var i,[a,o]=e.propsOptions,s=!1;if(t)for(var l in t)if(!On(l)){var u=t[l],c=void 0;a&&vn(a,c=An(l))?o&&o.includes(c)?(i||(i={}))[c]=u:n[c]=u:lo(e.emitsOptions,l)||l in r&&u===r[l]||(r[l]=u,s=!0)}if(o)for(var d=Ta(n),h=i||an,f=0;f<o.length;f++){var p=o[f];n[p]=ds(a,d,p,h[p],e,!vn(h,p))}return s}function ds(e,t,n,r,i,a){var o=e[n];if(null!=o){var s=vn(o,"default");if(s&&void 0===r){var l=o.default;if(o.type!==Function&&bn(l)){var{propsDefaults:u}=i;n in u?r=u[n]:(nl(i),r=u[n]=l.call(null,t),rl())}else r=l}o[0]&&(a&&!s?r=!1:!o[1]||""!==r&&r!==Nn(n)||(r=!0))}return r}function hs(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=t.propsCache,i=r.get(e);if(i)return i;var a=e.props,o={},s=[],l=!1;if(!bn(e)){var u=e=>{l=!0;var[n,r]=hs(e,t,!0);hn(o,n),r&&s.push(...r)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!a&&!l)return Sn(e)&&r.set(e,on),on;if(gn(a))for(var c=0;c<a.length;c++){var d=An(a[c]);fs(d)&&(o[d]=an)}else if(a)for(var h in a){var f=An(h);if(fs(f)){var p=a[h],v=o[f]=gn(p)||bn(p)?{type:p}:Object.assign({},p);if(v){var g=gs(Boolean,v.type),m=gs(String,v.type);v[0]=g>-1,v[1]=m<0||g<m,(g>-1||vn(v,"default"))&&s.push(f)}}}var _=[o,s];return Sn(e)&&r.set(e,_),_}function fs(e){return"$"!==e[0]}function ps(e){var t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:null===e?"null":""}function vs(e,t){return ps(e)===ps(t)}function gs(e,t){return gn(t)?t.findIndex((t=>vs(t,e))):bn(t)&&vs(t,e)?0:-1}var ms=e=>"_"===e[0]||"$stable"===e,_s=e=>gn(e)?e.map(Ys):[Ys(e)],ys=(e,t,n)=>{if(t._n)return t;var r=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:uo;if(!t)return e;if(e._n)return e;var n=function(){n._d&&Ps(-1);var r,i=ho(t);try{r=e(...arguments)}finally{ho(i),n._d&&Ps(1)}return r};return n._n=!0,n._c=!0,n._d=!0,n}((function(){return _s(t(...arguments))}),n);return r._c=!1,r},bs=(e,t,n)=>{var r=e._ctx;for(var i in e)if(!ms(i)){var a=e[i];bn(a)?t[i]=ys(0,a,r):null!=a&&function(){var e=_s(a);t[i]=()=>e}()}},ws=(e,t)=>{var n=_s(t);e.slots.default=()=>n},xs=(e,t)=>{if(32&e.vnode.shapeFlag){var n=t._;n?(e.slots=Ta(t),Fn(t,"_",n)):bs(t,e.slots={})}else e.slots={},t&&ws(e,t);Fn(e.slots,Fs,1)};function Ss(){return{app:null,config:{isNativeTag:ln,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}var ks=0;function Ts(e,t){return function(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;bn(n)||(n=Object.assign({},n)),null==r||Sn(r)||(r=null);var i=Ss(),a=new Set,o=!1,s=i.app={_uid:ks++,_component:n,_props:r,_container:null,_context:i,_instance:null,version:vl,get config(){return i.config},set config(e){},use(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return a.has(e)||(e&&bn(e.install)?(a.add(e),e.install(s,...n)):bn(e)&&(a.add(e),e(s,...n))),s},mixin:e=>(i.mixins.includes(e)||i.mixins.push(e),s),component:(e,t)=>t?(i.components[e]=t,s):i.components[e],directive:(e,t)=>t?(i.directives[e]=t,s):i.directives[e],mount(a,l,u){if(!o){var c=Ws(n,r);return c.appContext=i,l&&t?t(c,a):e(c,a,u),o=!0,s._container=a,a.__vue_app__=s,ul(c.component)||c.component.proxy}},unmount(){o&&(e(null,s._container),delete s._container.__vue_app__)},provide:(e,t)=>(i.provides[e]=t,s)};return s}}function Es(e,t,n,r){var i=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(gn(e))e.forEach(((e,a)=>Es(e,t&&(gn(t)?t[a]:t),n,r,i)));else if(!Co(r)||i){var a=4&r.shapeFlag?ul(r.component)||r.component.proxy:r.el,o=i?null:a,{i:s,r:l}=e,u=t&&t.r,c=s.refs===an?s.refs={}:s.refs,d=s.setupState;if(null!=u&&u!==l&&(wn(u)?(c[u]=null,vn(d,u)&&(d[u]=null)):La(u)&&(u.value=null)),bn(l))$a(l,s,12,[o,c]);else{var h=wn(l),f=La(l);if(h||f){var p=()=>{if(e.f){var t=h?vn(d,l)?d[l]:c[l]:l.value;i?gn(t)&&fn(t,a):gn(t)?t.includes(a)||t.push(a):h?(c[l]=[a],vn(d,l)&&(d[l]=c[l])):(l.value=[a],e.k&&(c[e.k]=l.value))}else h?(c[l]=o,vn(d,l)&&(d[l]=o)):f&&(l.value=o,e.k&&(c[e.k]=o))};o?(p.id=-1,Cs(p,n)):p()}}}}var Cs=function(e,t){var n;t&&t.pendingBranch?gn(e)?t.effects.push(...e):t.effects.push(e):(gn(n=e)?Ya.push(...n):Xa&&Xa.includes(n,n.allowRecurse?Za+1:Za)||Ya.push(n),eo())};function Ms(e){return function(e,t){(tn||(tn="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window||"undefined"!=typeof window?window:{})).__VUE__=!0;var n,r,{insert:i,remove:a,patchProp:o,createElement:s,createText:l,createComment:u,setText:c,setElementText:d,parentNode:h,nextSibling:f,setScopeId:p=sn,insertStaticContent:v}=e,g=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:null,o=arguments.length>6&&void 0!==arguments[6]&&arguments[6],s=arguments.length>7&&void 0!==arguments[7]?arguments[7]:null,l=arguments.length>8&&void 0!==arguments[8]?arguments[8]:!!t.dynamicChildren;if(e!==t){e&&!zs(e,t)&&(r=U(e),F(e,i,a,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);var{type:u,ref:c,shapeFlag:d}=t;switch(u){case Bs:m(e,t,n,r);break;case Ns:_(e,t,n,r);break;case Rs:null==e&&y(t,n,r,o);break;case As:O(e,t,n,r,i,a,o,s,l);break;default:1&d?x(e,t,n,r,i,a,o,s,l):6&d?I(e,t,n,r,i,a,o,s,l):(64&d||128&d)&&u.process(e,t,n,r,i,a,o,s,l,q)}null!=c&&i&&Es(c,e&&e.ref,a,t||e,!t)}},m=(e,t,n,r)=>{if(null==e)i(t.el=l(t.children),n,r);else{var a=t.el=e.el;t.children!==e.children&&c(a,t.children)}},_=(e,t,n,r)=>{null==e?i(t.el=u(t.children||""),n,r):t.el=e.el},y=(e,t,n,r)=>{[e.el,e.anchor]=v(e.children,t,n,r,e.el,e.anchor)},b=(e,t,n)=>{for(var r,{el:a,anchor:o}=e;a&&a!==o;)r=f(a),i(a,t,n),a=r;i(o,t,n)},w=e=>{for(var t,{el:n,anchor:r}=e;n&&n!==r;)t=f(n),a(n),n=t;a(r)},x=(e,t,n,r,i,a,o,s,l)=>{o=o||"svg"===t.type,null==e?S(t,n,r,i,a,o,s,l):E(e,t,i,a,o,s,l)},S=(e,t,n,r,a,l,u,c)=>{var h,f,{type:p,props:v,shapeFlag:g,transition:m,dirs:_}=e;if(h=e.el=s(e.type,l,v&&v.is,v),8&g?d(h,e.children):16&g&&T(e.children,h,null,r,a,l&&"foreignObject"!==p,u,c),_&&qo(e,null,r,"created"),k(h,e,e.scopeId,u,r),v){for(var y in v)"value"===y||On(y)||o(h,y,null,v[y],l,e.children,r,a,W);"value"in v&&o(h,"value",null,v.value),(f=v.onVnodeBeforeMount)&&Ks(f,r,e)}Object.defineProperty(h,"__vueParentComponent",{value:r,enumerable:!1}),_&&qo(e,null,r,"beforeMount");var b=(!a||a&&!a.pendingBranch)&&m&&!m.persisted;b&&m.beforeEnter(h),i(h,t,n),((f=v&&v.onVnodeMounted)||b||_)&&Cs((()=>{f&&Ks(f,r,e),b&&m.enter(h),_&&qo(e,null,r,"mounted")}),a)},k=(e,t,n,r,i)=>{if(n&&p(e,n),r)for(var a=0;a<r.length;a++)p(e,r[a]);if(i&&t===i.subTree){var o=i.vnode;k(e,o,o.scopeId,o.slotScopeIds,i.parent)}},T=function(e,t,n,r,i,a,o,s){for(var l=arguments.length>8&&void 0!==arguments[8]?arguments[8]:0;l<e.length;l++){var u=e[l]=s?Xs(e[l]):Ys(e[l]);g(null,u,t,n,r,i,a,o,s)}},E=(e,t,n,r,i,a,s)=>{var l=t.el=e.el,{patchFlag:u,dynamicChildren:c,dirs:h}=t;u|=16&e.patchFlag;var f,p=e.props||an,v=t.props||an;n&&Os(n,!1),(f=v.onVnodeBeforeUpdate)&&Ks(f,n,t,e),h&&qo(t,e,n,"beforeUpdate"),n&&Os(n,!0);var g=i&&"foreignObject"!==t.type;if(c?C(e.dynamicChildren,c,l,n,r,g,a):s||R(e,t,l,null,n,r,g,a,!1),u>0){if(16&u)M(l,t,p,v,n,r,i);else if(2&u&&p.class!==v.class&&o(l,"class",null,v.class,i),4&u&&o(l,"style",p.style,v.style,i),8&u)for(var m=t.dynamicProps,_=0;_<m.length;_++){var y=m[_],b=p[y],w=v[y];w===b&&"value"!==y||o(l,y,b,w,i,e.children,n,r,W)}1&u&&e.children!==t.children&&d(l,t.children)}else s||null!=c||M(l,t,p,v,n,r,i);((f=v.onVnodeUpdated)||h)&&Cs((()=>{f&&Ks(f,n,t,e),h&&qo(t,e,n,"updated")}),r)},C=(e,t,n,r,i,a,o)=>{for(var s=0;s<t.length;s++){var l=e[s],u=t[s],c=l.el&&(l.type===As||!zs(l,u)||70&l.shapeFlag)?h(l.el):n;g(l,u,c,null,r,i,a,o,!0)}},M=(e,t,n,r,i,a,s)=>{if(n!==r){if(n!==an)for(var l in n)On(l)||l in r||o(e,l,n[l],null,s,t.children,i,a,W);for(var u in r)if(!On(u)){var c=r[u],d=n[u];c!==d&&"value"!==u&&o(e,u,d,c,s,t.children,i,a,W)}"value"in r&&o(e,"value",n.value,r.value)}},O=(e,t,n,r,a,o,s,u,c)=>{var d=t.el=e?e.el:l(""),h=t.anchor=e?e.anchor:l(""),{patchFlag:f,dynamicChildren:p,slotScopeIds:v}=t;v&&(u=u?u.concat(v):v),null==e?(i(d,n,r),i(h,n,r),T(t.children,n,h,a,o,s,u,c)):f>0&&64&f&&p&&e.dynamicChildren?(C(e.dynamicChildren,p,n,a,o,s,u),(null!=t.key||a&&t===a.subTree)&&Is(e,t,!0)):R(e,t,n,h,a,o,s,u,c)},I=(e,t,n,r,i,a,o,s,l)=>{t.slotScopeIds=s,null==e?512&t.shapeFlag?i.ctx.activate(t,n,r,o,l):L(t,n,r,i,a,o,l):A(e,t,l)},L=(e,t,n,r,i,a,o)=>{var s=e.component=function(e,t,n){var r=e.type,i=(t?t.appContext:e.appContext)||Js,a={uid:Qs++,vnode:e,type:r,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,scope:new ci(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:hs(r,i),emitsOptions:so(r,i),emit:null,emitted:null,propsDefaults:an,inheritAttrs:r.inheritAttrs,ctx:an,data:an,props:an,attrs:an,slots:an,refs:an,setupState:an,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};a.ctx={_:a},a.root=t?t.root:a,a.emit=oo.bind(null,a),e.ce&&e.ce(a);return a}(e,r,i);if(Mo(e)&&(s.ctx.renderer=q),function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];al=t;var{props:n,children:r}=e.vnode,i=il(e);us(e,n,i,t),xs(e,r);var a=i?ol(e,t):void 0;al=!1}(s),s.asyncDep){if(i&&i.registerDep(s,B),!e.el){var l=s.subTree=Ws(Ns);_(null,l,t,n)}}else B(s,e,t,n,i,a,o)},A=(e,t,n)=>{var r,i,a=t.component=e.component;if(function(e,t,n){var{props:r,children:i,component:a}=e,{props:o,children:s,patchFlag:l}=t,u=a.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!i&&!s||s&&s.$stable)||r!==o&&(r?!o||go(r,o,u):!!o);if(1024&l)return!0;if(16&l)return r?go(r,o,u):!!o;if(8&l)for(var c=t.dynamicProps,d=0;d<c.length;d++){var h=c[d];if(o[h]!==r[h]&&!lo(u,h))return!0}return!1}(e,t,n)){if(a.asyncDep&&!a.asyncResolved)return void N(a,t,n);a.next=t,r=a.update,(i=Ha.indexOf(r))>qa&&Ha.splice(i,1),a.update()}else t.el=e.el,a.vnode=t},B=(e,t,n,i,a,o,s)=>{var l=()=>{if(e.isMounted){var l,{next:u,bu:c,u:d,parent:f,vnode:p}=e,v=u;Os(e,!1),u?(u.el=p.el,N(e,u,s)):u=p,c&&zn(c),(l=u.props&&u.props.onVnodeBeforeUpdate)&&Ks(l,f,u,p),Os(e,!0);var m=fo(e),_=e.subTree;e.subTree=m,g(_,m,h(_.el),U(_),e,a,o),u.el=m.el,null===v&&function(e,t){for(var{vnode:n,parent:r}=e;r&&r.subTree===n;)(n=r.vnode).el=t,r=r.parent}(e,m.el),d&&Cs(d,a),(l=u.props&&u.props.onVnodeUpdated)&&Cs((()=>Ks(l,f,u,p)),a)}else{var y,{el:b,props:w}=t,{bm:x,m:S,parent:k}=e,T=Co(t);if(Os(e,!1),x&&zn(x),!T&&(y=w&&w.onVnodeBeforeMount)&&Ks(y,k,t),Os(e,!0),b&&r){var E=()=>{e.subTree=fo(e),r(b,e.subTree,e,a,null)};T?t.type.__asyncLoader().then((()=>!e.isUnmounted&&E())):E()}else{var C=e.subTree=fo(e);g(null,C,n,i,e,a,o),t.el=C.el}if(S&&Cs(S,a),!T&&(y=w&&w.onVnodeMounted)){var M=t;Cs((()=>Ks(y,k,M)),a)}(256&t.shapeFlag||k&&Co(k.vnode)&&256&k.vnode.shapeFlag)&&e.a&&Cs(e.a,a),e.isMounted=!0,t=n=i=null}},u=e.effect=new wi(l,(()=>Qa(c)),e.scope),c=e.update=()=>u.run();c.id=e.uid,Os(e,!0),c()},N=(e,t,n)=>{t.component=e;var r=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,r){var{props:i,attrs:a,vnode:{patchFlag:o}}=e,s=Ta(i),[l]=e.propsOptions,u=!1;if(!(r||o>0)||16&o){var c;for(var d in cs(e,t,i,a)&&(u=!0),s)t&&(vn(t,d)||(c=Nn(d))!==d&&vn(t,c))||(l?!n||void 0===n[d]&&void 0===n[c]||(i[d]=ds(l,s,d,void 0,e,!0)):delete i[d]);if(a!==s)for(var h in a)t&&vn(t,h)||(delete a[h],u=!0)}else if(8&o)for(var f=e.vnode.dynamicProps,p=0;p<f.length;p++){var v=f[p];if(!lo(e.emitsOptions,v)){var g=t[v];if(l)if(vn(a,v))g!==a[v]&&(a[v]=g,u=!0);else{var m=An(v);i[m]=ds(l,s,m,g,e,!1)}else g!==a[v]&&(a[v]=g,u=!0)}}u&&Oi(e,"set","$attrs")}(e,t.props,r,n),((e,t,n)=>{var{vnode:r,slots:i}=e,a=!0,o=an;if(32&r.shapeFlag){var s=t._;s?n&&1===s?a=!1:(hn(i,t),n||1!==s||delete i._):(a=!t.$stable,bs(t,i)),o=t}else t&&(ws(e,t),o={default:1});if(a)for(var l in i)ms(l)||l in o||delete i[l]})(e,t.children,n),Ti(),to(),Ei()},R=function(e,t,n,r,i,a,o,s){var l=arguments.length>8&&void 0!==arguments[8]&&arguments[8],u=e&&e.children,c=e?e.shapeFlag:0,h=t.children,{patchFlag:f,shapeFlag:p}=t;if(f>0){if(128&f)return void D(u,h,n,r,i,a,o,s,l);if(256&f)return void P(u,h,n,r,i,a,o,s,l)}8&p?(16&c&&W(u,i,a),h!==u&&d(n,h)):16&c?16&p?D(u,h,n,r,i,a,o,s,l):W(u,i,a,!0):(8&c&&d(n,""),16&p&&T(h,n,r,i,a,o,s,l))},P=(e,t,n,r,i,a,o,s,l)=>{t=t||on;var u,c=(e=e||on).length,d=t.length,h=Math.min(c,d);for(u=0;u<h;u++){var f=t[u]=l?Xs(t[u]):Ys(t[u]);g(e[u],f,n,null,i,a,o,s,l)}c>d?W(e,i,a,!0,!1,h):T(t,n,r,i,a,o,s,l,h)},D=(e,t,n,r,i,a,o,s,l)=>{for(var u=0,c=t.length,d=e.length-1,h=c-1;u<=d&&u<=h;){var f=e[u],p=t[u]=l?Xs(t[u]):Ys(t[u]);if(!zs(f,p))break;g(f,p,n,null,i,a,o,s,l),u++}for(;u<=d&&u<=h;){var v=e[d],m=t[h]=l?Xs(t[h]):Ys(t[h]);if(!zs(v,m))break;g(v,m,n,null,i,a,o,s,l),d--,h--}if(u>d){if(u<=h)for(var _=h+1,y=_<c?t[_].el:r;u<=h;)g(null,t[u]=l?Xs(t[u]):Ys(t[u]),n,y,i,a,o,s,l),u++}else if(u>h)for(;u<=d;)F(e[u],i,a,!0),u++;else{var b,w=u,x=u,S=new Map;for(u=x;u<=h;u++){var k=t[u]=l?Xs(t[u]):Ys(t[u]);null!=k.key&&S.set(k.key,u)}var T=0,E=h-x+1,C=!1,M=0,O=new Array(E);for(u=0;u<E;u++)O[u]=0;for(u=w;u<=d;u++){var I=e[u];if(T>=E)F(I,i,a,!0);else{var L=void 0;if(null!=I.key)L=S.get(I.key);else for(b=x;b<=h;b++)if(0===O[b-x]&&zs(I,t[b])){L=b;break}void 0===L?F(I,i,a,!0):(O[L-x]=u+1,L>=M?M=L:C=!0,g(I,t[L],n,null,i,a,o,s,l),T++)}}var A=C?function(e){var t,n,r,i,a,o=e.slice(),s=[0],l=e.length;for(t=0;t<l;t++){var u=e[t];if(0!==u){if(e[n=s[s.length-1]]<u){o[t]=n,s.push(t);continue}for(r=0,i=s.length-1;r<i;)e[s[a=r+i>>1]]<u?r=a+1:i=a;u<e[s[r]]&&(r>0&&(o[t]=s[r-1]),s[r]=t)}}r=s.length,i=s[r-1];for(;r-- >0;)s[r]=i,i=o[i];return s}(O):on;for(b=A.length-1,u=E-1;u>=0;u--){var B=x+u,N=t[B],R=B+1<c?t[B+1].el:r;0===O[u]?g(null,N,n,R,i,a,o,s,l):C&&(b<0||u!==A[b]?z(N,n,R,2):b--)}}},z=function(e,t,n,r){var a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,{el:o,type:s,transition:l,children:u,shapeFlag:c}=e;if(6&c)z(e.component.subTree,t,n,r);else if(128&c)e.suspense.move(t,n,r);else if(64&c)s.move(e,t,n,q);else if(s!==As){if(s!==Rs)if(2!==r&&1&c&&l)if(0===r)l.beforeEnter(o),i(o,t,n),Cs((()=>l.enter(o)),a);else{var{leave:d,delayLeave:h,afterLeave:f}=l,p=()=>i(o,t,n),v=()=>{d(o,(()=>{p(),f&&f()}))};h?h(o,p,v):v()}else i(o,t,n);else b(e,t,n)}else{i(o,t,n);for(var g=0;g<u.length;g++)z(u[g],t,n,r);i(e.anchor,t,n)}},F=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],{type:a,props:o,ref:s,children:l,dynamicChildren:u,shapeFlag:c,patchFlag:d,dirs:h}=e;if(null!=s&&Es(s,null,n,e,!0),256&c)t.ctx.deactivate(e);else{var f,p=1&c&&h,v=!Co(e);if(v&&(f=o&&o.onVnodeBeforeUnmount)&&Ks(f,t,e),6&c)j(e.component,n,r);else{if(128&c)return void e.suspense.unmount(n,r);p&&qo(e,null,t,"beforeUnmount"),64&c?e.type.remove(e,t,n,i,q,r):u&&(a!==As||d>0&&64&d)?W(u,t,n,!1,!0):(a===As&&384&d||!i&&16&c)&&W(l,t,n),r&&$(e)}(v&&(f=o&&o.onVnodeUnmounted)||p)&&Cs((()=>{f&&Ks(f,t,e),p&&qo(e,null,t,"unmounted")}),n)}},$=e=>{var{type:t,el:n,anchor:r,transition:i}=e;if(t!==As)if(t!==Rs){var o=()=>{a(n),i&&!i.persisted&&i.afterLeave&&i.afterLeave()};if(1&e.shapeFlag&&i&&!i.persisted){var{leave:s,delayLeave:l}=i,u=()=>s(n,o);l?l(e.el,o,u):u()}else o()}else w(e);else V(n,r)},V=(e,t)=>{for(var n;e!==t;)n=f(e),a(e),e=n;a(t)},j=(e,t,n)=>{var{bum:r,scope:i,update:a,subTree:o,um:s}=e;r&&zn(r),i.stop(),a&&(a.active=!1,F(o,e,t,n)),s&&Cs(s,t),Cs((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},W=function(e,t,n){for(var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;a<e.length;a++)F(e[a],t,n,r,i)},U=e=>6&e.shapeFlag?U(e.component.subTree):128&e.shapeFlag?e.suspense.next():f(e.anchor||e.el),H=(e,t,n)=>{if(null==e)t._vnode&&F(t._vnode,null,null,!0);else{var r=t.__vueParent;g(t._vnode||null,e,t,null,r,null,n)}to(),t._vnode=e},q={p:g,um:F,m:z,r:$,mt:L,mc:T,pc:R,pbc:C,n:U,o:e};t&&([n,r]=t(q));return{render:H,hydrate:n,createApp:Ts(H,n)}}(e)}function Os(e,t){var{effect:n,update:r}=e;n.allowRecurse=r.allowRecurse=t}function Is(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=e.children,i=t.children;if(gn(r)&&gn(i))for(var a=0;a<r.length;a++){var o=r[a],s=i[a];1&s.shapeFlag&&!s.dynamicChildren&&((s.patchFlag<=0||32===s.patchFlag)&&((s=i[a]=Xs(i[a])).el=o.el),n||Is(o,s)),s.type===Bs&&(s.el=o.el)}}var Ls=e=>e.__isTeleport,As=Symbol(void 0),Bs=Symbol(void 0),Ns=Symbol(void 0),Rs=Symbol(void 0);function Ps(e){e}function Ds(e){return!!e&&!0===e.__v_isVNode}function zs(e,t){return e.type===t.type&&e.key===t.key}var Fs="__vInternal",$s=e=>{var{key:t}=e;return null!=t?t:null},Vs=e=>{var{ref:t,ref_key:n,ref_for:r}=e;return null!=t?wn(t)||La(t)||bn(t)?{i:uo,r:t,k:n,f:!!r}:t:null};function js(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e===As?0:1,o=arguments.length>7&&void 0!==arguments[7]&&arguments[7],s={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&$s(t),ref:t&&Vs(t),scopeId:co,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:a,patchFlag:r,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:uo};return o?(Zs(s,n),128&a&&e.normalize(s)):n&&(s.shapeFlag|=wn(n)?8:16),s}var Ws=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,a=arguments.length>5&&void 0!==arguments[5]&&arguments[5];e&&e!==Yo||(e=Ns);if(Ds(e)){var o=Hs(e,t,!0);return n&&Zs(o,n),o.patchFlag|=-2,o}cl(e)&&(e=e.__vccOpts);if(t){t=Us(t);var{class:s,style:l}=t;s&&!wn(s)&&(t.class=Gt(s)),Sn(l)&&(ka(l)&&!gn(l)&&(l=hn({},l)),t.style=Ht(l))}var u=wn(e)?1:mo(e)?128:Ls(e)?64:Sn(e)?4:bn(e)?2:0;return js(e,t,n,r,i,u,a,!0)};function Us(e){return e?ka(e)||Fs in e?hn({},e):e:null}function Hs(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],{props:r,ref:i,patchFlag:a,children:o}=e,s=t?Gs(r||{},t):r,l={__v_isVNode:!0,__v_skip:!0,type:e.type,props:s,key:s&&$s(s),ref:t&&t.ref?n&&i?gn(i)?i.concat(Vs(t)):[i,Vs(t)]:Vs(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:o,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==As?-1===a?16:16|a:a,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Hs(e.ssContent),ssFallback:e.ssFallback&&Hs(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l}function qs(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return Ws(Bs,null,e,t)}function Ys(e){return null==e||"boolean"==typeof e?Ws(Ns):gn(e)?Ws(As,null,e.slice()):"object"==typeof e?Xs(e):Ws(Bs,null,String(e))}function Xs(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:Hs(e)}function Zs(e,t){var n=0,{shapeFlag:r}=e;if(null==t)t=null;else if(gn(t))n=16;else if("object"==typeof t){if(65&r){var i=t.default;return void(i&&(i._c&&(i._d=!1),Zs(e,i()),i._c&&(i._d=!0)))}n=32;var a=t._;a||Fs in t?3===a&&uo&&(1===uo.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=uo}else bn(t)?(t={default:t,_ctx:uo},n=32):(t=String(t),64&r?(n=16,t=[qs(t)]):n=8);e.children=t,e.shapeFlag|=n}function Gs(){for(var e={},t=0;t<arguments.length;t++){var n=t<0||arguments.length<=t?void 0:arguments[t];for(var r in n)if("class"===r)e.class!==n.class&&(e.class=Gt([e.class,n.class]));else if("style"===r)e.style=Ht([e.style,n.style]);else if(cn(r)){var i=e[r],a=n[r];!a||i===a||gn(i)&&i.includes(a)||(e[r]=i?[].concat(i,a):a)}else""!==r&&(e[r]=n[r])}return e}function Ks(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;Va(e,t,7,[n,r])}var Js=Ss(),Qs=0;var el=null,tl=()=>el||uo,nl=e=>{el=e,e.scope.on()},rl=()=>{el&&el.scope.off(),el=null};function il(e){return 4&e.vnode.shapeFlag}var al=!1;function ol(e,t){var n=e.type;e.accessCache=Object.create(null),e.proxy=Ea(new Proxy(e.ctx,Ko));var{setup:r}=n;if(r){var i=e.setupContext=r.length>1?function(e){var t,n=t=>{e.exposed=t||{}};return{get attrs(){return t||(t=function(e){return new Proxy(e.attrs,{get:(t,n)=>(Ci(e,0,"$attrs"),t[n])})}(e))},slots:e.slots,emit:e.emit,expose:n}}(e):null;nl(e),Ti();var a=$a(r,e,0,[e.props,i]);if(Ei(),rl(),kn(a)){if(a.then(rl,rl),t)return a.then((n=>{sl(e,n,t)})).catch((t=>{ja(t,e,0)}));e.asyncDep=a}else sl(e,a,t)}else ll(e,t)}function sl(e,t,n){bn(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Sn(t)&&(e.setupState=za(t)),ll(e,n)}function ll(e,t,n){var r=e.type;e.render||(e.render=r.render||sn);nl(e),Ti(),Qo(e),Ei(),rl()}function ul(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(za(Ea(e.exposed)),{get:(t,n)=>n in t?t[n]:n in Zo?Zo[n](e):void 0,has:(e,t)=>t in e||t in Zo}))}function cl(e){return bn(e)&&"__vccOpts"in e}var dl=(e,t)=>function(e,t){var n,r,i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=bn(e);return a?(n=e,r=sn):(n=e.get,r=e.set),new Fa(n,r,a||!r,i)}(e,t,al);function hl(e,t,n){var r=arguments.length;return 2===r?Sn(t)&&!gn(t)?Ds(t)?Ws(e,null,[t]):Ws(e,t):Ws(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):3===r&&Ds(n)&&(n=[n]),Ws(e,t,n))}var fl=Symbol(""),pl=()=>yo(fl),vl="3.2.47",gl="undefined"!=typeof document?document:null,ml=gl&&gl.createElement("template"),_l={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{var t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{var i=t?gl.createElementNS("http://www.w3.org/2000/svg",e):gl.createElement(e,n?{is:n}:void 0);return"select"===e&&r&&null!=r.multiple&&i.setAttribute("multiple",r.multiple),i},createText:e=>gl.createTextNode(e),createComment:e=>gl.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>gl.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,i,a){var o=n?n.previousSibling:t.lastChild;if(i&&(i===a||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),n),i!==a&&(i=i.nextSibling););else{ml.innerHTML=r?"<svg>".concat(e,"</svg>"):e;var s=ml.content;if(r){for(var l=s.firstChild;l.firstChild;)s.appendChild(l.firstChild);s.removeChild(l)}t.insertBefore(s,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}};function yl(e,t,n){var r=e._vtc;r&&(t=(t?[t,...r]:[...r]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}function bl(e,t,n){var r=e.style,i=wn(n);if(n&&!i){if(t&&!wn(t))for(var a in t)null==n[a]&&xl(r,a,"");for(var o in n)xl(r,o,n[o])}else{var s=r.display;i?t!==n&&(r.cssText=normalizeStyleValue(n)):t&&e.removeAttribute("style"),"_vod"in e&&(r.display=s)}}var wl=/\s*!important$/;function xl(e,t,n){if(gn(n))n.forEach((n=>xl(e,t,n)));else if(null==n&&(n=""),n=normalizeStyleValue(n),t.startsWith("--"))e.setProperty(t,n);else{var r=normalizeStyleName(e,t);wl.test(n)?e.setProperty(Nn(r),n.replace(wl,""),"important"):e[r]=n}}var Sl="http://www.w3.org/1999/xlink";function kl(e,t,n,r,i){if(r&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(Sl,t.slice(6,t.length)):e.setAttributeNS(Sl,t,n);else{var a=Kt(t);null==n||a&&!Jt(n)?e.removeAttribute(t):e.setAttribute(t,a?"":n)}}function Tl(e,t,n,r,i,a,o){if("innerHTML"===t||"textContent"===t)return r&&o(r,i,a),void(e[t]=null==n?"":n);if("value"===t&&"PROGRESS"!==e.tagName&&!e.tagName.includes("-")){e._value=n;var s=null==n?"":n;return e.value===s&&"OPTION"!==e.tagName||(e.value=s),void(null==n&&e.removeAttribute(t))}var l=!1;if(""===n||null==n){var u=typeof e[t];"boolean"===u?n=Jt(n):null==n&&"string"===u?(n="",l=!0):"number"===u&&(n=0,l=!0)}try{e[t]=n}catch(c){}l&&e.removeAttribute(t)}function El(e,t,n,r){e.addEventListener(t,n,r)}function Cl(e,t,n,r){e.removeEventListener(t,n,r)}function Ml(e,t,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,a=e._vei||(e._vei={}),o=a[t];if(r&&o)o.value=r;else{var[s,l]=Il(t);if(r){var u=a[t]=Bl(r,i);El(e,s,u,l)}else o&&(Cl(e,s,o,l),a[t]=void 0)}}var Ol=/(?:Once|Passive|Capture)$/;function Il(e){var t,n;if(Ol.test(e))for(t={};n=e.match(Ol);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0;return[":"===e[2]?e.slice(3):Nn(e.slice(2)),t]}var Ll=0,Al=Promise.resolve();function Bl(e,t){var n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();Va(function(e,t){if(gn(t)){var n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>t=>!t._stopped&&e&&e(t)))}return t}(e,n.value),t,5,[e])};return n.value=e,n.attached=Ll||(Al.then((()=>Ll=0)),Ll=Date.now()),n}var Nl=/^on[a-z]/;function Rl(e,t,n,r){return r?"innerHTML"===t||"textContent"===t||!!(t in e&&Nl.test(t)&&bn(n)):"spellcheck"!==t&&"draggable"!==t&&"translate"!==t&&("form"!==t&&(("list"!==t||"INPUT"!==e.tagName)&&(("type"!==t||"TEXTAREA"!==e.tagName)&&((!Nl.test(t)||!wn(n))&&t in e))))}var Pl=e=>{var t=e.props["onUpdate:modelValue"]||!1;return gn(t)?e=>zn(t,e):t};function Dl(e){e.target.composing=!0}function zl(e){var t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}var Fl={created(e,t,n){var{modifiers:{lazy:r,trim:i,number:a}}=t;e._assign=Pl(n);var o=a||n.props&&"number"===n.props.type;El(e,r?"change":"input",(t=>{if(!t.target.composing){var n=e.value;i&&(n=n.trim()),o&&(n=$n(n)),e._assign(n)}})),i&&El(e,"change",(()=>{e.value=e.value.trim()})),r||(El(e,"compositionstart",Dl),El(e,"compositionend",zl),El(e,"change",zl))},mounted(e,t){var{value:n}=t;e.value=null==n?"":n},beforeUpdate(e,t,n){var{value:r,modifiers:{lazy:i,trim:a,number:o}}=t;if(e._assign=Pl(n),!e.composing){if(document.activeElement===e&&"range"!==e.type){if(i)return;if(a&&e.value.trim()===r)return;if((o||"number"===e.type)&&$n(e.value)===r)return}var s=null==r?"":r;e.value!==s&&(e.value=s)}}},$l={deep:!0,created(e,t,n){e._assign=Pl(n),El(e,"change",(()=>{var t=e._modelValue,n=Hl(e),r=e.checked,i=e._assign;if(gn(t)){var a=en(t,n),o=-1!==a;if(r&&!o)i(t.concat(n));else if(!r&&o){var s=[...t];s.splice(a,1),i(s)}}else if(_n(t)){var l=new Set(t);r?l.add(n):l.delete(n),i(l)}else i(ql(e,r))}))},mounted:Vl,beforeUpdate(e,t,n){e._assign=Pl(n),Vl(e,t,n)}};function Vl(e,t,n){var{value:r,oldValue:i}=t;e._modelValue=r,gn(r)?e.checked=en(r,n.props.value)>-1:_n(r)?e.checked=r.has(n.props.value):r!==i&&(e.checked=Qt(r,ql(e,!0)))}var jl={created(e,t,n){var{value:r}=t;e.checked=Qt(r,n.props.value),e._assign=Pl(n),El(e,"change",(()=>{e._assign(Hl(e))}))},beforeUpdate(e,t,n){var{value:r,oldValue:i}=t;e._assign=Pl(n),r!==i&&(e.checked=Qt(r,n.props.value))}},Wl={deep:!0,created(e,t,n){var{value:r,modifiers:{number:i}}=t,a=_n(r);El(e,"change",(()=>{var t=Array.prototype.filter.call(e.options,(e=>e.selected)).map((e=>i?$n(Hl(e)):Hl(e)));e._assign(e.multiple?a?new Set(t):t:t[0])})),e._assign=Pl(n)},mounted(e,t){var{value:n}=t;Ul(e,n)},beforeUpdate(e,t,n){e._assign=Pl(n)},updated(e,t){var{value:n}=t;Ul(e,n)}};function Ul(e,t){var n=e.multiple;if(!n||gn(t)||_n(t)){for(var r=0,i=e.options.length;r<i;r++){var a=e.options[r],o=Hl(a);if(n)gn(t)?a.selected=en(t,o)>-1:a.selected=t.has(o);else if(Qt(Hl(a),t))return void(e.selectedIndex!==r&&(e.selectedIndex=r))}n||-1===e.selectedIndex||(e.selectedIndex=-1)}}function Hl(e){return"_value"in e?e._value:e.value}function ql(e,t){var n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}var Yl={created(e,t,n){Xl(e,t,n,null,"created")},mounted(e,t,n){Xl(e,t,n,null,"mounted")},beforeUpdate(e,t,n,r){Xl(e,t,n,r,"beforeUpdate")},updated(e,t,n,r){Xl(e,t,n,r,"updated")}};function Xl(e,t,n,r,i){var a=function(e,t){switch(e){case"SELECT":return Wl;case"TEXTAREA":return Fl;default:switch(t){case"checkbox":return $l;case"radio":return jl;default:return Fl}}}(e.tagName,n.props&&n.props.type)[i];a&&a(e,t,n,r)}var Zl=["ctrl","shift","alt","meta"],Gl={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>Zl.some((n=>e["".concat(n,"Key")]&&!t.includes(n)))},Kl=(e,t)=>function(n){for(var r=0;r<t.length;r++){var i=Gl[t[r]];if(i&&i(n,t))return}for(var a=arguments.length,o=new Array(a>1?a-1:0),s=1;s<a;s++)o[s-1]=arguments[s];return e(n,...o)},Jl={beforeMount(e,t,n){var{value:r}=t,{transition:i}=n;e._vod="none"===e.style.display?"":e.style.display,i&&r?i.beforeEnter(e):Ql(e,r)},mounted(e,t,n){var{value:r}=t,{transition:i}=n;i&&r&&i.enter(e)},updated(e,t,n){var{value:r,oldValue:i}=t,{transition:a}=n;!r!=!i&&(a?r?(a.beforeEnter(e),Ql(e,!0),a.enter(e)):a.leave(e,(()=>{Ql(e,!1)})):Ql(e,r))},beforeUnmount(e,t){var{value:n}=t;Ql(e,n)}};function Ql(e,t){e.style.display=t?e._vod:"none"}var eu,tu=hn({patchProp:function(e,t,n,r){var i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],a=arguments.length>5?arguments[5]:void 0,o=arguments.length>6?arguments[6]:void 0,s=arguments.length>7?arguments[7]:void 0,l=arguments.length>8?arguments[8]:void 0;"class"===t?yl(e,r,i):"style"===t?bl(e,n,r):cn(t)?dn(t)||Ml(e,t,n,r,o):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):Rl(e,t,r,i))?Tl(e,t,r,a,o,s,l):("true-value"===t?e._trueValue=r:"false-value"===t&&(e._falseValue=r),kl(e,t,r,i))}},_l);function nu(){return eu||(eu=Ms(tu))}var ru=function(){var e=nu().createApp(...arguments),{mount:t}=e;return e.mount=n=>{var r=iu(n);if(r){var i=e._component;bn(i)||i.render||i.template||(i.template=r.innerHTML),r.innerHTML="";var a=t(r,!1,r instanceof SVGElement);return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),a}},e};function iu(e){return wn(e)?document.querySelector(e):e}var au,ou,su=["top","left","right","bottom"],lu={};function uu(){return ou="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function cu(){if(ou="string"==typeof ou?ou:uu()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(s){}var r=document.createElement("div");i(r,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),su.forEach((function(e){o(r,e)})),document.body.appendChild(r),a(),au=!0}else su.forEach((function(e){lu[e]=0}));function i(e,t){var n=e.style;Object.keys(t).forEach((function(e){var r=t[e];n[e]=r}))}function a(t){t?e.push(t):e.forEach((function(e){e()}))}function o(e,n){var r=document.createElement("div"),o=document.createElement("div"),s=document.createElement("div"),l=document.createElement("div"),u={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:ou+"(safe-area-inset-"+n+")"};i(r,u),i(o,u),i(s,{transition:"0s",animation:"none",width:"400px",height:"400px"}),i(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),r.appendChild(s),o.appendChild(l),e.appendChild(r),e.appendChild(o),a((function(){r.scrollTop=o.scrollTop=1e4;var e=r.scrollTop,i=o.scrollTop;function a(){this.scrollTop!==(this===r?e:i)&&(r.scrollTop=o.scrollTop=1e4,e=r.scrollTop,i=o.scrollTop,function(e){hu.length||setTimeout((function(){var e={};hu.forEach((function(t){e[t]=lu[t]})),hu.length=0,fu.forEach((function(t){t(e)}))}),0);hu.push(e)}(n))}r.addEventListener("scroll",a,t),o.addEventListener("scroll",a,t)}));var c=getComputedStyle(r);Object.defineProperty(lu,n,{configurable:!0,get:function(){return parseFloat(c.paddingBottom)}})}}function du(e){return au||cu(),lu[e]}var hu=[];var fu=[];const pu=e({get support(){return 0!=("string"==typeof ou?ou:uu()).length},get top(){return du("top")},get left(){return du("left")},get right(){return du("right")},get bottom(){return du("bottom")},onChange:function(e){uu()&&(au||cu(),"function"==typeof e&&fu.push(e))},offChange:function(e){var t=fu.indexOf(e);t>=0&&fu.splice(t,1)}});var vu=Kl((()=>{}),["prevent"]);function gu(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function mu(){var e=gu(document.documentElement.style,"--window-top");return e?e+pu.top:0}function _u(e){return Symbol(e)}function yu(e){return-1!==(e+="").indexOf("rpx")||-1!==e.indexOf("upx")}function bu(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t)return wu(e);if(wn(e)){var n=parseInt(e)||0;return yu(e)?uni.upx2px(n):n}return e}function wu(e){return yu(e)?e.replace(/(\d+(\.\d+)?)[ru]px/g,((e,t)=>uni.upx2px(parseFloat(t))+"px")):e}var xu,Su="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z";function ku(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"#000",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:27;return Ws("svg",{width:n,height:n,viewBox:"0 0 32 32"},[Ws("path",{d:e,fill:t},null,8,["d","fill"])],8,["width","height"])}function Tu(){return Eu()}function Eu(){return window.__id__||(window.__id__=plus.webview.currentWebview().id),parseInt(window.__id__)}function Cu(e){e.preventDefault()}var Mu,Ou,Iu,Lu,Au,Bu=0;function Nu(e){var{onPageScroll:t,onReachBottom:n,onReachBottomDistance:r}=e,i=!1,a=!1,o=!0,s=()=>{function e(){if((()=>{var{scrollHeight:e}=document.documentElement,t=window.innerHeight,n=window.scrollY,i=n>0&&e>t&&n+t+r>=e,o=Math.abs(e-Bu)>r;return!i||a&&!o?(!i&&a&&(a=!1),!1):(Bu=e,a=!0,!0)})())return n&&n(),o=!1,setTimeout((function(){o=!0}),350),!0}t&&t(window.pageYOffset),n&&o&&(e()||(xu=setTimeout(e,300))),i=!1};return function(){clearTimeout(xu),i||requestAnimationFrame(s),i=!0}}function Ru(e,t){if(0===t.indexOf("/"))return t;if(0===t.indexOf("./"))return Ru(e,t.slice(2));for(var n=t.split("/"),r=n.length,i=0;i<r&&".."===n[i];i++);n.splice(0,i),t=n.join("/");var a=e.length>0?e.split("/"):[];return a.splice(a.length-i-1,i+1),nr(a.concat(n).join("/"))}function Pu(){return"object"==typeof window&&"object"==typeof navigator&&"object"==typeof document?"webview":"v8"}function Du(){return Mu.webview.currentWebview().id}var zu={};function Fu(e){var t=e.data&&e.data.__message;if(t&&t.__page){var n=t.__page,r=zu[n];r&&r(t),t.keep||delete zu[n]}}class $u{constructor(e){this.webview=e}sendMessage(e){var t=JSON.parse(JSON.stringify({__message:{data:e}})),n=this.webview.id;Iu?new Iu(n).postMessage(t):Mu.webview.postMessageToUniNView&&Mu.webview.postMessageToUniNView(t,n)}close(){this.webview.close()}}function Vu(e){var{context:t={},url:n,data:r={},style:i={},onMessage:a,onClose:o}=e,s=__uniConfig.darkmode;Mu=t.plus||plus,Ou=t.weex||("object"==typeof weex?weex:null),Iu=t.BroadcastChannel||("object"==typeof BroadcastChannel?BroadcastChannel:null);var l="page".concat(Date.now());!1!==(i=hn({},i)).titleNView&&"none"!==i.titleNView&&(i.titleNView=hn({autoBackButton:!0,titleSize:"17px"},i.titleNView));var u={top:0,bottom:0,usingComponents:{},popGesture:"close",scrollIndicator:"none",animationType:"pop-in",animationDuration:200,uniNView:{path:"/".concat(n,".js"),defaultFontSize:16,viewport:Mu.screen.resolutionWidth}};i=hn(u,i);var c=Mu.webview.create("",l,i,{extras:{from:Du(),runtime:Pu(),data:hn({},r,{darkmode:s}),useGlobalEvent:!Iu}});return c.addEventListener("close",o),function(e,t){"v8"===Pu()?Iu?(Lu&&Lu.close(),(Lu=new Iu(Du())).onmessage=Fu):Au||(Au=Ou.requireModule("globalEvent")).addEventListener("plusMessage",Fu):window.__plusMessage=Fu,zu[e]=t}(l,(e=>{bn(a)&&a(e.data),e.keep||c.close("auto")})),c.show(i.animationType,i.animationDuration),new $u(c)}class ju{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=e.$el,this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(this.$el&&e){var t=Hu(this.$el.querySelector(e));if(t)return Wu(t)}}selectAllComponents(e){if(!this.$el||!e)return[];for(var t=[],n=this.$el.querySelectorAll(e),r=0;r<n.length;r++){var i=Hu(n[r]);i&&t.push(Wu(i))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){var{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){var{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){var t="";if(!e||wn(e))return t;for(var n in e){var r=e[n],i=n.startsWith("--")?n:Nn(n);(wn(r)||"number"==typeof r)&&(t+="".concat(i,":").concat(r,";"))}return t}(e))}setStyle(e){return this.$el&&e?(wn(e)&&(e=Zt(e)),Cn(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;var t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;var{__wxsAddClass:t}=this.$el;if(t){var n=t.indexOf(e);n>-1&&t.splice(n,1)}var r=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===r.indexOf(e)&&(r.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this.$vm[e];bn(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&UniViewJSBridge.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){var t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce(((e,n)=>(e[n]=t[n],e)),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function Wu(e){if(e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new ju(e)),e.$el.__wxsComponentDescriptor}function Uu(e,t){return Wu(e)}function Hu(e){if(e)return qu(e)}function qu(e){return e.__wxsVm||(e.__wxsVm={ownerId:e.__ownerId,$el:e,$emit(){},$forceUpdate(){var t,n,{__wxsStyle:r,__wxsAddClass:i,__wxsRemoveClass:a,__wxsStyleChanged:o,__wxsClassChanged:s}=e;o&&(e.__wxsStyleChanged=!1,r&&(n=()=>{Object.keys(r).forEach((t=>{e.style[t]=r[t]}))})),s&&(e.__wxsClassChanged=!1,t=()=>{a&&a.forEach((t=>{e.classList.remove(t)})),i&&i.forEach((t=>{e.classList.add(t)}))}),requestAnimationFrame((()=>{t&&t(),n&&n()}))}})}function Yu(e,t,n){var{currentTarget:r}=e;if(!(e instanceof Event&&r instanceof HTMLElement))return[e];var i=0!==r.tagName.indexOf("UNI-"),a=Zu(e,i);if("click"===e.type)!function(e,t){var{x:n,y:r}=t,i=mu();e.detail={x:n,y:r-i},e.touches=e.changedTouches=[Gu(t,i)]}(a,e);else if((e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type))(e))!function(e,t){var n=mu();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[Gu(t,n)]}(a,e);else if((e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch")||["longpress"].indexOf(e.type)>=0)(e)){var o=mu();a.touches=Ku(e.touches,o),a.changedTouches=Ku(e.changedTouches,o)}else if((e=>!e.type.indexOf("key")&&e instanceof KeyboardEvent)(e)){["key","code"].forEach((t=>{Object.defineProperty(a,t,{get:()=>e[t]})}))}return[a]}function Xu(e){for(;e&&0!==e.tagName.indexOf("UNI-");)e=e.parentElement;return e}function Zu(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],{type:n,timeStamp:r,target:i,currentTarget:a}=e,o={type:n,timeStamp:r,target:cr(t?i:Xu(i)),detail:{},currentTarget:cr(a)};return e._stopped&&(o._stopped=!0),e.type.startsWith("touch")&&(o.touches=e.touches,o.changedTouches=e.changedTouches),o}function Gu(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function Ku(e,t){for(var n=[],r=0;r<e.length;r++){var{identifier:i,pageX:a,pageY:o,clientX:s,clientY:l,force:u}=e[r];n.push({identifier:i,pageX:a,pageY:o-t,clientX:s,clientY:l-t,force:u||0})}return n}var Ju="vdSync",Qu="__uniapp__service",ec="onWebviewReady",tc=hn(Qr,{publishHandler:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=Eu()+"";plus.webview.postMessageToUniNView({type:"subscribeHandler",args:{type:e,data:t,pageId:n}},Qu)}});function nc(e,t,n,r){if(r&&r.beforeInvoke){var i=r.beforeInvoke(t);if(wn(i))return i}var a=function(e,t){var n=e[0];if(t&&(Cn(t.formatArgs)||!Cn(n)))for(var r=t.formatArgs,i=Object.keys(r),a=0;a<i.length;a++){var o=i[a],s=r[o];if(bn(s)){var l=s(e[0][o],n);if(wn(l))return l}else vn(n,o)||(n[o]=s)}}(t,r);if(a)return a}function rc(e,t,n,r){return function(e,t,n,r){return function(){for(var e=arguments.length,n=new Array(e),i=0;i<e;i++)n[i]=arguments[i];var a=nc(0,n,0,r);if(a)throw new Error(a);return t.apply(null,n)}}(0,t,0,r)}function ic(){if("undefined"!=typeof __SYSTEM_INFO__)return window.__SYSTEM_INFO__;var{resolutionWidth:e}=plus.screen.getCurrentSize()||{resolutionWidth:0};return{platform:(plus.os.name||"").toLowerCase(),pixelRatio:plus.screen.scale,windowWidth:Math.round(e)}}function ac(e){if(0===e.indexOf("//"))return"https:"+e;if(Zn.test(e)||Gn.test(e))return e;if(function(e){if(0===e.indexOf("_www")||0===e.indexOf("_doc")||0===e.indexOf("_documents")||0===e.indexOf("_downloads"))return!0;return!1}(e))return"file://"+oc(e);var t="file://"+oc("_www");if(0===e.indexOf("/"))return e.startsWith("/storage/")||e.startsWith("/sdcard/")||e.includes("/Containers/Data/Application/")?"file://"+e:t+e;if(0===e.indexOf("../")||0===e.indexOf("./")){if("string"==typeof __id__)return t+Ru(nr(__id__),e);var n=window.__PAGE_INFO__;if(n)return t+Ru(nr(n.route),e)}return e}var oc=function(e){var t=Object.create(null);return n=>t[n]||(t[n]=e(n))}((e=>plus.io.convertLocalFileSystemURL(e).replace(/^\/?apps\//,"/android_asset/apps/").replace(/\/$/,"")));var sc=0;function lc(e){return function(e){return new Promise((function(t,n){0===e.indexOf("http://")||0===e.indexOf("https://")?plus.downloader.createDownload(e,{filename:"".concat("_doc/uniapp_temp","/download/")},(function(e,r){200===r?t(e.filename):n(new Error("network fail"))})).start():t(e)}))}(e).then((function(e){var t,n=window;return n.webkit&&n.webkit.messageHandlers?(t=e,new Promise((function(e,n){function r(){var r=new plus.nativeObj.Bitmap("bitmap_".concat(Date.now(),"_").concat(Math.random(),"}"));r.load(t,(function(){e(r.toBase64Data()),r.clear()}),(function(e){r.clear(),n(e)}))}plus.io.resolveLocalFileSystemURL(t,(function(t){t.file((function(t){var n=new plus.io.FileReader;n.onload=function(t){e(t.target.result)},n.onerror=r,n.readAsDataURL(t)}),r)}),r)}))):plus.io.convertLocalFileSystemURL(e)}))}var uc={};!function(e){var t="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;function n(e,t){return Object.prototype.hasOwnProperty.call(e,t)}e.assign=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var r=t.shift();if(r){if("object"!=typeof r)throw new TypeError(r+"must be non-object");for(var i in r)n(r,i)&&(e[i]=r[i])}}return e},e.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)};var r={arraySet:function(e,t,n,r,i){if(t.subarray&&e.subarray)e.set(t.subarray(n,n+r),i);else for(var a=0;a<r;a++)e[i+a]=t[n+a]},flattenChunks:function(e){var t,n,r,i,a,o;for(r=0,t=0,n=e.length;t<n;t++)r+=e[t].length;for(o=new Uint8Array(r),i=0,t=0,n=e.length;t<n;t++)a=e[t],o.set(a,i),i+=a.length;return o}},i={arraySet:function(e,t,n,r,i){for(var a=0;a<r;a++)e[i+a]=t[n+a]},flattenChunks:function(e){return[].concat.apply([],e)}};e.setTyped=function(t){t?(e.Buf8=Uint8Array,e.Buf16=Uint16Array,e.Buf32=Int32Array,e.assign(e,r)):(e.Buf8=Array,e.Buf16=Array,e.Buf32=Array,e.assign(e,i))},e.setTyped(t)}(uc);var cc={},dc={},hc={},fc=uc;function pc(e){for(var t=e.length;--t>=0;)e[t]=0}var vc=256,gc=286,mc=30,_c=15,yc=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],bc=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],wc=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],xc=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],Sc=new Array(576);pc(Sc);var kc=new Array(60);pc(kc);var Tc=new Array(512);pc(Tc);var Ec=new Array(256);pc(Ec);var Cc=new Array(29);pc(Cc);var Mc,Oc,Ic,Lc=new Array(mc);function Ac(e,t,n,r,i){this.static_tree=e,this.extra_bits=t,this.extra_base=n,this.elems=r,this.max_length=i,this.has_stree=e&&e.length}function Bc(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}function Nc(e){return e<256?Tc[e]:Tc[256+(e>>>7)]}function Rc(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255}function Pc(e,t,n){e.bi_valid>16-n?(e.bi_buf|=t<<e.bi_valid&65535,Rc(e,e.bi_buf),e.bi_buf=t>>16-e.bi_valid,e.bi_valid+=n-16):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=n)}function Dc(e,t,n){Pc(e,n[2*t],n[2*t+1])}function zc(e,t){var n=0;do{n|=1&e,e>>>=1,n<<=1}while(--t>0);return n>>>1}function Fc(e,t,n){var r,i,a=new Array(16),o=0;for(r=1;r<=_c;r++)a[r]=o=o+n[r-1]<<1;for(i=0;i<=t;i++){var s=e[2*i+1];0!==s&&(e[2*i]=zc(a[s]++,s))}}function $c(e){var t;for(t=0;t<gc;t++)e.dyn_ltree[2*t]=0;for(t=0;t<mc;t++)e.dyn_dtree[2*t]=0;for(t=0;t<19;t++)e.bl_tree[2*t]=0;e.dyn_ltree[512]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function Vc(e){e.bi_valid>8?Rc(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function jc(e,t,n,r){var i=2*t,a=2*n;return e[i]<e[a]||e[i]===e[a]&&r[t]<=r[n]}function Wc(e,t,n){for(var r=e.heap[n],i=n<<1;i<=e.heap_len&&(i<e.heap_len&&jc(t,e.heap[i+1],e.heap[i],e.depth)&&i++,!jc(t,r,e.heap[i],e.depth));)e.heap[n]=e.heap[i],n=i,i<<=1;e.heap[n]=r}function Uc(e,t,n){var r,i,a,o,s=0;if(0!==e.last_lit)do{r=e.pending_buf[e.d_buf+2*s]<<8|e.pending_buf[e.d_buf+2*s+1],i=e.pending_buf[e.l_buf+s],s++,0===r?Dc(e,i,t):(Dc(e,(a=Ec[i])+vc+1,t),0!==(o=yc[a])&&Pc(e,i-=Cc[a],o),Dc(e,a=Nc(--r),n),0!==(o=bc[a])&&Pc(e,r-=Lc[a],o))}while(s<e.last_lit);Dc(e,256,t)}function Hc(e,t){var n,r,i,a=t.dyn_tree,o=t.stat_desc.static_tree,s=t.stat_desc.has_stree,l=t.stat_desc.elems,u=-1;for(e.heap_len=0,e.heap_max=573,n=0;n<l;n++)0!==a[2*n]?(e.heap[++e.heap_len]=u=n,e.depth[n]=0):a[2*n+1]=0;for(;e.heap_len<2;)a[2*(i=e.heap[++e.heap_len]=u<2?++u:0)]=1,e.depth[i]=0,e.opt_len--,s&&(e.static_len-=o[2*i+1]);for(t.max_code=u,n=e.heap_len>>1;n>=1;n--)Wc(e,a,n);i=l;do{n=e.heap[1],e.heap[1]=e.heap[e.heap_len--],Wc(e,a,1),r=e.heap[1],e.heap[--e.heap_max]=n,e.heap[--e.heap_max]=r,a[2*i]=a[2*n]+a[2*r],e.depth[i]=(e.depth[n]>=e.depth[r]?e.depth[n]:e.depth[r])+1,a[2*n+1]=a[2*r+1]=i,e.heap[1]=i++,Wc(e,a,1)}while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],function(e,t){var n,r,i,a,o,s,l=t.dyn_tree,u=t.max_code,c=t.stat_desc.static_tree,d=t.stat_desc.has_stree,h=t.stat_desc.extra_bits,f=t.stat_desc.extra_base,p=t.stat_desc.max_length,v=0;for(a=0;a<=_c;a++)e.bl_count[a]=0;for(l[2*e.heap[e.heap_max]+1]=0,n=e.heap_max+1;n<573;n++)(a=l[2*l[2*(r=e.heap[n])+1]+1]+1)>p&&(a=p,v++),l[2*r+1]=a,r>u||(e.bl_count[a]++,o=0,r>=f&&(o=h[r-f]),s=l[2*r],e.opt_len+=s*(a+o),d&&(e.static_len+=s*(c[2*r+1]+o)));if(0!==v){do{for(a=p-1;0===e.bl_count[a];)a--;e.bl_count[a]--,e.bl_count[a+1]+=2,e.bl_count[p]--,v-=2}while(v>0);for(a=p;0!==a;a--)for(r=e.bl_count[a];0!==r;)(i=e.heap[--n])>u||(l[2*i+1]!==a&&(e.opt_len+=(a-l[2*i+1])*l[2*i],l[2*i+1]=a),r--)}}(e,t),Fc(a,u,e.bl_count)}function qc(e,t,n){var r,i,a=-1,o=t[1],s=0,l=7,u=4;for(0===o&&(l=138,u=3),t[2*(n+1)+1]=65535,r=0;r<=n;r++)i=o,o=t[2*(r+1)+1],++s<l&&i===o||(s<u?e.bl_tree[2*i]+=s:0!==i?(i!==a&&e.bl_tree[2*i]++,e.bl_tree[32]++):s<=10?e.bl_tree[34]++:e.bl_tree[36]++,s=0,a=i,0===o?(l=138,u=3):i===o?(l=6,u=3):(l=7,u=4))}function Yc(e,t,n){var r,i,a=-1,o=t[1],s=0,l=7,u=4;for(0===o&&(l=138,u=3),r=0;r<=n;r++)if(i=o,o=t[2*(r+1)+1],!(++s<l&&i===o)){if(s<u)do{Dc(e,i,e.bl_tree)}while(0!=--s);else 0!==i?(i!==a&&(Dc(e,i,e.bl_tree),s--),Dc(e,16,e.bl_tree),Pc(e,s-3,2)):s<=10?(Dc(e,17,e.bl_tree),Pc(e,s-3,3)):(Dc(e,18,e.bl_tree),Pc(e,s-11,7));s=0,a=i,0===o?(l=138,u=3):i===o?(l=6,u=3):(l=7,u=4)}}pc(Lc);var Xc=!1;function Zc(e,t,n,r){Pc(e,0+(r?1:0),3),function(e,t,n,r){Vc(e),r&&(Rc(e,n),Rc(e,~n)),fc.arraySet(e.pending_buf,e.window,t,n,e.pending),e.pending+=n}(e,t,n,!0)}hc._tr_init=function(e){Xc||(!function(){var e,t,n,r,i,a=new Array(16);for(n=0,r=0;r<28;r++)for(Cc[r]=n,e=0;e<1<<yc[r];e++)Ec[n++]=r;for(Ec[n-1]=r,i=0,r=0;r<16;r++)for(Lc[r]=i,e=0;e<1<<bc[r];e++)Tc[i++]=r;for(i>>=7;r<mc;r++)for(Lc[r]=i<<7,e=0;e<1<<bc[r]-7;e++)Tc[256+i++]=r;for(t=0;t<=_c;t++)a[t]=0;for(e=0;e<=143;)Sc[2*e+1]=8,e++,a[8]++;for(;e<=255;)Sc[2*e+1]=9,e++,a[9]++;for(;e<=279;)Sc[2*e+1]=7,e++,a[7]++;for(;e<=287;)Sc[2*e+1]=8,e++,a[8]++;for(Fc(Sc,287,a),e=0;e<mc;e++)kc[2*e+1]=5,kc[2*e]=zc(e,5);Mc=new Ac(Sc,yc,257,gc,_c),Oc=new Ac(kc,bc,0,mc,_c),Ic=new Ac(new Array(0),wc,0,19,7)}(),Xc=!0),e.l_desc=new Bc(e.dyn_ltree,Mc),e.d_desc=new Bc(e.dyn_dtree,Oc),e.bl_desc=new Bc(e.bl_tree,Ic),e.bi_buf=0,e.bi_valid=0,$c(e)},hc._tr_stored_block=Zc,hc._tr_flush_block=function(e,t,n,r){var i,a,o=0;e.level>0?(2===e.strm.data_type&&(e.strm.data_type=function(e){var t,n=4093624447;for(t=0;t<=31;t++,n>>>=1)if(1&n&&0!==e.dyn_ltree[2*t])return 0;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return 1;for(t=32;t<vc;t++)if(0!==e.dyn_ltree[2*t])return 1;return 0}(e)),Hc(e,e.l_desc),Hc(e,e.d_desc),o=function(e){var t;for(qc(e,e.dyn_ltree,e.l_desc.max_code),qc(e,e.dyn_dtree,e.d_desc.max_code),Hc(e,e.bl_desc),t=18;t>=3&&0===e.bl_tree[2*xc[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(e),i=e.opt_len+3+7>>>3,(a=e.static_len+3+7>>>3)<=i&&(i=a)):i=a=n+5,n+4<=i&&-1!==t?Zc(e,t,n,r):4===e.strategy||a===i?(Pc(e,2+(r?1:0),3),Uc(e,Sc,kc)):(Pc(e,4+(r?1:0),3),function(e,t,n,r){var i;for(Pc(e,t-257,5),Pc(e,n-1,5),Pc(e,r-4,4),i=0;i<r;i++)Pc(e,e.bl_tree[2*xc[i]+1],3);Yc(e,e.dyn_ltree,t-1),Yc(e,e.dyn_dtree,n-1)}(e,e.l_desc.max_code+1,e.d_desc.max_code+1,o+1),Uc(e,e.dyn_ltree,e.dyn_dtree)),$c(e),r&&Vc(e)},hc._tr_tally=function(e,t,n){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&n,e.last_lit++,0===t?e.dyn_ltree[2*n]++:(e.matches++,t--,e.dyn_ltree[2*(Ec[n]+vc+1)]++,e.dyn_dtree[2*Nc(t)]++),e.last_lit===e.lit_bufsize-1},hc._tr_align=function(e){Pc(e,2,3),Dc(e,256,Sc),function(e){16===e.bi_valid?(Rc(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}(e)};var Gc=function(e,t,n,r){for(var i=65535&e|0,a=e>>>16&65535|0,o=0;0!==n;){n-=o=n>2e3?2e3:n;do{a=a+(i=i+t[r++]|0)|0}while(--o);i%=65521,a%=65521}return i|a<<16|0};var Kc=function(){for(var e,t=[],n=0;n<256;n++){e=n;for(var r=0;r<8;r++)e=1&e?3988292384^e>>>1:e>>>1;t[n]=e}return t}();var Jc,Qc=function(e,t,n,r){var i=Kc,a=r+n;e^=-1;for(var o=r;o<a;o++)e=e>>>8^i[255&(e^t[o])];return-1^e},ed={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},td=uc,nd=hc,rd=Gc,id=Qc,ad=ed,od=-2,sd=258,ld=262,ud=103,cd=113,dd=666;function hd(e,t){return e.msg=ad[t],t}function fd(e){return(e<<1)-(e>4?9:0)}function pd(e){for(var t=e.length;--t>=0;)e[t]=0}function vd(e){var t=e.state,n=t.pending;n>e.avail_out&&(n=e.avail_out),0!==n&&(td.arraySet(e.output,t.pending_buf,t.pending_out,n,e.next_out),e.next_out+=n,t.pending_out+=n,e.total_out+=n,e.avail_out-=n,t.pending-=n,0===t.pending&&(t.pending_out=0))}function gd(e,t){nd._tr_flush_block(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,vd(e.strm)}function md(e,t){e.pending_buf[e.pending++]=t}function _d(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t}function yd(e,t){var n,r,i=e.max_chain_length,a=e.strstart,o=e.prev_length,s=e.nice_match,l=e.strstart>e.w_size-ld?e.strstart-(e.w_size-ld):0,u=e.window,c=e.w_mask,d=e.prev,h=e.strstart+sd,f=u[a+o-1],p=u[a+o];e.prev_length>=e.good_match&&(i>>=2),s>e.lookahead&&(s=e.lookahead);do{if(u[(n=t)+o]===p&&u[n+o-1]===f&&u[n]===u[a]&&u[++n]===u[a+1]){a+=2,n++;do{}while(u[++a]===u[++n]&&u[++a]===u[++n]&&u[++a]===u[++n]&&u[++a]===u[++n]&&u[++a]===u[++n]&&u[++a]===u[++n]&&u[++a]===u[++n]&&u[++a]===u[++n]&&a<h);if(r=sd-(h-a),a=h-sd,r>o){if(e.match_start=t,o=r,r>=s)break;f=u[a+o-1],p=u[a+o]}}}while((t=d[t&c])>l&&0!=--i);return o<=e.lookahead?o:e.lookahead}function bd(e){var t,n,r,i,a,o,s,l,u,c,d=e.w_size;do{if(i=e.window_size-e.lookahead-e.strstart,e.strstart>=d+(d-ld)){td.arraySet(e.window,e.window,d,d,0),e.match_start-=d,e.strstart-=d,e.block_start-=d,t=n=e.hash_size;do{r=e.head[--t],e.head[t]=r>=d?r-d:0}while(--n);t=n=d;do{r=e.prev[--t],e.prev[t]=r>=d?r-d:0}while(--n);i+=d}if(0===e.strm.avail_in)break;if(o=e.strm,s=e.window,l=e.strstart+e.lookahead,u=i,c=void 0,(c=o.avail_in)>u&&(c=u),n=0===c?0:(o.avail_in-=c,td.arraySet(s,o.input,o.next_in,c,l),1===o.state.wrap?o.adler=rd(o.adler,s,c,l):2===o.state.wrap&&(o.adler=id(o.adler,s,c,l)),o.next_in+=c,o.total_in+=c,c),e.lookahead+=n,e.lookahead+e.insert>=3)for(a=e.strstart-e.insert,e.ins_h=e.window[a],e.ins_h=(e.ins_h<<e.hash_shift^e.window[a+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[a+3-1])&e.hash_mask,e.prev[a&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=a,a++,e.insert--,!(e.lookahead+e.insert<3)););}while(e.lookahead<ld&&0!==e.strm.avail_in)}function wd(e,t){for(var n,r;;){if(e.lookahead<ld){if(bd(e),e.lookahead<ld&&0===t)return 1;if(0===e.lookahead)break}if(n=0,e.lookahead>=3&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+3-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==n&&e.strstart-n<=e.w_size-ld&&(e.match_length=yd(e,n)),e.match_length>=3)if(r=nd._tr_tally(e,e.strstart-e.match_start,e.match_length-3),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=3){e.match_length--;do{e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+3-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart}while(0!=--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else r=nd._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(r&&(gd(e,!1),0===e.strm.avail_out))return 1}return e.insert=e.strstart<2?e.strstart:2,4===t?(gd(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(gd(e,!1),0===e.strm.avail_out)?1:2}function xd(e,t){for(var n,r,i;;){if(e.lookahead<ld){if(bd(e),e.lookahead<ld&&0===t)return 1;if(0===e.lookahead)break}if(n=0,e.lookahead>=3&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+3-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=2,0!==n&&e.prev_length<e.max_lazy_match&&e.strstart-n<=e.w_size-ld&&(e.match_length=yd(e,n),e.match_length<=5&&(1===e.strategy||3===e.match_length&&e.strstart-e.match_start>4096)&&(e.match_length=2)),e.prev_length>=3&&e.match_length<=e.prev_length){i=e.strstart+e.lookahead-3,r=nd._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-3),e.lookahead-=e.prev_length-1,e.prev_length-=2;do{++e.strstart<=i&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+3-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart)}while(0!=--e.prev_length);if(e.match_available=0,e.match_length=2,e.strstart++,r&&(gd(e,!1),0===e.strm.avail_out))return 1}else if(e.match_available){if((r=nd._tr_tally(e,0,e.window[e.strstart-1]))&&gd(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return 1}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(r=nd._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<2?e.strstart:2,4===t?(gd(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(gd(e,!1),0===e.strm.avail_out)?1:2}function Sd(e,t,n,r,i){this.good_length=e,this.max_lazy=t,this.nice_length=n,this.max_chain=r,this.func=i}function kd(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=8,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new td.Buf16(1146),this.dyn_dtree=new td.Buf16(122),this.bl_tree=new td.Buf16(78),pd(this.dyn_ltree),pd(this.dyn_dtree),pd(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new td.Buf16(16),this.heap=new td.Buf16(573),pd(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new td.Buf16(573),pd(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function Td(e){var t;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=2,(t=e.state).pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?42:cd,e.adler=2===t.wrap?0:1,t.last_flush=0,nd._tr_init(t),0):hd(e,od)}function Ed(e){var t,n=Td(e);return 0===n&&((t=e.state).window_size=2*t.w_size,pd(t.head),t.max_lazy_match=Jc[t.level].max_lazy,t.good_match=Jc[t.level].good_length,t.nice_match=Jc[t.level].nice_length,t.max_chain_length=Jc[t.level].max_chain,t.strstart=0,t.block_start=0,t.lookahead=0,t.insert=0,t.match_length=t.prev_length=2,t.match_available=0,t.ins_h=0),n}function Cd(e,t,n,r,i,a){if(!e)return od;var o=1;if(-1===t&&(t=6),r<0?(o=0,r=-r):r>15&&(o=2,r-=16),i<1||i>9||8!==n||r<8||r>15||t<0||t>9||a<0||a>4)return hd(e,od);8===r&&(r=9);var s=new kd;return e.state=s,s.strm=e,s.wrap=o,s.gzhead=null,s.w_bits=r,s.w_size=1<<s.w_bits,s.w_mask=s.w_size-1,s.hash_bits=i+7,s.hash_size=1<<s.hash_bits,s.hash_mask=s.hash_size-1,s.hash_shift=~~((s.hash_bits+3-1)/3),s.window=new td.Buf8(2*s.w_size),s.head=new td.Buf16(s.hash_size),s.prev=new td.Buf16(s.w_size),s.lit_bufsize=1<<i+6,s.pending_buf_size=4*s.lit_bufsize,s.pending_buf=new td.Buf8(s.pending_buf_size),s.d_buf=1*s.lit_bufsize,s.l_buf=3*s.lit_bufsize,s.level=t,s.strategy=a,s.method=n,Ed(e)}Jc=[new Sd(0,0,0,0,(function(e,t){var n=65535;for(n>e.pending_buf_size-5&&(n=e.pending_buf_size-5);;){if(e.lookahead<=1){if(bd(e),0===e.lookahead&&0===t)return 1;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var r=e.block_start+n;if((0===e.strstart||e.strstart>=r)&&(e.lookahead=e.strstart-r,e.strstart=r,gd(e,!1),0===e.strm.avail_out))return 1;if(e.strstart-e.block_start>=e.w_size-ld&&(gd(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,4===t?(gd(e,!0),0===e.strm.avail_out?3:4):(e.strstart>e.block_start&&(gd(e,!1),e.strm.avail_out),1)})),new Sd(4,4,8,4,wd),new Sd(4,5,16,8,wd),new Sd(4,6,32,32,wd),new Sd(4,4,16,16,xd),new Sd(8,16,32,32,xd),new Sd(8,16,128,128,xd),new Sd(8,32,128,256,xd),new Sd(32,128,258,1024,xd),new Sd(32,258,258,4096,xd)],dc.deflateInit=function(e,t){return Cd(e,t,8,15,8,0)},dc.deflateInit2=Cd,dc.deflateReset=Ed,dc.deflateResetKeep=Td,dc.deflateSetHeader=function(e,t){return e&&e.state?2!==e.state.wrap?od:(e.state.gzhead=t,0):od},dc.deflate=function(e,t){var n,r,i,a;if(!e||!e.state||t>5||t<0)return e?hd(e,od):od;if(r=e.state,!e.output||!e.input&&0!==e.avail_in||r.status===dd&&4!==t)return hd(e,0===e.avail_out?-5:od);if(r.strm=e,n=r.last_flush,r.last_flush=t,42===r.status)if(2===r.wrap)e.adler=0,md(r,31),md(r,139),md(r,8),r.gzhead?(md(r,(r.gzhead.text?1:0)+(r.gzhead.hcrc?2:0)+(r.gzhead.extra?4:0)+(r.gzhead.name?8:0)+(r.gzhead.comment?16:0)),md(r,255&r.gzhead.time),md(r,r.gzhead.time>>8&255),md(r,r.gzhead.time>>16&255),md(r,r.gzhead.time>>24&255),md(r,9===r.level?2:r.strategy>=2||r.level<2?4:0),md(r,255&r.gzhead.os),r.gzhead.extra&&r.gzhead.extra.length&&(md(r,255&r.gzhead.extra.length),md(r,r.gzhead.extra.length>>8&255)),r.gzhead.hcrc&&(e.adler=id(e.adler,r.pending_buf,r.pending,0)),r.gzindex=0,r.status=69):(md(r,0),md(r,0),md(r,0),md(r,0),md(r,0),md(r,9===r.level?2:r.strategy>=2||r.level<2?4:0),md(r,3),r.status=cd);else{var o=8+(r.w_bits-8<<4)<<8;o|=(r.strategy>=2||r.level<2?0:r.level<6?1:6===r.level?2:3)<<6,0!==r.strstart&&(o|=32),o+=31-o%31,r.status=cd,_d(r,o),0!==r.strstart&&(_d(r,e.adler>>>16),_d(r,65535&e.adler)),e.adler=1}if(69===r.status)if(r.gzhead.extra){for(i=r.pending;r.gzindex<(65535&r.gzhead.extra.length)&&(r.pending!==r.pending_buf_size||(r.gzhead.hcrc&&r.pending>i&&(e.adler=id(e.adler,r.pending_buf,r.pending-i,i)),vd(e),i=r.pending,r.pending!==r.pending_buf_size));)md(r,255&r.gzhead.extra[r.gzindex]),r.gzindex++;r.gzhead.hcrc&&r.pending>i&&(e.adler=id(e.adler,r.pending_buf,r.pending-i,i)),r.gzindex===r.gzhead.extra.length&&(r.gzindex=0,r.status=73)}else r.status=73;if(73===r.status)if(r.gzhead.name){i=r.pending;do{if(r.pending===r.pending_buf_size&&(r.gzhead.hcrc&&r.pending>i&&(e.adler=id(e.adler,r.pending_buf,r.pending-i,i)),vd(e),i=r.pending,r.pending===r.pending_buf_size)){a=1;break}a=r.gzindex<r.gzhead.name.length?255&r.gzhead.name.charCodeAt(r.gzindex++):0,md(r,a)}while(0!==a);r.gzhead.hcrc&&r.pending>i&&(e.adler=id(e.adler,r.pending_buf,r.pending-i,i)),0===a&&(r.gzindex=0,r.status=91)}else r.status=91;if(91===r.status)if(r.gzhead.comment){i=r.pending;do{if(r.pending===r.pending_buf_size&&(r.gzhead.hcrc&&r.pending>i&&(e.adler=id(e.adler,r.pending_buf,r.pending-i,i)),vd(e),i=r.pending,r.pending===r.pending_buf_size)){a=1;break}a=r.gzindex<r.gzhead.comment.length?255&r.gzhead.comment.charCodeAt(r.gzindex++):0,md(r,a)}while(0!==a);r.gzhead.hcrc&&r.pending>i&&(e.adler=id(e.adler,r.pending_buf,r.pending-i,i)),0===a&&(r.status=ud)}else r.status=ud;if(r.status===ud&&(r.gzhead.hcrc?(r.pending+2>r.pending_buf_size&&vd(e),r.pending+2<=r.pending_buf_size&&(md(r,255&e.adler),md(r,e.adler>>8&255),e.adler=0,r.status=cd)):r.status=cd),0!==r.pending){if(vd(e),0===e.avail_out)return r.last_flush=-1,0}else if(0===e.avail_in&&fd(t)<=fd(n)&&4!==t)return hd(e,-5);if(r.status===dd&&0!==e.avail_in)return hd(e,-5);if(0!==e.avail_in||0!==r.lookahead||0!==t&&r.status!==dd){var s=2===r.strategy?function(e,t){for(var n;;){if(0===e.lookahead&&(bd(e),0===e.lookahead)){if(0===t)return 1;break}if(e.match_length=0,n=nd._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,n&&(gd(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,4===t?(gd(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(gd(e,!1),0===e.strm.avail_out)?1:2}(r,t):3===r.strategy?function(e,t){for(var n,r,i,a,o=e.window;;){if(e.lookahead<=sd){if(bd(e),e.lookahead<=sd&&0===t)return 1;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=3&&e.strstart>0&&(r=o[i=e.strstart-1])===o[++i]&&r===o[++i]&&r===o[++i]){a=e.strstart+sd;do{}while(r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&i<a);e.match_length=sd-(a-i),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=3?(n=nd._tr_tally(e,1,e.match_length-3),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(n=nd._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),n&&(gd(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,4===t?(gd(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(gd(e,!1),0===e.strm.avail_out)?1:2}(r,t):Jc[r.level].func(r,t);if(3!==s&&4!==s||(r.status=dd),1===s||3===s)return 0===e.avail_out&&(r.last_flush=-1),0;if(2===s&&(1===t?nd._tr_align(r):5!==t&&(nd._tr_stored_block(r,0,0,!1),3===t&&(pd(r.head),0===r.lookahead&&(r.strstart=0,r.block_start=0,r.insert=0))),vd(e),0===e.avail_out))return r.last_flush=-1,0}return 4!==t?0:r.wrap<=0?1:(2===r.wrap?(md(r,255&e.adler),md(r,e.adler>>8&255),md(r,e.adler>>16&255),md(r,e.adler>>24&255),md(r,255&e.total_in),md(r,e.total_in>>8&255),md(r,e.total_in>>16&255),md(r,e.total_in>>24&255)):(_d(r,e.adler>>>16),_d(r,65535&e.adler)),vd(e),r.wrap>0&&(r.wrap=-r.wrap),0!==r.pending?0:1)},dc.deflateEnd=function(e){var t;return e&&e.state?42!==(t=e.state.status)&&69!==t&&73!==t&&91!==t&&t!==ud&&t!==cd&&t!==dd?hd(e,od):(e.state=null,t===cd?hd(e,-3):0):od},dc.deflateSetDictionary=function(e,t){var n,r,i,a,o,s,l,u,c=t.length;if(!e||!e.state)return od;if(2===(a=(n=e.state).wrap)||1===a&&42!==n.status||n.lookahead)return od;for(1===a&&(e.adler=rd(e.adler,t,c,0)),n.wrap=0,c>=n.w_size&&(0===a&&(pd(n.head),n.strstart=0,n.block_start=0,n.insert=0),u=new td.Buf8(n.w_size),td.arraySet(u,t,c-n.w_size,n.w_size,0),t=u,c=n.w_size),o=e.avail_in,s=e.next_in,l=e.input,e.avail_in=c,e.next_in=0,e.input=t,bd(n);n.lookahead>=3;){r=n.strstart,i=n.lookahead-2;do{n.ins_h=(n.ins_h<<n.hash_shift^n.window[r+3-1])&n.hash_mask,n.prev[r&n.w_mask]=n.head[n.ins_h],n.head[n.ins_h]=r,r++}while(--i);n.strstart=r,n.lookahead=2,bd(n)}return n.strstart+=n.lookahead,n.block_start=n.strstart,n.insert=n.lookahead,n.lookahead=0,n.match_length=n.prev_length=2,n.match_available=0,e.next_in=s,e.input=l,e.avail_in=o,n.wrap=a,0},dc.deflateInfo="pako deflate (from Nodeca project)";var Md={},Od=uc,Id=!0,Ld=!0;try{String.fromCharCode.apply(null,[0])}catch(o_){Id=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(o_){Ld=!1}for(var Ad=new Od.Buf8(256),Bd=0;Bd<256;Bd++)Ad[Bd]=Bd>=252?6:Bd>=248?5:Bd>=240?4:Bd>=224?3:Bd>=192?2:1;function Nd(e,t){if(t<65534&&(e.subarray&&Ld||!e.subarray&&Id))return String.fromCharCode.apply(null,Od.shrinkBuf(e,t));for(var n="",r=0;r<t;r++)n+=String.fromCharCode(e[r]);return n}Ad[254]=Ad[254]=1,Md.string2buf=function(e){var t,n,r,i,a,o=e.length,s=0;for(i=0;i<o;i++)55296==(64512&(n=e.charCodeAt(i)))&&i+1<o&&56320==(64512&(r=e.charCodeAt(i+1)))&&(n=65536+(n-55296<<10)+(r-56320),i++),s+=n<128?1:n<2048?2:n<65536?3:4;for(t=new Od.Buf8(s),a=0,i=0;a<s;i++)55296==(64512&(n=e.charCodeAt(i)))&&i+1<o&&56320==(64512&(r=e.charCodeAt(i+1)))&&(n=65536+(n-55296<<10)+(r-56320),i++),n<128?t[a++]=n:n<2048?(t[a++]=192|n>>>6,t[a++]=128|63&n):n<65536?(t[a++]=224|n>>>12,t[a++]=128|n>>>6&63,t[a++]=128|63&n):(t[a++]=240|n>>>18,t[a++]=128|n>>>12&63,t[a++]=128|n>>>6&63,t[a++]=128|63&n);return t},Md.buf2binstring=function(e){return Nd(e,e.length)},Md.binstring2buf=function(e){for(var t=new Od.Buf8(e.length),n=0,r=t.length;n<r;n++)t[n]=e.charCodeAt(n);return t},Md.buf2string=function(e,t){var n,r,i,a,o=t||e.length,s=new Array(2*o);for(r=0,n=0;n<o;)if((i=e[n++])<128)s[r++]=i;else if((a=Ad[i])>4)s[r++]=65533,n+=a-1;else{for(i&=2===a?31:3===a?15:7;a>1&&n<o;)i=i<<6|63&e[n++],a--;a>1?s[r++]=65533:i<65536?s[r++]=i:(i-=65536,s[r++]=55296|i>>10&1023,s[r++]=56320|1023&i)}return Nd(s,r)},Md.utf8border=function(e,t){var n;for((t=t||e.length)>e.length&&(t=e.length),n=t-1;n>=0&&128==(192&e[n]);)n--;return n<0||0===n?t:n+Ad[e[n]]>t?n:t};var Rd=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0},Pd=dc,Dd=uc,zd=Md,Fd=ed,$d=Rd,Vd=Object.prototype.toString;function jd(e){if(!(this instanceof jd))return new jd(e);this.options=Dd.assign({level:-1,method:8,chunkSize:16384,windowBits:15,memLevel:8,strategy:0,to:""},e||{});var t=this.options;t.raw&&t.windowBits>0?t.windowBits=-t.windowBits:t.gzip&&t.windowBits>0&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new $d,this.strm.avail_out=0;var n=Pd.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(0!==n)throw new Error(Fd[n]);if(t.header&&Pd.deflateSetHeader(this.strm,t.header),t.dictionary){var r;if(r="string"==typeof t.dictionary?zd.string2buf(t.dictionary):"[object ArrayBuffer]"===Vd.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,0!==(n=Pd.deflateSetDictionary(this.strm,r)))throw new Error(Fd[n]);this._dict_set=!0}}function Wd(e,t){var n=new jd(t);if(n.push(e,!0),n.err)throw n.msg||Fd[n.err];return n.result}jd.prototype.push=function(e,t){var n,r,i=this.strm,a=this.options.chunkSize;if(this.ended)return!1;r=t===~~t?t:!0===t?4:0,"string"==typeof e?i.input=zd.string2buf(e):"[object ArrayBuffer]"===Vd.call(e)?i.input=new Uint8Array(e):i.input=e,i.next_in=0,i.avail_in=i.input.length;do{if(0===i.avail_out&&(i.output=new Dd.Buf8(a),i.next_out=0,i.avail_out=a),1!==(n=Pd.deflate(i,r))&&0!==n)return this.onEnd(n),this.ended=!0,!1;0!==i.avail_out&&(0!==i.avail_in||4!==r&&2!==r)||("string"===this.options.to?this.onData(zd.buf2binstring(Dd.shrinkBuf(i.output,i.next_out))):this.onData(Dd.shrinkBuf(i.output,i.next_out)))}while((i.avail_in>0||0===i.avail_out)&&1!==n);return 4===r?(n=Pd.deflateEnd(this.strm),this.onEnd(n),this.ended=!0,0===n):2!==r||(this.onEnd(0),i.avail_out=0,!0)},jd.prototype.onData=function(e){this.chunks.push(e)},jd.prototype.onEnd=function(e){0===e&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=Dd.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},cc.Deflate=jd,cc.deflate=Wd,cc.deflateRaw=function(e,t){return(t=t||{}).raw=!0,Wd(e,t)},cc.gzip=function(e,t){return(t=t||{}).gzip=!0,Wd(e,t)};var Ud={},Hd={},qd=uc,Yd=15,Xd=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],Zd=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],Gd=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],Kd=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64],Jd=uc,Qd=Gc,eh=Qc,th=function(e,t){var n,r,i,a,o,s,l,u,c,d,h,f,p,v,g,m,_,y,b,w,x,S,k,T,E;n=e.state,r=e.next_in,T=e.input,i=r+(e.avail_in-5),a=e.next_out,E=e.output,o=a-(t-e.avail_out),s=a+(e.avail_out-257),l=n.dmax,u=n.wsize,c=n.whave,d=n.wnext,h=n.window,f=n.hold,p=n.bits,v=n.lencode,g=n.distcode,m=(1<<n.lenbits)-1,_=(1<<n.distbits)-1;e:do{p<15&&(f+=T[r++]<<p,p+=8,f+=T[r++]<<p,p+=8),y=v[f&m];t:for(;;){if(f>>>=b=y>>>24,p-=b,0===(b=y>>>16&255))E[a++]=65535&y;else{if(!(16&b)){if(0==(64&b)){y=v[(65535&y)+(f&(1<<b)-1)];continue t}if(32&b){n.mode=12;break e}e.msg="invalid literal/length code",n.mode=30;break e}w=65535&y,(b&=15)&&(p<b&&(f+=T[r++]<<p,p+=8),w+=f&(1<<b)-1,f>>>=b,p-=b),p<15&&(f+=T[r++]<<p,p+=8,f+=T[r++]<<p,p+=8),y=g[f&_];n:for(;;){if(f>>>=b=y>>>24,p-=b,!(16&(b=y>>>16&255))){if(0==(64&b)){y=g[(65535&y)+(f&(1<<b)-1)];continue n}e.msg="invalid distance code",n.mode=30;break e}if(x=65535&y,p<(b&=15)&&(f+=T[r++]<<p,(p+=8)<b&&(f+=T[r++]<<p,p+=8)),(x+=f&(1<<b)-1)>l){e.msg="invalid distance too far back",n.mode=30;break e}if(f>>>=b,p-=b,x>(b=a-o)){if((b=x-b)>c&&n.sane){e.msg="invalid distance too far back",n.mode=30;break e}if(S=0,k=h,0===d){if(S+=u-b,b<w){w-=b;do{E[a++]=h[S++]}while(--b);S=a-x,k=E}}else if(d<b){if(S+=u+d-b,(b-=d)<w){w-=b;do{E[a++]=h[S++]}while(--b);if(S=0,d<w){w-=b=d;do{E[a++]=h[S++]}while(--b);S=a-x,k=E}}}else if(S+=d-b,b<w){w-=b;do{E[a++]=h[S++]}while(--b);S=a-x,k=E}for(;w>2;)E[a++]=k[S++],E[a++]=k[S++],E[a++]=k[S++],w-=3;w&&(E[a++]=k[S++],w>1&&(E[a++]=k[S++]))}else{S=a-x;do{E[a++]=E[S++],E[a++]=E[S++],E[a++]=E[S++],w-=3}while(w>2);w&&(E[a++]=E[S++],w>1&&(E[a++]=E[S++]))}break}}break}}while(r<i&&a<s);r-=w=p>>3,f&=(1<<(p-=w<<3))-1,e.next_in=r,e.next_out=a,e.avail_in=r<i?i-r+5:5-(r-i),e.avail_out=a<s?s-a+257:257-(a-s),n.hold=f,n.bits=p},nh=function(e,t,n,r,i,a,o,s){var l,u,c,d,h,f,p,v,g,m=s.bits,_=0,y=0,b=0,w=0,x=0,S=0,k=0,T=0,E=0,C=0,M=null,O=0,I=new qd.Buf16(16),L=new qd.Buf16(16),A=null,B=0;for(_=0;_<=Yd;_++)I[_]=0;for(y=0;y<r;y++)I[t[n+y]]++;for(x=m,w=Yd;w>=1&&0===I[w];w--);if(x>w&&(x=w),0===w)return i[a++]=20971520,i[a++]=20971520,s.bits=1,0;for(b=1;b<w&&0===I[b];b++);for(x<b&&(x=b),T=1,_=1;_<=Yd;_++)if(T<<=1,(T-=I[_])<0)return-1;if(T>0&&(0===e||1!==w))return-1;for(L[1]=0,_=1;_<Yd;_++)L[_+1]=L[_]+I[_];for(y=0;y<r;y++)0!==t[n+y]&&(o[L[t[n+y]]++]=y);if(0===e?(M=A=o,f=19):1===e?(M=Xd,O-=257,A=Zd,B-=257,f=256):(M=Gd,A=Kd,f=-1),C=0,y=0,_=b,h=a,S=x,k=0,c=-1,d=(E=1<<x)-1,1===e&&E>852||2===e&&E>592)return 1;for(;;){p=_-k,o[y]<f?(v=0,g=o[y]):o[y]>f?(v=A[B+o[y]],g=M[O+o[y]]):(v=96,g=0),l=1<<_-k,b=u=1<<S;do{i[h+(C>>k)+(u-=l)]=p<<24|v<<16|g|0}while(0!==u);for(l=1<<_-1;C&l;)l>>=1;if(0!==l?(C&=l-1,C+=l):C=0,y++,0==--I[_]){if(_===w)break;_=t[n+o[y]]}if(_>x&&(C&d)!==c){for(0===k&&(k=x),h+=b,T=1<<(S=_-k);S+k<w&&!((T-=I[S+k])<=0);)S++,T<<=1;if(E+=1<<S,1===e&&E>852||2===e&&E>592)return 1;i[c=C&d]=x<<24|S<<16|h-a|0}}return 0!==C&&(i[h+C]=_-k<<24|64<<16|0),s.bits=x,0},rh=-2,ih=12,ah=30;function oh(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function sh(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new Jd.Buf16(320),this.work=new Jd.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function lh(e){var t;return e&&e.state?(t=e.state,e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=1,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new Jd.Buf32(852),t.distcode=t.distdyn=new Jd.Buf32(592),t.sane=1,t.back=-1,0):rh}function uh(e){var t;return e&&e.state?((t=e.state).wsize=0,t.whave=0,t.wnext=0,lh(e)):rh}function ch(e,t){var n,r;return e&&e.state?(r=e.state,t<0?(n=0,t=-t):(n=1+(t>>4),t<48&&(t&=15)),t&&(t<8||t>15)?rh:(null!==r.window&&r.wbits!==t&&(r.window=null),r.wrap=n,r.wbits=t,uh(e))):rh}function dh(e,t){var n,r;return e?(r=new sh,e.state=r,r.window=null,0!==(n=ch(e,t))&&(e.state=null),n):rh}var hh,fh,ph=!0;function vh(e){if(ph){var t;for(hh=new Jd.Buf32(512),fh=new Jd.Buf32(32),t=0;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(nh(1,e.lens,0,288,hh,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;nh(2,e.lens,0,32,fh,0,e.work,{bits:5}),ph=!1}e.lencode=hh,e.lenbits=9,e.distcode=fh,e.distbits=5}function gh(e,t,n,r){var i,a=e.state;return null===a.window&&(a.wsize=1<<a.wbits,a.wnext=0,a.whave=0,a.window=new Jd.Buf8(a.wsize)),r>=a.wsize?(Jd.arraySet(a.window,t,n-a.wsize,a.wsize,0),a.wnext=0,a.whave=a.wsize):((i=a.wsize-a.wnext)>r&&(i=r),Jd.arraySet(a.window,t,n-r,i,a.wnext),(r-=i)?(Jd.arraySet(a.window,t,n-r,r,0),a.wnext=r,a.whave=a.wsize):(a.wnext+=i,a.wnext===a.wsize&&(a.wnext=0),a.whave<a.wsize&&(a.whave+=i))),0}Hd.inflateReset=uh,Hd.inflateReset2=ch,Hd.inflateResetKeep=lh,Hd.inflateInit=function(e){return dh(e,15)},Hd.inflateInit2=dh,Hd.inflate=function(e,t){var n,r,i,a,o,s,l,u,c,d,h,f,p,v,g,m,_,y,b,w,x,S,k,T,E=0,C=new Jd.Buf8(4),M=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return rh;(n=e.state).mode===ih&&(n.mode=13),o=e.next_out,i=e.output,l=e.avail_out,a=e.next_in,r=e.input,s=e.avail_in,u=n.hold,c=n.bits,d=s,h=l,S=0;e:for(;;)switch(n.mode){case 1:if(0===n.wrap){n.mode=13;break}for(;c<16;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(2&n.wrap&&35615===u){n.check=0,C[0]=255&u,C[1]=u>>>8&255,n.check=eh(n.check,C,2,0),u=0,c=0,n.mode=2;break}if(n.flags=0,n.head&&(n.head.done=!1),!(1&n.wrap)||(((255&u)<<8)+(u>>8))%31){e.msg="incorrect header check",n.mode=ah;break}if(8!=(15&u)){e.msg="unknown compression method",n.mode=ah;break}if(c-=4,x=8+(15&(u>>>=4)),0===n.wbits)n.wbits=x;else if(x>n.wbits){e.msg="invalid window size",n.mode=ah;break}n.dmax=1<<x,e.adler=n.check=1,n.mode=512&u?10:ih,u=0,c=0;break;case 2:for(;c<16;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(n.flags=u,8!=(255&n.flags)){e.msg="unknown compression method",n.mode=ah;break}if(57344&n.flags){e.msg="unknown header flags set",n.mode=ah;break}n.head&&(n.head.text=u>>8&1),512&n.flags&&(C[0]=255&u,C[1]=u>>>8&255,n.check=eh(n.check,C,2,0)),u=0,c=0,n.mode=3;case 3:for(;c<32;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}n.head&&(n.head.time=u),512&n.flags&&(C[0]=255&u,C[1]=u>>>8&255,C[2]=u>>>16&255,C[3]=u>>>24&255,n.check=eh(n.check,C,4,0)),u=0,c=0,n.mode=4;case 4:for(;c<16;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}n.head&&(n.head.xflags=255&u,n.head.os=u>>8),512&n.flags&&(C[0]=255&u,C[1]=u>>>8&255,n.check=eh(n.check,C,2,0)),u=0,c=0,n.mode=5;case 5:if(1024&n.flags){for(;c<16;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}n.length=u,n.head&&(n.head.extra_len=u),512&n.flags&&(C[0]=255&u,C[1]=u>>>8&255,n.check=eh(n.check,C,2,0)),u=0,c=0}else n.head&&(n.head.extra=null);n.mode=6;case 6:if(1024&n.flags&&((f=n.length)>s&&(f=s),f&&(n.head&&(x=n.head.extra_len-n.length,n.head.extra||(n.head.extra=new Array(n.head.extra_len)),Jd.arraySet(n.head.extra,r,a,f,x)),512&n.flags&&(n.check=eh(n.check,r,f,a)),s-=f,a+=f,n.length-=f),n.length))break e;n.length=0,n.mode=7;case 7:if(2048&n.flags){if(0===s)break e;f=0;do{x=r[a+f++],n.head&&x&&n.length<65536&&(n.head.name+=String.fromCharCode(x))}while(x&&f<s);if(512&n.flags&&(n.check=eh(n.check,r,f,a)),s-=f,a+=f,x)break e}else n.head&&(n.head.name=null);n.length=0,n.mode=8;case 8:if(4096&n.flags){if(0===s)break e;f=0;do{x=r[a+f++],n.head&&x&&n.length<65536&&(n.head.comment+=String.fromCharCode(x))}while(x&&f<s);if(512&n.flags&&(n.check=eh(n.check,r,f,a)),s-=f,a+=f,x)break e}else n.head&&(n.head.comment=null);n.mode=9;case 9:if(512&n.flags){for(;c<16;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(u!==(65535&n.check)){e.msg="header crc mismatch",n.mode=ah;break}u=0,c=0}n.head&&(n.head.hcrc=n.flags>>9&1,n.head.done=!0),e.adler=n.check=0,n.mode=ih;break;case 10:for(;c<32;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}e.adler=n.check=oh(u),u=0,c=0,n.mode=11;case 11:if(0===n.havedict)return e.next_out=o,e.avail_out=l,e.next_in=a,e.avail_in=s,n.hold=u,n.bits=c,2;e.adler=n.check=1,n.mode=ih;case ih:if(5===t||6===t)break e;case 13:if(n.last){u>>>=7&c,c-=7&c,n.mode=27;break}for(;c<3;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}switch(n.last=1&u,c-=1,3&(u>>>=1)){case 0:n.mode=14;break;case 1:if(vh(n),n.mode=20,6===t){u>>>=2,c-=2;break e}break;case 2:n.mode=17;break;case 3:e.msg="invalid block type",n.mode=ah}u>>>=2,c-=2;break;case 14:for(u>>>=7&c,c-=7&c;c<32;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if((65535&u)!=(u>>>16^65535)){e.msg="invalid stored block lengths",n.mode=ah;break}if(n.length=65535&u,u=0,c=0,n.mode=15,6===t)break e;case 15:n.mode=16;case 16:if(f=n.length){if(f>s&&(f=s),f>l&&(f=l),0===f)break e;Jd.arraySet(i,r,a,f,o),s-=f,a+=f,l-=f,o+=f,n.length-=f;break}n.mode=ih;break;case 17:for(;c<14;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(n.nlen=257+(31&u),u>>>=5,c-=5,n.ndist=1+(31&u),u>>>=5,c-=5,n.ncode=4+(15&u),u>>>=4,c-=4,n.nlen>286||n.ndist>30){e.msg="too many length or distance symbols",n.mode=ah;break}n.have=0,n.mode=18;case 18:for(;n.have<n.ncode;){for(;c<3;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}n.lens[M[n.have++]]=7&u,u>>>=3,c-=3}for(;n.have<19;)n.lens[M[n.have++]]=0;if(n.lencode=n.lendyn,n.lenbits=7,k={bits:n.lenbits},S=nh(0,n.lens,0,19,n.lencode,0,n.work,k),n.lenbits=k.bits,S){e.msg="invalid code lengths set",n.mode=ah;break}n.have=0,n.mode=19;case 19:for(;n.have<n.nlen+n.ndist;){for(;m=(E=n.lencode[u&(1<<n.lenbits)-1])>>>16&255,_=65535&E,!((g=E>>>24)<=c);){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(_<16)u>>>=g,c-=g,n.lens[n.have++]=_;else{if(16===_){for(T=g+2;c<T;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(u>>>=g,c-=g,0===n.have){e.msg="invalid bit length repeat",n.mode=ah;break}x=n.lens[n.have-1],f=3+(3&u),u>>>=2,c-=2}else if(17===_){for(T=g+3;c<T;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}c-=g,x=0,f=3+(7&(u>>>=g)),u>>>=3,c-=3}else{for(T=g+7;c<T;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}c-=g,x=0,f=11+(127&(u>>>=g)),u>>>=7,c-=7}if(n.have+f>n.nlen+n.ndist){e.msg="invalid bit length repeat",n.mode=ah;break}for(;f--;)n.lens[n.have++]=x}}if(n.mode===ah)break;if(0===n.lens[256]){e.msg="invalid code -- missing end-of-block",n.mode=ah;break}if(n.lenbits=9,k={bits:n.lenbits},S=nh(1,n.lens,0,n.nlen,n.lencode,0,n.work,k),n.lenbits=k.bits,S){e.msg="invalid literal/lengths set",n.mode=ah;break}if(n.distbits=6,n.distcode=n.distdyn,k={bits:n.distbits},S=nh(2,n.lens,n.nlen,n.ndist,n.distcode,0,n.work,k),n.distbits=k.bits,S){e.msg="invalid distances set",n.mode=ah;break}if(n.mode=20,6===t)break e;case 20:n.mode=21;case 21:if(s>=6&&l>=258){e.next_out=o,e.avail_out=l,e.next_in=a,e.avail_in=s,n.hold=u,n.bits=c,th(e,h),o=e.next_out,i=e.output,l=e.avail_out,a=e.next_in,r=e.input,s=e.avail_in,u=n.hold,c=n.bits,n.mode===ih&&(n.back=-1);break}for(n.back=0;m=(E=n.lencode[u&(1<<n.lenbits)-1])>>>16&255,_=65535&E,!((g=E>>>24)<=c);){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(m&&0==(240&m)){for(y=g,b=m,w=_;m=(E=n.lencode[w+((u&(1<<y+b)-1)>>y)])>>>16&255,_=65535&E,!(y+(g=E>>>24)<=c);){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}u>>>=y,c-=y,n.back+=y}if(u>>>=g,c-=g,n.back+=g,n.length=_,0===m){n.mode=26;break}if(32&m){n.back=-1,n.mode=ih;break}if(64&m){e.msg="invalid literal/length code",n.mode=ah;break}n.extra=15&m,n.mode=22;case 22:if(n.extra){for(T=n.extra;c<T;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}n.length+=u&(1<<n.extra)-1,u>>>=n.extra,c-=n.extra,n.back+=n.extra}n.was=n.length,n.mode=23;case 23:for(;m=(E=n.distcode[u&(1<<n.distbits)-1])>>>16&255,_=65535&E,!((g=E>>>24)<=c);){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(0==(240&m)){for(y=g,b=m,w=_;m=(E=n.distcode[w+((u&(1<<y+b)-1)>>y)])>>>16&255,_=65535&E,!(y+(g=E>>>24)<=c);){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}u>>>=y,c-=y,n.back+=y}if(u>>>=g,c-=g,n.back+=g,64&m){e.msg="invalid distance code",n.mode=ah;break}n.offset=_,n.extra=15&m,n.mode=24;case 24:if(n.extra){for(T=n.extra;c<T;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}n.offset+=u&(1<<n.extra)-1,u>>>=n.extra,c-=n.extra,n.back+=n.extra}if(n.offset>n.dmax){e.msg="invalid distance too far back",n.mode=ah;break}n.mode=25;case 25:if(0===l)break e;if(f=h-l,n.offset>f){if((f=n.offset-f)>n.whave&&n.sane){e.msg="invalid distance too far back",n.mode=ah;break}f>n.wnext?(f-=n.wnext,p=n.wsize-f):p=n.wnext-f,f>n.length&&(f=n.length),v=n.window}else v=i,p=o-n.offset,f=n.length;f>l&&(f=l),l-=f,n.length-=f;do{i[o++]=v[p++]}while(--f);0===n.length&&(n.mode=21);break;case 26:if(0===l)break e;i[o++]=n.length,l--,n.mode=21;break;case 27:if(n.wrap){for(;c<32;){if(0===s)break e;s--,u|=r[a++]<<c,c+=8}if(h-=l,e.total_out+=h,n.total+=h,h&&(e.adler=n.check=n.flags?eh(n.check,i,h,o-h):Qd(n.check,i,h,o-h)),h=l,(n.flags?u:oh(u))!==n.check){e.msg="incorrect data check",n.mode=ah;break}u=0,c=0}n.mode=28;case 28:if(n.wrap&&n.flags){for(;c<32;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(u!==(4294967295&n.total)){e.msg="incorrect length check",n.mode=ah;break}u=0,c=0}n.mode=29;case 29:S=1;break e;case ah:S=-3;break e;case 31:return-4;case 32:default:return rh}return e.next_out=o,e.avail_out=l,e.next_in=a,e.avail_in=s,n.hold=u,n.bits=c,(n.wsize||h!==e.avail_out&&n.mode<ah&&(n.mode<27||4!==t))&&gh(e,e.output,e.next_out,h-e.avail_out),d-=e.avail_in,h-=e.avail_out,e.total_in+=d,e.total_out+=h,n.total+=h,n.wrap&&h&&(e.adler=n.check=n.flags?eh(n.check,i,h,e.next_out-h):Qd(n.check,i,h,e.next_out-h)),e.data_type=n.bits+(n.last?64:0)+(n.mode===ih?128:0)+(20===n.mode||15===n.mode?256:0),(0===d&&0===h||4===t)&&0===S&&(S=-5),S},Hd.inflateEnd=function(e){if(!e||!e.state)return rh;var t=e.state;return t.window&&(t.window=null),e.state=null,0},Hd.inflateGetHeader=function(e,t){var n;return e&&e.state?0==(2&(n=e.state).wrap)?rh:(n.head=t,t.done=!1,0):rh},Hd.inflateSetDictionary=function(e,t){var n,r=t.length;return e&&e.state?0!==(n=e.state).wrap&&11!==n.mode?rh:11===n.mode&&Qd(1,t,r,0)!==n.check?-3:gh(e,t,r,r)?(n.mode=31,-4):(n.havedict=1,0):rh},Hd.inflateInfo="pako inflate (from Nodeca project)";var mh={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8};var _h=Hd,yh=uc,bh=Md,wh=mh,xh=ed,Sh=Rd,kh=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1},Th=Object.prototype.toString;function Eh(e){if(!(this instanceof Eh))return new Eh(e);this.options=yh.assign({chunkSize:16384,windowBits:0,to:""},e||{});var t=this.options;t.raw&&t.windowBits>=0&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(t.windowBits>=0&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),t.windowBits>15&&t.windowBits<48&&0==(15&t.windowBits)&&(t.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Sh,this.strm.avail_out=0;var n=_h.inflateInit2(this.strm,t.windowBits);if(n!==wh.Z_OK)throw new Error(xh[n]);if(this.header=new kh,_h.inflateGetHeader(this.strm,this.header),t.dictionary&&("string"==typeof t.dictionary?t.dictionary=bh.string2buf(t.dictionary):"[object ArrayBuffer]"===Th.call(t.dictionary)&&(t.dictionary=new Uint8Array(t.dictionary)),t.raw&&(n=_h.inflateSetDictionary(this.strm,t.dictionary))!==wh.Z_OK))throw new Error(xh[n])}function Ch(e,t){var n=new Eh(t);if(n.push(e,!0),n.err)throw n.msg||xh[n.err];return n.result}Eh.prototype.push=function(e,t){var n,r,i,a,o,s=this.strm,l=this.options.chunkSize,u=this.options.dictionary,c=!1;if(this.ended)return!1;r=t===~~t?t:!0===t?wh.Z_FINISH:wh.Z_NO_FLUSH,"string"==typeof e?s.input=bh.binstring2buf(e):"[object ArrayBuffer]"===Th.call(e)?s.input=new Uint8Array(e):s.input=e,s.next_in=0,s.avail_in=s.input.length;do{if(0===s.avail_out&&(s.output=new yh.Buf8(l),s.next_out=0,s.avail_out=l),(n=_h.inflate(s,wh.Z_NO_FLUSH))===wh.Z_NEED_DICT&&u&&(n=_h.inflateSetDictionary(this.strm,u)),n===wh.Z_BUF_ERROR&&!0===c&&(n=wh.Z_OK,c=!1),n!==wh.Z_STREAM_END&&n!==wh.Z_OK)return this.onEnd(n),this.ended=!0,!1;s.next_out&&(0!==s.avail_out&&n!==wh.Z_STREAM_END&&(0!==s.avail_in||r!==wh.Z_FINISH&&r!==wh.Z_SYNC_FLUSH)||("string"===this.options.to?(i=bh.utf8border(s.output,s.next_out),a=s.next_out-i,o=bh.buf2string(s.output,i),s.next_out=a,s.avail_out=l-a,a&&yh.arraySet(s.output,s.output,i,a,0),this.onData(o)):this.onData(yh.shrinkBuf(s.output,s.next_out)))),0===s.avail_in&&0===s.avail_out&&(c=!0)}while((s.avail_in>0||0===s.avail_out)&&n!==wh.Z_STREAM_END);return n===wh.Z_STREAM_END&&(r=wh.Z_FINISH),r===wh.Z_FINISH?(n=_h.inflateEnd(this.strm),this.onEnd(n),this.ended=!0,n===wh.Z_OK):r!==wh.Z_SYNC_FLUSH||(this.onEnd(wh.Z_OK),s.avail_out=0,!0)},Eh.prototype.onData=function(e){this.chunks.push(e)},Eh.prototype.onEnd=function(e){e===wh.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=yh.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},Ud.Inflate=Eh,Ud.inflate=Ch,Ud.inflateRaw=function(e,t){return(t=t||{}).raw=!0,Ch(e,t)},Ud.ungzip=Ch;var Mh={};(0,uc.assign)(Mh,cc,Ud,mh);var Oh=Mh,Ih=!1,Lh=0,Ah=0,Bh=960,Nh=375,Rh=750;function Ph(e,t){var n=Number(e);return isNaN(n)?t:n}var Dh=rc(0,((e,t)=>{var n;if(0===Lh&&(!function(){var{platform:e,pixelRatio:t,windowWidth:n}=ic();Lh=n,Ah=t,Ih="ios"===e}(),n=__uniConfig.globalStyle||{},Bh=Ph(n.rpxCalcMaxDeviceWidth,960),Nh=Ph(n.rpxCalcBaseDeviceWidth,375),Rh=Ph(n.rpxCalcBaseDeviceWidth,750)),0===(e=Number(e)))return 0;var r=t||Lh,i=e/750*(r=e===Rh||r<=Bh?r:Nh);return i<0&&(i=-i),0===(i=Math.floor(i+1e-4))&&(i=1!==Ah&&Ih?.5:1),e<0?-i:i})),zh={};zh.f={}.propertyIsEnumerable;var Fh,$h=C,Vh=De,jh=Y,Wh=zh.f,Uh=(Fh=!1,function(e){for(var t,n=jh(e),r=Vh(n),i=r.length,a=0,o=[];i>a;)t=r[a++],$h&&!Wh.call(n,t)||o.push(Fh?[t,n[t]]:n[t]);return o});ve(ve.S,"Object",{values:function(e){return Uh(e)}});var Hh=function(){if("object"==typeof window)if("IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype)"isIntersecting"in window.IntersectionObserverEntry.prototype||Object.defineProperty(window.IntersectionObserverEntry.prototype,"isIntersecting",{get:function(){return this.intersectionRatio>0}});else{var e=function(e){for(var t=window.document,n=i(t);n;)n=i(t=n.ownerDocument);return t}(),t=[],n=null,r=null;o.prototype.THROTTLE_TIMEOUT=100,o.prototype.POLL_INTERVAL=null,o.prototype.USE_MUTATION_OBSERVER=!0,o._setupCrossOriginUpdater=function(){return n||(n=function(e,n){r=e&&n?d(e,n):{top:0,bottom:0,left:0,right:0,width:0,height:0},t.forEach((function(e){e._checkForIntersections()}))}),n},o._resetCrossOriginUpdater=function(){n=null,r=null},o.prototype.observe=function(e){if(!this._observationTargets.some((function(t){return t.element==e}))){if(!e||1!=e.nodeType)throw new Error("target must be an Element");this._registerInstance(),this._observationTargets.push({element:e,entry:null}),this._monitorIntersections(e.ownerDocument),this._checkForIntersections()}},o.prototype.unobserve=function(e){this._observationTargets=this._observationTargets.filter((function(t){return t.element!=e})),this._unmonitorIntersections(e.ownerDocument),0==this._observationTargets.length&&this._unregisterInstance()},o.prototype.disconnect=function(){this._observationTargets=[],this._unmonitorAllIntersections(),this._unregisterInstance()},o.prototype.takeRecords=function(){var e=this._queuedEntries.slice();return this._queuedEntries=[],e},o.prototype._initThresholds=function(e){var t=e||[0];return Array.isArray(t)||(t=[t]),t.sort().filter((function(e,t,n){if("number"!=typeof e||isNaN(e)||e<0||e>1)throw new Error("threshold must be a number between 0 and 1 inclusively");return e!==n[t-1]}))},o.prototype._parseRootMargin=function(e){var t=(e||"0px").split(/\s+/).map((function(e){var t=/^(-?\d*\.?\d+)(px|%)$/.exec(e);if(!t)throw new Error("rootMargin must be specified in pixels or percent");return{value:parseFloat(t[1]),unit:t[2]}}));return t[1]=t[1]||t[0],t[2]=t[2]||t[0],t[3]=t[3]||t[1],t},o.prototype._monitorIntersections=function(t){var n=t.defaultView;if(n&&-1==this._monitoringDocuments.indexOf(t)){var r=this._checkForIntersections,a=null,o=null;this.POLL_INTERVAL?a=n.setInterval(r,this.POLL_INTERVAL):(s(n,"resize",r,!0),s(t,"scroll",r,!0),this.USE_MUTATION_OBSERVER&&"MutationObserver"in n&&(o=new n.MutationObserver(r)).observe(t,{attributes:!0,childList:!0,characterData:!0,subtree:!0})),this._monitoringDocuments.push(t),this._monitoringUnsubscribes.push((function(){var e=t.defaultView;e&&(a&&e.clearInterval(a),l(e,"resize",r,!0)),l(t,"scroll",r,!0),o&&o.disconnect()}));var u=this.root&&(this.root.ownerDocument||this.root)||e;if(t!=u){var c=i(t);c&&this._monitorIntersections(c.ownerDocument)}}},o.prototype._unmonitorIntersections=function(t){var n=this._monitoringDocuments.indexOf(t);if(-1!=n){var r=this.root&&(this.root.ownerDocument||this.root)||e;if(!this._observationTargets.some((function(e){var n=e.element.ownerDocument;if(n==t)return!0;for(;n&&n!=r;){var a=i(n);if((n=a&&a.ownerDocument)==t)return!0}return!1}))){var a=this._monitoringUnsubscribes[n];if(this._monitoringDocuments.splice(n,1),this._monitoringUnsubscribes.splice(n,1),a(),t!=r){var o=i(t);o&&this._unmonitorIntersections(o.ownerDocument)}}}},o.prototype._unmonitorAllIntersections=function(){var e=this._monitoringUnsubscribes.slice(0);this._monitoringDocuments.length=0,this._monitoringUnsubscribes.length=0;for(var t=0;t<e.length;t++)e[t]()},o.prototype._checkForIntersections=function(){if(this.root||!n||r){var e=this._rootIsInDom(),t=e?this._getRootRect():{top:0,bottom:0,left:0,right:0,width:0,height:0};this._observationTargets.forEach((function(r){var i=r.element,o=u(i),s=this._rootContainsTarget(i),l=r.entry,c=e&&s&&this._computeTargetAndRootIntersection(i,o,t),d=null;this._rootContainsTarget(i)?n&&!this.root||(d=t):d={top:0,bottom:0,left:0,right:0,width:0,height:0};var h=r.entry=new a({time:window.performance&&performance.now&&performance.now(),target:i,boundingClientRect:o,rootBounds:d,intersectionRect:c});l?e&&s?this._hasCrossedThreshold(l,h)&&this._queuedEntries.push(h):l&&l.isIntersecting&&this._queuedEntries.push(h):this._queuedEntries.push(h)}),this),this._queuedEntries.length&&this._callback(this.takeRecords(),this)}},o.prototype._computeTargetAndRootIntersection=function(t,i,a){if("none"!=window.getComputedStyle(t).display){for(var o,s,l,c,h,p,v,g,m=i,_=f(t),y=!1;!y&&_;){var b=null,w=1==_.nodeType?window.getComputedStyle(_):{};if("none"==w.display)return null;if(_==this.root||9==_.nodeType)if(y=!0,_==this.root||_==e)n&&!this.root?!r||0==r.width&&0==r.height?(_=null,b=null,m=null):b=r:b=a;else{var x=f(_),S=x&&u(x),k=x&&this._computeTargetAndRootIntersection(x,S,a);S&&k?(_=x,b=d(S,k)):(_=null,m=null)}else{var T=_.ownerDocument;_!=T.body&&_!=T.documentElement&&"visible"!=w.overflow&&(b=u(_))}if(b&&(o=b,s=m,l=void 0,c=void 0,h=void 0,p=void 0,v=void 0,g=void 0,l=Math.max(o.top,s.top),c=Math.min(o.bottom,s.bottom),h=Math.max(o.left,s.left),p=Math.min(o.right,s.right),g=c-l,m=(v=p-h)>=0&&g>=0&&{top:l,bottom:c,left:h,right:p,width:v,height:g}||null),!m)break;_=_&&f(_)}return m}},o.prototype._getRootRect=function(){var t;if(this.root&&!p(this.root))t=u(this.root);else{var n=p(this.root)?this.root:e,r=n.documentElement,i=n.body;t={top:0,left:0,right:r.clientWidth||i.clientWidth,width:r.clientWidth||i.clientWidth,bottom:r.clientHeight||i.clientHeight,height:r.clientHeight||i.clientHeight}}return this._expandRectByRootMargin(t)},o.prototype._expandRectByRootMargin=function(e){var t=this._rootMarginValues.map((function(t,n){return"px"==t.unit?t.value:t.value*(n%2?e.width:e.height)/100})),n={top:e.top-t[0],right:e.right+t[1],bottom:e.bottom+t[2],left:e.left-t[3]};return n.width=n.right-n.left,n.height=n.bottom-n.top,n},o.prototype._hasCrossedThreshold=function(e,t){var n=e&&e.isIntersecting?e.intersectionRatio||0:-1,r=t.isIntersecting?t.intersectionRatio||0:-1;if(n!==r)for(var i=0;i<this.thresholds.length;i++){var a=this.thresholds[i];if(a==n||a==r||a<n!=a<r)return!0}},o.prototype._rootIsInDom=function(){return!this.root||h(e,this.root)},o.prototype._rootContainsTarget=function(t){var n=this.root&&(this.root.ownerDocument||this.root)||e;return h(n,t)&&(!this.root||n==t.ownerDocument)},o.prototype._registerInstance=function(){t.indexOf(this)<0&&t.push(this)},o.prototype._unregisterInstance=function(){var e=t.indexOf(this);-1!=e&&t.splice(e,1)},window.IntersectionObserver=o,window.IntersectionObserverEntry=a}function i(e){try{return e.defaultView&&e.defaultView.frameElement||null}catch(t){return null}}function a(e){this.time=e.time,this.target=e.target,this.rootBounds=c(e.rootBounds),this.boundingClientRect=c(e.boundingClientRect),this.intersectionRect=c(e.intersectionRect||{top:0,bottom:0,left:0,right:0,width:0,height:0}),this.isIntersecting=!!e.intersectionRect;var t=this.boundingClientRect,n=t.width*t.height,r=this.intersectionRect,i=r.width*r.height;this.intersectionRatio=n?Number((i/n).toFixed(4)):this.isIntersecting?1:0}function o(e,t){var n=t||{};if("function"!=typeof e)throw new Error("callback must be a function");if(n.root&&1!=n.root.nodeType&&9!=n.root.nodeType)throw new Error("root must be a Document or Element");this._checkForIntersections=function(e,t){var n=null;return function(){n||(n=setTimeout((function(){e(),n=null}),t))}}(this._checkForIntersections.bind(this),this.THROTTLE_TIMEOUT),this._callback=e,this._observationTargets=[],this._queuedEntries=[],this._rootMarginValues=this._parseRootMargin(n.rootMargin),this.thresholds=this._initThresholds(n.threshold),this.root=n.root||null,this.rootMargin=this._rootMarginValues.map((function(e){return e.value+e.unit})).join(" "),this._monitoringDocuments=[],this._monitoringUnsubscribes=[]}function s(e,t,n,r){"function"==typeof e.addEventListener?e.addEventListener(t,n,r||!1):"function"==typeof e.attachEvent&&e.attachEvent("on"+t,n)}function l(e,t,n,r){"function"==typeof e.removeEventListener?e.removeEventListener(t,n,r||!1):"function"==typeof e.detatchEvent&&e.detatchEvent("on"+t,n)}function u(e){var t;try{t=e.getBoundingClientRect()}catch(n){}return t?(t.width&&t.height||(t={top:t.top,right:t.right,bottom:t.bottom,left:t.left,width:t.right-t.left,height:t.bottom-t.top}),t):{top:0,bottom:0,left:0,right:0,width:0,height:0}}function c(e){return!e||"x"in e?e:{top:e.top,y:e.top,bottom:e.bottom,left:e.left,x:e.left,right:e.right,width:e.width,height:e.height}}function d(e,t){var n=t.top-e.top,r=t.left-e.left;return{top:n,left:r,height:t.height,width:t.width,bottom:n+t.height,right:r+t.width}}function h(e,t){for(var n=t;n;){if(n==e)return!0;n=f(n)}return!1}function f(t){var n=t.parentNode;return 9==t.nodeType&&t!=e?i(t):(n&&n.assignedSlot&&(n=n.assignedSlot.parentNode),n&&11==n.nodeType&&n.host?n.host:n)}function p(e){return e&&9===e.nodeType}};function qh(e){var{bottom:t,height:n,left:r,right:i,top:a,width:o}=e||{};return{bottom:t,height:n,left:r,right:i,top:a,width:o}}function Yh(e){var{intersectionRatio:t,boundingClientRect:{height:n,width:r},intersectionRect:{height:i,width:a}}=e;return 0!==t?t:i===n?a/r:i/n}const Xh=Object.freeze(Object.defineProperty({__proto__:null,navigateBack:function(e){UniViewJSBridge.invokeServiceMethod("navigateBack",e)},navigateTo:function(e){UniViewJSBridge.invokeServiceMethod("navigateTo",e)},reLaunch:function(e){UniViewJSBridge.invokeServiceMethod("reLaunch",e)},redirectTo:function(e){UniViewJSBridge.invokeServiceMethod("redirectTo",e)},switchTab:function(e){UniViewJSBridge.invokeServiceMethod("switchTab",e)},upx2px:Dh},Symbol.toStringTag,{value:"Module"}));function Zh(e,t){if(t)return vn(t,"a")&&(t.a=e(t.a)),vn(t,"e")&&(t.e=e(t.e,!1)),vn(t,"w")&&(t.w=function(e,t){var n={};return e.forEach((e=>{var[r,[i,a]]=e;n[t(r)]=[t(i),a]})),n}(t.w,e)),vn(t,"s")&&(t.s=e(t.s)),vn(t,"t")&&(t.t=e(t.t)),t}var Gh=new Set;function Kh(e,t){Gh.add(function(e,t){return e.priority=t,e}(e,t))}function Jh(e,t){var n=window.__wxsModules,r=n&&n[e];return r||(t&&t.__renderjsInstances?t.__renderjsInstances[e]:void 0)}var Qh=Kn.length;function ef(e,t,n){var[r,i,a,o]=nf(t),s=tf(e,r);if(gn(n)||gn(o)){var[l,u]=a.split(".");return rf(s,i,l,u,n||o)}return function(e,t,n){var r=Jh(t,e);if(!r)return console.error(tr("wxs","module "+n+" not found"));return ir(r,n.slice(n.indexOf(".")+1))}(s,i,a)}function tf(e,t){if(e.__ownerId===t)return e;for(var n=e.parentElement;n;){if(n.__ownerId===t)return n;n=n.parentElement}return e}function nf(e){return JSON.parse(e.slice(Qh))}function rf(e,t,n,r,i){var a=Jh(t,e);if(!a)return console.error(tr("wxs","module "+n+" not found"));var o=a[r];return bn(o)?o.apply(a,i):console.error(n+"."+r+" is not a function")}function af(e,t,n){var r=n;return n=>{try{!function(e,t,n,r){var[i,a,o]=nf(e),s=tf(t,i),[l,u]=o.split(".");rf(s,a,l,u,[n,r,Uu(qu(s)),Uu(qu(t))])}(t,e.$,n,r)}catch(i){console.error(i)}r=n}}function of(e,t){var n=qu(t);return Object.defineProperty(e,"instance",{get:()=>Uu(n)}),e}function sf(e,t){Object.keys(t).forEach((n=>{!function(e,t){var n=function(e){var t=window.__renderjsModules,n=t&&t[e];if(!n)return console.error(tr("renderjs",e+" not found"));return n}(t);if(!n)return;var r=e.$;(r.__renderjsInstances||(r.__renderjsInstances={}))[t]=function(e,t){return(t=t.default||t).render=()=>{},ru(t).mixin({mounted(){this.$ownerInstance=Uu(qu(e))}}).mount(document.createElement("div"))}(r,n)}(e,t[n])}))}var lf=Jn.length;function uf(e,t){return wn(e)?(0===e.indexOf(Jn)?e=JSON.parse(e.slice(lf)):0===e.indexOf(Kn)&&(e=ef(t,e)),e):e}function cf(e){return 0===e.indexOf("--")}class df{constructor(e,t,n,r){this.isMounted=!1,this.isUnmounted=!1,this.$hasWxsProps=!1,this.$children=[],this.id=e,this.tag=t,this.pid=n,r&&(this.$=r),this.$wxsProps=new Map;var i=this.$parent=function(e){return Bm.get(e)}(n);i&&i.appendUniChild(this)}init(e){vn(e,"t")&&(this.$.textContent=e.t)}setText(e){this.$.textContent=e,this.updateView()}insert(e,t,n){n&&this.init(n,!1);var r=this.$,i=Nm(e);-1===t?i.appendChild(r):i.insertBefore(r,Nm(t).$),this.isMounted=!0}remove(){this.removeUniParent();var{$:e}=this;e.parentNode.removeChild(e),this.isUnmounted=!0,Rm(this.id),function(e){var{__renderjsInstances:t}=e.$;t&&Object.keys(t).forEach((e=>{t[e].$.appContext.app.unmount()}))}(this),this.removeUniChildren(),this.updateView()}appendChild(e){var t=this.$.appendChild(e);return this.updateView(!0),t}insertBefore(e,t){var n=this.$.insertBefore(e,t);return this.updateView(!0),n}appendUniChild(e){this.$children.push(e)}removeUniChild(e){var t=this.$children.indexOf(e);t>=0&&this.$children.splice(t,1)}removeUniParent(){var{$parent:e}=this;e&&(e.removeUniChild(this),this.$parent=void 0)}removeUniChildren(){this.$children.forEach((e=>e.remove())),this.$children.length=0}setWxsProps(e){Object.keys(e).forEach((t=>{if(0===t.indexOf(br)){var n=t.replace(br,""),r=uf(e[n]),i=af(this,e[t],r);Kh((()=>i(r)),4),this.$wxsProps.set(t,i),delete e[t],delete e[n],this.$hasWxsProps=!0}}))}addWxsEvents(e){Object.keys(e).forEach((t=>{var[n,r]=e[t];this.addWxsEvent(t,n,r)}))}addWxsEvent(e,t,n){}wxsPropsInvoke(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=this.$hasWxsProps&&this.$wxsProps.get(br+e);if(r)return Kh((()=>n?Ja((()=>r(t))):r(t)),4),!0}updateView(e){(this.isMounted||e)&&window.dispatchEvent(new CustomEvent("updateview"))}}function hf(e,t){var{__wxsAddClass:n,__wxsRemoveClass:r}=e;r&&r.length&&(t=t.split(/\s+/).filter((e=>-1===r.indexOf(e))).join(" "),r.length=0),n&&n.length&&(t=t+" "+n.join(" ")),e.className=t}function ff(e){return _f(Sf(e))}var pf,vf,gf,mf=/url\(\s*'?"?([a-zA-Z0-9\.\-\_\/]+\.(jpg|gif|png))"?'?\s*\)/,_f=e=>{if(wn(e)&&-1!==e.indexOf("url(")){var t=e.match(mf);t&&3===t.length&&(e=e.replace(t[1],ac(t[1])))}return e},{unit:yf,unitRatio:bf,unitPrecision:wf}={unit:"rem",unitRatio:10/320,unitPrecision:5},xf=(pf=yf,vf=bf,gf=wf,e=>e.replace(lr,((e,t)=>{if(!t)return e;if(1===vf)return"".concat(t).concat(pf);var n,r,i,a,o=(n=parseFloat(t)*vf,r=gf,i=Math.pow(10,r+1),a=Math.floor(n*i),10*Math.round(a/10)/i);return 0===o?"0":"".concat(o).concat(pf)}))),Sf=e=>wn(e)?xf(e):e,kf=["Webkit"],Tf={};function Ef(e,t){var n=Tf[t];if(n)return n;var r=An(t);if("filter"!==r&&r in e)return Tf[t]=r;r=Rn(r);for(var i=0;i<kf.length;i++){var a=kf[i]+r;if(a in e)return Tf[t]=a}return t}function Cf(e,t){var n=e.style;if(wn(t))""===t?e.removeAttribute("style"):n.cssText=ff(t);else for(var r in t)Of(n,r,t[r]);var{__wxsStyle:i}=e;if(i)for(var a in i)Of(n,a,i[a])}var Mf=/\s*!important$/;function Of(e,t,n){if(gn(n))n.forEach((n=>Of(e,t,n)));else if(n=ff(n),t.startsWith("--"))e.setProperty(t,n);else{var r=Ef(e,t);Mf.test(n)?e.setProperty(Nn(r),n.replace(Mf,""),"important"):e[r]=n}}function If(e,t){var n=e.__listeners[t];n&&e.removeEventListener(t,n)}function Lf(e,t){if(e.__listeners[t])return!0}function Af(e,t,n){var[r,i]=fr(t);-1===n?If(e,r):Lf(e,r)||e.addEventListener(r,e.__listeners[r]=Bf(e.__id,n,i),i)}function Bf(e,t,n){var r=t=>{var[r]=Yu(t);r.type=function(e,t){return t&&(t.capture&&(e+="Capture"),t.once&&(e+="Once"),t.passive&&(e+="Passive")),"on".concat(Rn(An(e)))}(t.type,n),UniViewJSBridge.publishHandler(Ju,[[20,e,r]])};return t?Kl(r,Nf(t)):r}function Nf(e){var t=[];return e&pr.prevent&&t.push("prevent"),e&pr.self&&t.push("self"),e&pr.stop&&t.push("stop"),t}function Rf(e,t,n){var r=n=>{!function(e,t,n){var[r,i,a]=nf(t),[o,s]=a.split("."),l=tf(e,r);rf(l,i,o,s,[of(n,e),Uu(qu(l))])}(function(e){return!!e.addWxsEvent}(e)?e.$:e,t,Yu(n)[0])};return n?Kl(r,Nf(n)):r}function Pf(e,t){e._vod="none"===e.style.display?"":e.style.display,e.style.display=t?e._vod:"none"}class Df extends df{constructor(e,t,n,r,i){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:[];super(e,t.tagName,n,t),this.$props=ma({}),this.$.__id=e,this.$.__listeners=Object.create(null),this.$propNames=a,this._update=this.update.bind(this),this.init(i),this.insert(n,r)}init(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];vn(e,"a")&&this.setAttrs(e.a),vn(e,"s")&&this.setAttr("style",e.s),vn(e,"e")&&this.addEvents(e.e),vn(e,"w")&&this.addWxsEvents(e.w),super.init(e),t&&(xo(this.$props,(()=>{Kh(this._update,1)}),{flush:"sync"}),this.update(!0))}setAttrs(e){this.setWxsProps(e),Object.keys(e).forEach((t=>{this.setAttr(t,e[t])}))}addEvents(e){Object.keys(e).forEach((t=>{this.addEvent(t,e[t])}))}addWxsEvent(e,t,n){!function(e,t,n,r){var[i,a]=fr(t);-1===r?If(e,i):Lf(e,i)||e.addEventListener(i,e.__listeners[i]=Rf(e,n,r),a)}(this.$,e,t,n)}addEvent(e,t){Af(this.$,e,t)}removeEvent(e){Af(this.$,e,-1)}setAttr(e,t){e===vr?hf(this.$,t):e===gr?Cf(this.$,t):e===mr?Pf(this.$,t):e===_r?this.$.__ownerId=t:e===yr?Kh((()=>sf(this,t)),3):"innerHTML"===e?this.$.innerHTML=t:"textContent"===e?this.setText(t):this.setAttribute(e,t),this.updateView()}removeAttr(e){e===vr?hf(this.$,""):e===gr?Cf(this.$,""):this.removeAttribute(e),this.updateView()}setAttribute(e,t){t=uf(t,this.$),-1!==this.$propNames.indexOf(e)?this.$props[e]=t:cf(e)?this.$.style.setProperty(e,ff(t)):this.wxsPropsInvoke(e,t)||this.$.setAttribute(e,t)}removeAttribute(e){-1!==this.$propNames.indexOf(e)?delete this.$props[e]:cf(e)?this.$.style.removeProperty(e):this.$.removeAttribute(e)}update(){}}function zf(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,((e,t)=>"".concat(uni.upx2px(parseFloat(t)),"px"))):/^-?[\d\.]+$/.test(e)?"".concat(e,"px"):e||""}function Ff(e){var t=e.animation;if(t&&t.actions&&t.actions.length){var n=0,r=t.actions,i=t.actions.length;setTimeout((()=>{a()}),0)}function a(){var t=r[n],o=t.option.transition,s=function(e){var t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],r=["opacity","background-color"],i=["width","height","left","right","top","bottom"],a=e.animates,o=e.option,s=o.transition,l={},u=[];return a.forEach((e=>{var a=e.type,o=[...e.args];if(t.concat(n).includes(a))a.startsWith("rotate")||a.startsWith("skew")?o=o.map((e=>parseFloat(e)+"deg")):a.startsWith("translate")&&(o=o.map(zf)),n.indexOf(a)>=0&&(o.length=1),u.push("".concat(a,"(").concat(o.join(","),")"));else if(r.concat(i).includes(o[0])){a=o[0];var s=o[1];l[a]=i.includes(a)?zf(s):s}})),l.transform=l.webkitTransform=u.join(" "),l.transition=l.webkitTransition=Object.keys(l).map((e=>"".concat(function(e){return e.replace(/[A-Z]/g,(e=>"-".concat(e.toLowerCase()))).replace("webkit","-webkit")}(e)," ").concat(s.duration,"ms ").concat(s.timingFunction," ").concat(s.delay,"ms"))).join(","),l.transformOrigin=l.webkitTransformOrigin=o.transformOrigin,l}(t);Object.keys(s).forEach((t=>{e.$el.style[t]=s[t]})),(n+=1)<i&&setTimeout(a,o.duration+o.delay)}}const $f={props:["animation"],watch:{animation:{deep:!0,handler(){Ff(this)}}},mounted(){Ff(this)}};var Vf=e=>{e.__reserved=!0;var{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push($f),jf(e)},jf=e=>(e.__reserved=!0,e.compatConfig={MODE:3},function(e){return bn(e)?{setup:e,name:e.name}:e}(e)),Wf={hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}};function Uf(e){var t,n,r=Aa(!1),i=!1;function a(){requestAnimationFrame((()=>{clearTimeout(n),n=setTimeout((()=>{r.value=!1}),parseInt(e.hoverStayTime))}))}function o(n){n._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(n._hoverPropagationStopped=!0),i=!0,t=setTimeout((()=>{r.value=!0,i||a()}),parseInt(e.hoverStartTime)))}function s(){i=!1,r.value&&a()}function l(){s(),window.removeEventListener("mouseup",l)}return{hovering:r,binding:{onTouchstartPassive:function(e){e.touches.length>1||o(e)},onMousedown:function(e){i||(o(e),window.addEventListener("mouseup",l))},onTouchend:function(){s()},onMouseup:function(){i&&l()},onTouchcancel:function(){i=!1,r.value=!1,clearTimeout(t)}}}}function Hf(e,t){return wn(t)&&(t=[t]),t.reduce(((t,n)=>(e[n]&&(t[n]=!0),t)),Object.create(null))}function qf(e){return e.__wwe=!0,e}function Yf(e,t){return(n,r,i)=>{e.value&&t(n,function(e,t,n,r){var i=cr(n);return{type:r.type||e,timeStamp:t.timeStamp||0,target:i,currentTarget:i,detail:r}}(n,r,e.value,i||{}))}}var Xf=_u("uf");const Zf=Vf({name:"Form",emits:["submit","reset"],setup(e,t){var n,r,{slots:i,emit:a}=t,o=Aa(null);return n=Yf(o,a),r=[],_o(Xf,{addField(e){r.push(e)},removeField(e){r.splice(r.indexOf(e),1)},submit(e){n("submit",e,{value:r.reduce(((e,t)=>{if(t.submit){var[n,r]=t.submit();n&&(e[n]=r)}return e}),Object.create(null))})},reset(e){r.forEach((e=>e.reset&&e.reset())),n("reset",e)}}),()=>Ws("uni-form",{ref:o},[Ws("span",null,[i.default&&i.default()])],512)}});var Gf={for:{type:String,default:""}},Kf=_u("ul");const Jf=Vf({name:"Label",props:Gf,setup(e,t){var{slots:n}=t,r=Tu(),i=function(){var e=[];return _o(Kf,{addHandler(t){e.push(t)},removeHandler(t){e.splice(e.indexOf(t),1)}}),e}(),a=dl((()=>e.for||n.default&&n.default.length)),o=qf((t=>{var n=t.target,a=/^uni-(checkbox|radio|switch)-/.test(n.className);a||(a=/^uni-(checkbox|radio|switch|button)$|^(svg|path)$/i.test(n.tagName)),a||(e.for?UniViewJSBridge.emit("uni-label-click-"+r+"-"+e.for,t,!0):i.length&&i[0](t,!0))}));return()=>Ws("uni-label",{class:{"uni-label-pointer":a},onClick:o},[n.default&&n.default()],10,["onClick"])}});function Qf(e,t){ep(e.id,t),xo((()=>e.id),((e,n)=>{tp(n,t,!0),ep(e,t,!0)})),$o((()=>{tp(e.id,t)}))}function ep(e,t,n){var r=Tu();n&&!e||Cn(t)&&Object.keys(t).forEach((i=>{n?0!==i.indexOf("@")&&0!==i.indexOf("uni-")&&UniViewJSBridge.on("uni-".concat(i,"-").concat(r,"-").concat(e),t[i]):0===i.indexOf("uni-")?UniViewJSBridge.on(i,t[i]):e&&UniViewJSBridge.on("uni-".concat(i,"-").concat(r,"-").concat(e),t[i])}))}function tp(e,t,n){var r=Tu();n&&!e||Cn(t)&&Object.keys(t).forEach((i=>{n?0!==i.indexOf("@")&&0!==i.indexOf("uni-")&&UniViewJSBridge.off("uni-".concat(i,"-").concat(r,"-").concat(e),t[i]):0===i.indexOf("uni-")?UniViewJSBridge.off(i,t[i]):e&&UniViewJSBridge.off("uni-".concat(i,"-").concat(r,"-").concat(e),t[i])}))}const np=Vf({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,t){var{slots:n}=t,r=Aa(null);Wr();var i=yo(Xf,!1),{hovering:a,binding:o}=Uf(e),{t:s}=$r(),l=qf(((t,n)=>{if(e.disabled)return t.stopImmediatePropagation();n&&r.value.click();var a=e.formType;if(a){if(!i)return;"submit"===a?i.submit(t):"reset"===a&&i.reset(t)}else{var o,l,u;"feedback"===e.openType&&(o=s("uni.button.feedback.title"),l=s("uni.button.feedback.send"),(u=plus.webview.create("https://service.dcloud.net.cn/uniapp/feedback.html","feedback",{titleNView:{titleText:o,autoBackButton:!0,backgroundColor:"#F7F7F7",titleColor:"#007aff",buttons:[{text:l,color:"#007aff",fontSize:"16px",fontWeight:"bold",onclick:function(){u.evalJS('typeof mui !== "undefined" && mui.trigger(document.getElementById("submit"),"tap")')}}]}})).show("slide-in-right"))}})),u=yo(Kf,!1);return u&&(u.addHandler(l),Fo((()=>{u.removeHandler(l)}))),Qf(e,{"label-click":l}),()=>{var t=e.hoverClass,i=Hf(e,"disabled"),s=Hf(e,"loading"),u=Hf(e,"plain"),c=t&&"none"!==t;return Ws("uni-button",Gs({ref:r,onClick:l,class:c&&a.value?t:""},c&&o,i,s,u),[n.default&&n.default()],16,["onClick"])}}});const rp=Vf({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,t){var{emit:n}=t,r=Aa(null),i=function(e){return()=>{var{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}(r),a=function(e,t,n){var r=ma({width:-1,height:-1});return xo((()=>hn({},r)),(e=>t("resize",e))),()=>{var t=e.value;r.width=t.offsetWidth,r.height=t.offsetHeight,n()}}(r,n,i);return function(e,t,n,r){Oo(r),Po((()=>{t.initial&&Ja(n);var i=e.value;i.offsetParent!==i.parentElement&&(i.parentElement.style.position="relative"),"AnimationEvent"in window||r()}))}(r,e,a,i),()=>Ws("uni-resize-sensor",{ref:r,onAnimationstartOnce:a},[Ws("div",{onScroll:a},[Ws("div",null,null)],40,["onScroll"]),Ws("div",{onScroll:a},[Ws("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});var ip=function(){var e=document.createElement("canvas");e.height=e.width=0;var t=e.getContext("2d"),n=t.backingStorePixelRatio||t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1;return(window.devicePixelRatio||1)/n}();function ap(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];e.width=e.offsetWidth*(t?ip:1),e.height=e.offsetHeight*(t?ip:1),e.getContext("2d").__hidpi__=t}var op=!1;var sp,lp=rr((()=>function(){if(!op){op=!0;var e,t=CanvasRenderingContext2D.prototype;t.drawImageByCanvas=(e=t.drawImage,function(t,n,r,i,a,o,s,l,u,c){if(!this.__hidpi__)return e.apply(this,arguments);n*=ip,r*=ip,i*=ip,a*=ip,o*=ip,s*=ip,l=c?l*ip:l,u=c?u*ip:u,e.call(this,t,n,r,i,a,o,s,l,u)}),1!==ip&&(function(e,t){for(var n in e)vn(e,n)&&t(e[n],n)}({fillRect:"all",clearRect:"all",strokeRect:"all",moveTo:"all",lineTo:"all",arc:[0,1,2],arcTo:"all",bezierCurveTo:"all",isPointinPath:"all",isPointinStroke:"all",quadraticCurveTo:"all",rect:"all",translate:"all",createRadialGradient:"all",createLinearGradient:"all",transform:[4,5],setTransform:[4,5]},(function(e,n){t[n]=function(t){return function(){if(!this.__hidpi__)return t.apply(this,arguments);var n=Array.prototype.slice.call(arguments);if("all"===e)n=n.map((function(e){return e*ip}));else if(Array.isArray(e))for(var r=0;r<e.length;r++)n[e[r]]*=ip;return t.apply(this,n)}}(t[n])})),t.stroke=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);this.lineWidth*=ip,e.apply(this,arguments),this.lineWidth/=ip}}(t.stroke),t.fillText=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);var t=Array.prototype.slice.call(arguments);t[1]*=ip,t[2]*=ip,t[3]&&"number"==typeof t[3]&&(t[3]*=ip);var n=this.font;this.font=n.replace(/(\d+\.?\d*)(px|em|rem|pt)/g,(function(e,t,n){return t*ip+n})),e.apply(this,t),this.font=n}}(t.fillText),t.strokeText=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);var t=Array.prototype.slice.call(arguments);t[1]*=ip,t[2]*=ip,t[3]&&"number"==typeof t[3]&&(t[3]*=ip);var n=this.font;this.font=n.replace(/(\d+\.?\d*)(px|em|rem|pt)/g,(function(e,t,n){return t*ip+n})),e.apply(this,t),this.font=n}}(t.strokeText),t.drawImage=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);this.scale(ip,ip),e.apply(this,arguments),this.scale(1/ip,1/ip)}}(t.drawImage))}}()));function up(e){return e?ac(e):e}function cp(e){return(e=e.slice(0))[3]=e[3]/255,"rgba("+e.join(",")+")"}function dp(e,t){Array.from(t).forEach((t=>{t.x=t.clientX-e.left,t.y=t.clientY-e.top}))}function hp(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return sp||(sp=document.createElement("canvas")),sp.width=e,sp.height=t,sp}const fp=Vf({inheritAttrs:!1,name:"Canvas",compatConfig:{MODE:3},props:{canvasId:{type:String,default:""},disableScroll:{type:[Boolean,String],default:!1},hidpi:{type:Boolean,default:!0}},computed:{id(){return this.canvasId}},setup(e,t){var{emit:n,slots:r}=t;lp();var i=Aa(null),a=Aa(null),o=Aa(!1),s=function(e){return(t,n)=>{e(t,Zu(n))}}(n),{$attrs:l,$excludeAttrs:u,$listeners:c}=Lv({excludeListeners:!0}),{_listeners:d}=function(e,t,n){return{_listeners:dl((()=>{var r=["onTouchstart","onTouchmove","onTouchend"],i=t.value,a=hn({},(()=>{var e={};for(var t in i)if(vn(i,t)){var n=i[t];e[t]=n}return e})());return r.forEach((t=>{var r=[];a[t]&&r.push(qf((e=>{var r=e.currentTarget.getBoundingClientRect();dp(r,e.touches),dp(r,e.changedTouches),n(t.replace("on","").toLocaleLowerCase(),e)}))),e.disableScroll&&"onTouchmove"===t&&r.push(vu),a[t]=r})),a}))}}(e,c,s),{_handleSubscribe:h,_resize:f}=function(e,t,n){var r=[],i={},a=dl((()=>e.hidpi?ip:1));function o(n){var r=t.value;if(!n||r.width!==Math.floor(n.width*a.value)||r.height!==Math.floor(n.height*a.value))if(r.width>0&&r.height>0){var i=r.getContext("2d"),o=i.getImageData(0,0,r.width,r.height);ap(r,e.hidpi),i.putImageData(o,0,0)}else ap(r,e.hidpi)}function s(e,a){var{actions:o,reserve:s}=e;if(o)if(n.value)r.push([o,s]);else{var c=t.value,d=c.getContext("2d");s||(d.fillStyle="#000000",d.strokeStyle="#000000",d.shadowColor="#000000",d.shadowBlur=0,d.shadowOffsetX=0,d.shadowOffsetY=0,d.setTransform(1,0,0,1,0,0),d.clearRect(0,0,c.width,c.height)),l(o);for(var h=function(e){var t=o[e],n=t.method,r=t.data,s=r[0];if(/^set/.test(n)&&"setTransform"!==n){var l,c=n[3].toLowerCase()+n.slice(4);if("fillStyle"===c||"strokeStyle"===c){if("normal"===s)l=cp(r[1]);else if("linear"===s){var h=d.createLinearGradient(...r[1]);r[2].forEach((function(e){var t=e[0],n=cp(e[1]);h.addColorStop(t,n)})),l=h}else if("radial"===s){var f=r[1],p=f[0],v=f[1],g=f[2],m=d.createRadialGradient(p,v,0,p,v,g);r[2].forEach((function(e){var t=e[0],n=cp(e[1]);m.addColorStop(t,n)})),l=m}else if("pattern"===s){return u(r[1],o.slice(e+1),a,(function(e){e&&(d[c]=d.createPattern(e,r[2]))}))?"continue":"break"}d[c]=l}else if("globalAlpha"===c)d[c]=Number(s)/255;else if("shadow"===c){var _=["shadowOffsetX","shadowOffsetY","shadowBlur","shadowColor"];r.forEach((function(e,t){d[_[t]]="shadowColor"===_[t]?cp(e):e}))}else if("fontSize"===c){var y=d.__font__||d.font;d.__font__=d.font=y.replace(/\d+\.?\d*px/,s+"px")}else"lineDash"===c?(d.setLineDash(s),d.lineDashOffset=r[1]||0):"textBaseline"===c?("normal"===s&&(r[0]="alphabetic"),d[c]=s):"font"===c?d.__font__=d.font=s:d[c]=s}else if("fillPath"===n||"strokePath"===n)n=n.replace(/Path/,""),d.beginPath(),r.forEach((function(e){d[e.method].apply(d,e.data)})),d[n]();else if("fillText"===n)d.fillText.apply(d,r);else if("drawImage"===n){if("break"===function(){var t=[...r],n=t[0],s=t.slice(1);if(i=i||{},!u(n,o.slice(e+1),a,(function(e){e&&d.drawImage.apply(d,[e].concat([...s.slice(4,8)],[...s.slice(0,4)]))})))return"break"}())return"break"}else"clip"===n?(r.forEach((function(e){d[e.method].apply(d,e.data)})),d.clip()):d[n].apply(d,r)},f=0;f<o.length;f++){var p=h(f);if("break"===p)break}n.value||a({errMsg:"drawCanvas:ok"})}}function l(e){e.forEach((function(e){var t=e.method,n=e.data,r="";function a(){var e=i[r]=new Image;if(e.onload=function(){e.ready=!0},"Google Inc."===navigator.vendor)return 0===r.indexOf("file://")&&(e.crossOrigin="anonymous"),void(e.src=r);lc(r).then((t=>{e.src=t})).catch((()=>{e.src=r}))}"drawImage"===t?(r=up(r=n[0]),n[0]=r):"setFillStyle"===t&&"pattern"===n[0]&&(r=up(r=n[1]),n[1]=r),r&&!i[r]&&a()}))}function u(e,t,a,o){var l=i[e];return l.ready?(o(l),!0):(r.unshift([t,!0]),n.value=!0,l.onload=function(){l.ready=!0,o(l),n.value=!1;var e=r.slice(0);r=[];for(var t=e.shift();t;)s({actions:t[0],reserve:t[1]},a),t=e.shift()},!1)}function c(e,n){var r,{x:i=0,y:o=0,width:s,height:l,destWidth:u,destHeight:c,hidpi:d=!0,dataType:h,quality:f=1,type:p="png"}=e,v=t.value,g=v.offsetWidth-i;s=s?Math.min(s,g):g;var m=v.offsetHeight-o;l=l?Math.min(l,m):m,d?(u=s,c=l):u||c?u?c||(c=Math.round(l/s*u)):u=Math.round(s/l*c):(u=Math.round(s*a.value),c=Math.round(l*a.value));var _,y=hp(u,c),b=y.getContext("2d");"jpeg"!==p&&"jpg"!==p||(p="jpeg",b.fillStyle="#fff",b.fillRect(0,0,u,c)),b.__hidpi__=!0,b.drawImageByCanvas(v,i,o,s,l,0,0,u,c,!1);try{var w;if("base64"===h)r=y.toDataURL("image/".concat(p),f);else{var x=b.getImageData(0,0,u,c);r=Oh.deflateRaw(x.data,{to:"string"}),w=!0}_={data:r,compressed:w,width:u,height:c}}catch(S){_={errMsg:"canvasGetImageData:fail ".concat(S)}}if(y.height=y.width=0,b.__hidpi__=!1,!n)return _;n(_)}function d(e,n){var{data:r,x:i,y:a,width:o,height:s,compressed:l}=e;try{l&&(r=Oh.inflateRaw(r)),s||(s=Math.round(r.length/4/o));var u=hp(o,s);u.getContext("2d").putImageData(new ImageData(new Uint8ClampedArray(r),o,s),0,0),t.value.getContext("2d").drawImage(u,i,a,o,s),u.height=u.width=0}catch(c){return void n({errMsg:"canvasPutImageData:fail"})}n({errMsg:"canvasPutImageData:ok"})}function h(e,t){var{x:n=0,y:r=0,width:i,height:a,destWidth:o,destHeight:s,fileType:l,quality:u,dirname:d}=e,h=c({x:n,y:r,width:i,height:a,destWidth:o,destHeight:s,hidpi:!1,dataType:"base64",type:l,quality:u});h.data&&h.data.length?function(e,t,n){var r="".concat(Date.now()).concat(sc++),i=e.split(","),a=i[0],o=i[1],s=(a.match(/data:image\/(\S+?);/)||["","png"])[1].replace("jpeg","jpg"),l="".concat(r,".").concat(s),u="".concat(t,"/").concat(l),c=t.indexOf("/"),d=t.substring(0,c),h=t.substring(c+1);plus.io.resolveLocalFileSystemURL(d,(function(e){e.getDirectory(h,{create:!0,exclusive:!1},(function(e){e.getFile(l,{create:!0,exclusive:!1},(function(e){e.createWriter((function(e){e.onwrite=function(){n(null,u)},e.onerror=n,e.seek(0),e.writeAsBinary(o)}),n)}),n)}),n)}),n)}(h.data,d,((e,n)=>{var r="toTempFilePath:".concat(e?"fail":"ok");e&&(r+=" ".concat(e.message)),t({errMsg:r,tempFilePath:n})})):t({errMsg:h.errMsg.replace("canvasPutImageData","toTempFilePath")})}var f={actionsChanged:s,getImageData:c,putImageData:d,toTempFilePath:h};function p(e,t,n){var r=f[e];0!==e.indexOf("_")&&bn(r)&&r(t,n)}return hn(f,{_resize:o,_handleSubscribe:p})}(e,i,o);return Hg(h,Yg(e.canvasId),!0),Po((()=>{f()})),()=>{var{canvasId:t,disableScroll:n}=e;return Ws("uni-canvas",Gs({"canvas-id":t,"disable-scroll":n},l.value,u.value,d.value),[Ws("canvas",{ref:i,class:"uni-canvas-canvas",width:"300",height:"150"},null,512),Ws("div",{style:"position: absolute;top: 0;left: 0;width: 100%;height: 100%;overflow: hidden;"},[r.default&&r.default()]),Ws(rp,{ref:a,onResize:f},null,8,["onResize"])],16,["canvas-id","disable-scroll"])}}});var pp=_u("ucg");const vp=Vf({name:"CheckboxGroup",props:{name:{type:String,default:""}},emits:["change"],setup(e,t){var{emit:n,slots:r}=t,i=Aa(null);return function(e,t){var n=[],r=()=>n.reduce(((e,t)=>(t.value.checkboxChecked&&e.push(t.value.value),e)),new Array);_o(pp,{addField(e){n.push(e)},removeField(e){n.splice(n.indexOf(e),1)},checkboxChange(e){t("change",e,{value:r()})}});var i=yo(Xf,!1);i&&i.addField({submit:()=>{var t=["",null];return""!==e.name&&(t[0]=e.name,t[1]=r()),t}})}(e,Yf(i,n)),()=>Ws("uni-checkbox-group",{ref:i},[r.default&&r.default()],512)}});const gp=Vf({name:"Checkbox",props:{checked:{type:[Boolean,String],default:!1},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},value:{type:String,default:""},color:{type:String,default:"#007aff"},backgroundColor:{type:String,default:""},borderColor:{type:String,default:""},activeBackgroundColor:{type:String,default:""},activeBorderColor:{type:String,default:""},iconColor:{type:String,default:""}},setup(e,t){var{slots:n}=t,r=Aa(e.checked),i=Aa(e.value),a=dl((()=>{if(e.disabled)return{backgroundColor:"#E1E1E1",borderColor:"#D1D1D1"};var t={};return r.value?(e.activeBorderColor&&(t.borderColor=e.activeBorderColor),e.activeBackgroundColor&&(t.backgroundColor=e.activeBackgroundColor)):(e.borderColor&&(t.borderColor=e.borderColor),e.backgroundColor&&(t.backgroundColor=e.backgroundColor)),t}));xo([()=>e.checked,()=>e.value],(e=>{var[t,n]=e;r.value=t,i.value=n}));var{uniCheckGroup:o,uniLabel:s}=function(e,t,n){var r=dl((()=>({checkboxChecked:Boolean(e.value),value:t.value}))),i={reset:n},a=yo(pp,!1);a&&a.addField(r);var o=yo(Xf,!1);o&&o.addField(i);var s=yo(Kf,!1);return Fo((()=>{a&&a.removeField(r),o&&o.removeField(i)})),{uniCheckGroup:a,uniForm:o,uniLabel:s}}(r,i,(()=>{r.value=!1})),l=t=>{e.disabled||(r.value=!r.value,o&&o.checkboxChange(t),t.stopPropagation())};return s&&(s.addHandler(l),Fo((()=>{s.removeHandler(l)}))),Qf(e,{"label-click":l}),()=>{var t=Hf(e,"disabled");return Ws("uni-checkbox",Gs(t,{onClick:l}),[Ws("div",{class:"uni-checkbox-wrapper",style:{"--HOVER-BD-COLOR":e.activeBorderColor}},[Ws("div",{class:["uni-checkbox-input",{"uni-checkbox-input-disabled":e.disabled}],style:a.value},[r.value?ku(Su,e.disabled?"#ADADAD":e.iconColor||e.color,22):""],6),n.default&&n.default()],4)],16,["onClick"])}}});var mp,_p,yp,bp,wp,xp;function Sp(){}function kp(e,t,n){dr((()=>{var r="adjustResize",i="adjustPan",a=plus.webview.currentWebview(),o=xp||a.getStyle()||{},s={mode:n||o.softinputMode===r?r:e.adjustPosition?i:"nothing",position:{top:0,height:0}};if(s.mode===i){var l=t.getBoundingClientRect();s.position.top=l.top,s.position.height=l.height+(Number(e.cursorSpacing)||0)}a.setSoftinputTemporary(s)}))}dr((()=>{_p="Android"===plus.os.name,yp=plus.os.version||""})),document.addEventListener("keyboardchange",(function(e){bp=e.height,wp&&wp()}),!1);var Tp={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}},Ep=["keyboardheightchange"];function Cp(e,t,n){var r={};function i(t){var i,a=dl((()=>0===String(navigator.vendor).indexOf("Apple"))),o=()=>{n("keyboardheightchange",{},{height:bp,duration:.25}),i&&0===bp&&kp(e,t),e.autoBlur&&i&&0===bp&&(_p||parseInt(yp)>=13)&&document.activeElement.blur()};t.addEventListener("focus",(()=>{i=!0,clearTimeout(mp),document.addEventListener("click",Sp,!1),wp=o,bp&&n("keyboardheightchange",{},{height:bp,duration:0}),function(e,t){"auto"!==e.showConfirmBar?dr((()=>{var n=plus.webview.currentWebview(),{softinputNavBar:r}=n.getStyle()||{};"none"!==r!==e.showConfirmBar?(t.softinputNavBar=r||"auto",n.setStyle({softinputNavBar:e.showConfirmBar?"auto":"none"})):delete t.softinputNavBar})):delete t.softinputNavBar}(e,r),kp(e,t)})),_p&&t.addEventListener("click",(()=>{e.disabled||e.readOnly||!i||0!==bp||kp(e,t)})),_p||(parseInt(yp)<12&&t.addEventListener("touchstart",(()=>{e.disabled||e.readOnly||i||kp(e,t)})),parseFloat(yp)>=14.6&&!xp&&dr((()=>{var e=plus.webview.currentWebview();xp=e.getStyle()||{}})));var s=()=>{document.removeEventListener("click",Sp,!1),wp=null,bp&&n("keyboardheightchange",{},{height:0,duration:0}),function(e){var t=e.softinputNavBar;t&&dr((()=>{plus.webview.currentWebview().setStyle({softinputNavBar:t})}))}(r),_p&&(mp=setTimeout((()=>{kp(e,t,!0)}),300)),a.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)};t.addEventListener("blur",(()=>{a.value&&t.blur(),i=!1,s()}))}xo((()=>t.value),(e=>e&&i(e)))}var Mp=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,Op=/^<\/([-A-Za-z0-9_]+)[^>]*>/,Ip=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,Lp=zp("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),Ap=zp("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),Bp=zp("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),Np=zp("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),Rp=zp("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),Pp=zp("script,style");function Dp(e,t){var n,r,i,a=[],o=e;for(a.last=function(){return this[this.length-1]};e;){if(r=!0,a.last()&&Pp[a.last()])e=e.replace(new RegExp("([\\s\\S]*?)</"+a.last()+"[^>]*>"),(function(e,n){return n=n.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),t.chars&&t.chars(n),""})),u("",a.last());else if(0==e.indexOf("\x3c!--")?(n=e.indexOf("--\x3e"))>=0&&(t.comment&&t.comment(e.substring(4,n)),e=e.substring(n+3),r=!1):0==e.indexOf("</")?(i=e.match(Op))&&(e=e.substring(i[0].length),i[0].replace(Op,u),r=!1):0==e.indexOf("<")&&(i=e.match(Mp))&&(e=e.substring(i[0].length),i[0].replace(Mp,l),r=!1),r){var s=(n=e.indexOf("<"))<0?e:e.substring(0,n);e=n<0?"":e.substring(n),t.chars&&t.chars(s)}if(e==o)throw"Parse Error: "+e;o=e}function l(e,n,r,i){if(n=n.toLowerCase(),Ap[n])for(;a.last()&&Bp[a.last()];)u("",a.last());if(Np[n]&&a.last()==n&&u("",n),(i=Lp[n]||!!i)||a.push(n),t.start){var o=[];r.replace(Ip,(function(e,t){var n=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:Rp[t]?t:"";o.push({name:t,value:n,escaped:n.replace(/(^|[^\\])"/g,'$1\\"')})})),t.start&&t.start(n,o,i)}}function u(e,n){if(n)for(r=a.length-1;r>=0&&a[r]!=n;r--);else var r=0;if(r>=0){for(var i=a.length-1;i>=r;i--)t.end&&t.end(a[i]);a.length=r}}u()}function zp(e){for(var t={},n=e.split(","),r=0;r<n.length;r++)t[n[r]]=!0;return t}var Fp={};function $p(e,t,n){if(wn(e)?window[e]:e)n();else{var r=Fp[t];if(!r){r=Fp[t]=[];var i=document.createElement("script");i.src=t,document.body.appendChild(i),i.onload=function(){r.forEach((e=>e())),delete Fp[t]}}r.push(n)}}function Vp(e){var t=e.import("blots/block/embed");class n extends t{}return n.blotName="divider",n.tagName="HR",{"formats/divider":n}}function jp(e){var t=e.import("blots/inline");class n extends t{}return n.blotName="ins",n.tagName="INS",{"formats/ins":n}}function Wp(e){var{Scope:t,Attributor:n}=e.import("parchment"),r={scope:t.BLOCK,whitelist:["left","right","center","justify"]};return{"formats/align":new n.Style("align","text-align",r)}}function Up(e){var{Scope:t,Attributor:n}=e.import("parchment"),r={scope:t.BLOCK,whitelist:["rtl"]};return{"formats/direction":new n.Style("direction","direction",r)}}function Hp(e){var t=e.import("parchment"),n=e.import("blots/container"),r=e.import("formats/list/item");class i extends n{static create(e){var t="ordered"===e?"OL":"UL",n=super.create(t);return"checked"!==e&&"unchecked"!==e||n.setAttribute("data-checked","checked"===e),n}static formats(e){return"OL"===e.tagName?"ordered":"UL"===e.tagName?e.hasAttribute("data-checked")?"true"===e.getAttribute("data-checked")?"checked":"unchecked":"bullet":void 0}constructor(e){super(e);e.addEventListener("click",(n=>{if(n.target.parentNode===e){var r=this.statics.formats(e),i=t.find(n.target);"checked"===r?i.format("list","unchecked"):"unchecked"===r&&i.format("list","checked")}}))}format(e,t){this.children.length>0&&this.children.tail.format(e,t)}formats(){return{[this.statics.blotName]:this.statics.formats(this.domNode)}}insertBefore(e,t){if(e instanceof r)super.insertBefore(e,t);else{var n=null==t?this.length():t.offset(this),i=this.split(n);i.parent.insertBefore(e,i)}}optimize(e){super.optimize(e);var t=this.next;null!=t&&t.prev===this&&t.statics.blotName===this.statics.blotName&&t.domNode.tagName===this.domNode.tagName&&t.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(t.moveChildren(this),t.remove())}replace(e){if(e.statics.blotName!==this.statics.blotName){var n=t.create(this.statics.defaultChild);e.moveChildren(n),this.appendChild(n)}super.replace(e)}}return i.blotName="list",i.scope=t.Scope.BLOCK_BLOT,i.tagName=["OL","UL"],i.defaultChild="list-item",i.allowedChildren=[r],{"formats/list":i}}function qp(e){var{Scope:t}=e.import("parchment");return{"formats/backgroundColor":new(e.import("formats/background").constructor)("backgroundColor","background-color",{scope:t.INLINE})}}function Yp(e){var{Scope:t,Attributor:n}=e.import("parchment"),r={scope:t.BLOCK},i={};return["margin","marginTop","marginBottom","marginLeft","marginRight"].concat(["padding","paddingTop","paddingBottom","paddingLeft","paddingRight"]).forEach((e=>{i["formats/".concat(e)]=new n.Style(e,Nn(e),r)})),i}function Xp(e){var{Scope:t,Attributor:n}=e.import("parchment"),r={scope:t.INLINE},i={};return["font","fontSize","fontStyle","fontVariant","fontWeight","fontFamily"].forEach((e=>{i["formats/".concat(e)]=new n.Style(e,Nn(e),r)})),i}function Zp(e){var{Scope:t,Attributor:n}=e.import("parchment"),r=[{name:"lineHeight",scope:t.BLOCK},{name:"letterSpacing",scope:t.INLINE},{name:"textDecoration",scope:t.INLINE},{name:"textIndent",scope:t.BLOCK}],i={};return r.forEach((e=>{var{name:t,scope:r}=e;i["formats/".concat(t)]=new n.Style(t,Nn(t),{scope:r})})),i}function Gp(e){var t=e.import("formats/image"),n=["alt","height","width","data-custom","class","data-local"];t.sanitize=e=>e?ac(e):e,t.formats=function(e){return n.reduce((function(t,n){return e.hasAttribute(n)&&(t[n]=e.getAttribute(n)),t}),{})};var r=t.prototype.format;t.prototype.format=function(e,t){n.indexOf(e)>-1?t?this.domNode.setAttribute(e,t):this.domNode.removeAttribute(e):r.call(this,e,t)}}function Kp(e){var t=e.import("formats/link");t.sanitize=e=>{var n=document.createElement("a");n.href=e;var r=n.href.slice(0,n.href.indexOf(":"));return t.PROTOCOL_WHITELIST.concat("file").indexOf(r)>-1?e:t.SANITIZED_URL}}function Jp(e,t,n){var r,i,a;function o(){return{html:a.root.innerHTML,text:a.getText(),delta:a.getContents()}}function s(e){var t="data-placeholder",n=a.root;n.getAttribute(t)!==e&&n.setAttribute(t,e)}xo((()=>e.readOnly),(e=>{r&&(a.enable(!e),e||a.blur())})),xo((()=>e.placeholder),(e=>{r&&s(e)}));var l={};function u(e){var t=e?a.getFormat(e):{},r=Object.keys(t);(r.length!==Object.keys(l).length||r.find((e=>t[e]!==l[e])))&&(l=t,n("statuschange",{},t))}function c(){n("input",{},o())}function d(l){var d=window.Quill;!function(e){var t={divider:Vp,ins:jp,align:Wp,direction:Up,list:Hp,background:qp,box:Yp,font:Xp,text:Zp,image:Gp,link:Kp},n={};Object.values(t).forEach((t=>hn(n,t(e)))),e.register(n,!0)}(d);var h={toolbar:!1,readOnly:e.readOnly,placeholder:e.placeholder};l.length&&(d.register("modules/ImageResize",window.ImageResize.default),h.modules={ImageResize:{modules:l}});var f=t.value,p=(a=new d(f,h)).root;["focus","blur","input"].forEach((t=>{p.addEventListener(t,(r=>{var i=o();if("input"===t){if("ios"===ic().platform){var a=(i.html.match(/<span [\s\S]*>([\s\S]*)<\/span>/)||[])[1];s(a&&a.replace(/\s/g,"")?"":e.placeholder)}r.stopPropagation()}else n(t,r,i)}))})),a.on("text-change",c),a.on("selection-change",u),a.on("scroll-optimize",(()=>{u(a.selection.getRange()[0])})),a.clipboard.addMatcher(Node.ELEMENT_NODE,((e,t)=>(i||t.ops&&(t.ops=t.ops.filter((e=>{var{insert:t}=e;return wn(t)})).map((e=>{var{insert:t}=e;return{insert:t}}))),t))),r=!0,n("ready",{},{})}Hg(((e,t,n)=>{var s,l,d,{options:h,callbackId:f}=t;if(r){var p=window.Quill;switch(e){case"format":var{name:v="",value:g=!1}=h;l=a.getSelection(!0);var m=a.getFormat(l)[v]||!1;if(["bold","italic","underline","strike","ins"].includes(v))g=!m;else if("direction"===v){g=("rtl"!==g||!m)&&g;var _=a.getFormat(l).align;"rtl"!==g||_?g||"right"!==_||a.format("align",!1,"user"):a.format("align","right","user")}else if("indent"===v){g="+1"===g,"rtl"===a.getFormat(l).direction&&(g=!g),g=g?"+1":"-1"}else"list"===v&&(g="check"===g?"unchecked":g,m="checked"===m?"unchecked":m),g=m&&m!==(g||!1)||!m&&g?g:!m;a.format(v,g,"user");break;case"insertDivider":l=a.getSelection(!0),a.insertText(l.index,Yn,"user"),a.insertEmbed(l.index+1,"divider",!0,"user"),a.setSelection(l.index+2,0,"silent");break;case"insertImage":l=a.getSelection(!0);var{src:y="",alt:b="",width:w="",height:x="",extClass:S="",data:k={}}=h,T=ac(y);a.insertEmbed(l.index,"image",T,"silent");var E=!!/^(file|blob):/.test(T)&&T;a.formatText(l.index,1,"data-local",E,"silent"),a.formatText(l.index,1,"alt",b,"silent"),a.formatText(l.index,1,"width",w,"silent"),a.formatText(l.index,1,"height",x,"silent"),a.formatText(l.index,1,"class",S,"silent"),a.formatText(l.index,1,"data-custom",Object.keys(k).map((e=>"".concat(e,"=").concat(k[e]))).join("&"),"silent"),a.setSelection(l.index+1,0,"silent"),a.scrollIntoView(),setTimeout((()=>{c()}),1e3);break;case"insertText":l=a.getSelection(!0);var{text:C=""}=h;a.insertText(l.index,C,"user"),a.setSelection(l.index+C.length,0,"silent");break;case"setContents":var{delta:M,html:O}=h;"object"==typeof M?a.setContents(M,"silent"):wn(O)?a.setContents(function(e){var t,n=["span","strong","b","ins","em","i","u","a","del","s","sub","sup","img","div","p","h1","h2","h3","h4","h5","h6","hr","ol","ul","li","br"],r="";Dp(e,{start:function(e,i,a){if(n.includes(e)){t=!1;var o=i.map((e=>{var{name:t,value:n}=e;return"".concat(t,'="').concat(n,'"')})).join(" "),s="<".concat(e," ").concat(o," ").concat(a?"/":"",">");r+=s}else t=!a},end:function(e){t||(r+="</".concat(e,">"))},chars:function(e){t||(r+=e)}}),i=!0;var o=a.clipboard.convert(r);return i=!1,o}(O),"silent"):d="contents is missing";break;case"getContents":s=o();break;case"clear":a.setText("");break;case"removeFormat":l=a.getSelection(!0);var I=p.import("parchment");l.length?a.removeFormat(l.index,l.length,"user"):Object.keys(a.getFormat(l)).forEach((e=>{I.query(e,I.Scope.INLINE)&&a.format(e,!1)}));break;case"undo":a.history.undo();break;case"redo":a.history.redo();break;case"blur":a.blur();break;case"getSelectionText":s={text:""},(l=a.selection.savedRange)&&0!==l.length&&(s.text=a.getText(l.index,l.length));break;case"scrollIntoView":a.scrollIntoView()}u(l)}else d="not ready";f&&n({callbackId:f,data:hn({},s,{errMsg:"".concat(e,":").concat(d?"fail "+d:"ok")})})}),Yg(),!0),Po((()=>{var t=[];e.showImgSize&&t.push("DisplaySize"),e.showImgToolbar&&t.push("Toolbar"),e.showImgResize&&t.push("Resize");$p(window.Quill,"./__uniappquill.js",(()=>{if(t.length){$p(window.ImageResize,"./__uniappquillimageresize.js",(()=>{d(t)}))}else d(t)}))}))}const Qp=Vf({name:"Editor",props:hn({},Tp,{id:{type:String,default:""},readOnly:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},showImgSize:{type:[Boolean,String],default:!1},showImgToolbar:{type:[Boolean,String],default:!1},showImgResize:{type:[Boolean,String],default:!1}}),emit:["ready","focus","blur","input","statuschange",...Ep],setup(e,t){var{emit:n}=t,r=Aa(null),i=Yf(r,n);return Jp(e,r,i),Cp(e,r,i),()=>Ws("uni-editor",{ref:r,id:e.id,class:"ql-container"},null,8,["id"])}});var ev="#10aeff",tv="#b2b2b2",nv={success:{d:"M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM24.832 11.328l-11.264 11.104q-0.032 0.032-0.112 0.032t-0.112-0.032l-5.216-5.376q-0.096-0.128 0-0.288l0.704-0.96q0.032-0.064 0.112-0.064t0.112 0.032l4.256 3.264q0.064 0.032 0.144 0.032t0.112-0.032l10.336-8.608q0.064-0.064 0.144-0.064t0.112 0.064l0.672 0.672q0.128 0.128 0 0.224z",c:Xn},success_no_circle:{d:Su,c:Xn},info:{d:"M15.808 0.128q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.176 3.776-2.176 8.16 0 4.224 2.176 7.872 2.080 3.552 5.632 5.632 3.648 2.176 7.872 2.176 4.384 0 8.16-2.176 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.416-2.176-8.16-2.112-3.616-5.728-5.728-3.744-2.176-8.16-2.176zM16.864 23.776q0 0.064-0.064 0.064h-1.568q-0.096 0-0.096-0.064l-0.256-11.328q0-0.064 0.064-0.064h2.112q0.096 0 0.064 0.064l-0.256 11.328zM16 10.88q-0.576 0-0.976-0.4t-0.4-0.96 0.4-0.96 0.976-0.4 0.976 0.4 0.4 0.96-0.4 0.96-0.976 0.4z",c:ev},warn:{d:"M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z",c:"#f76260"},waiting:{d:"M15.84 0.096q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM23.008 21.92l-0.512 0.896q-0.096 0.128-0.224 0.064l-8-3.808q-0.096-0.064-0.16-0.128-0.128-0.096-0.128-0.288l0.512-12.096q0-0.064 0.048-0.112t0.112-0.048h1.376q0.064 0 0.112 0.048t0.048 0.112l0.448 10.848 6.304 4.256q0.064 0.064 0.080 0.128t-0.016 0.128z",c:ev},cancel:{d:"M20.928 10.176l-4.928 4.928-4.928-4.928-0.896 0.896 4.928 4.928-4.928 4.928 0.896 0.896 4.928-4.928 4.928 4.928 0.896-0.896-4.928-4.928 4.928-4.928-0.896-0.896zM16 2.080q-3.776 0-7.040 1.888-3.136 1.856-4.992 4.992-1.888 3.264-1.888 7.040t1.888 7.040q1.856 3.136 4.992 4.992 3.264 1.888 7.040 1.888t7.040-1.888q3.136-1.856 4.992-4.992 1.888-3.264 1.888-7.040t-1.888-7.040q-1.856-3.136-4.992-4.992-3.264-1.888-7.040-1.888zM16 28.64q-3.424 0-6.4-1.728-2.848-1.664-4.512-4.512-1.728-2.976-1.728-6.4t1.728-6.4q1.664-2.848 4.512-4.512 2.976-1.728 6.4-1.728t6.4 1.728q2.848 1.664 4.512 4.512 1.728 2.976 1.728 6.4t-1.728 6.4q-1.664 2.848-4.512 4.512-2.976 1.728-6.4 1.728z",c:"#f43530"},download:{d:"M15.808 1.696q-3.776 0-7.072 1.984-3.2 1.888-5.088 5.152-1.952 3.392-1.952 7.36 0 3.776 1.952 7.072 1.888 3.2 5.088 5.088 3.296 1.952 7.072 1.952 3.968 0 7.36-1.952 3.264-1.888 5.152-5.088 1.984-3.296 1.984-7.072 0-4-1.984-7.36-1.888-3.264-5.152-5.152-3.36-1.984-7.36-1.984zM20.864 18.592l-3.776 4.928q-0.448 0.576-1.088 0.576t-1.088-0.576l-3.776-4.928q-0.448-0.576-0.24-0.992t0.944-0.416h2.976v-8.928q0-0.256 0.176-0.432t0.4-0.176h1.216q0.224 0 0.4 0.176t0.176 0.432v8.928h2.976q0.736 0 0.944 0.416t-0.24 0.992z",c:Xn},search:{d:"M20.928 22.688q-1.696 1.376-3.744 2.112-2.112 0.768-4.384 0.768-3.488 0-6.464-1.728-2.88-1.696-4.576-4.608-1.76-2.976-1.76-6.464t1.76-6.464q1.696-2.88 4.576-4.576 2.976-1.76 6.464-1.76t6.464 1.76q2.912 1.696 4.608 4.576 1.728 2.976 1.728 6.464 0 2.272-0.768 4.384-0.736 2.048-2.112 3.744l9.312 9.28-1.824 1.824-9.28-9.312zM12.8 23.008q2.784 0 5.184-1.376 2.304-1.376 3.68-3.68 1.376-2.4 1.376-5.184t-1.376-5.152q-1.376-2.336-3.68-3.68-2.4-1.408-5.184-1.408t-5.152 1.408q-2.336 1.344-3.68 3.68-1.408 2.368-1.408 5.152t1.408 5.184q1.344 2.304 3.68 3.68 2.368 1.376 5.152 1.376zM12.8 23.008v0z",c:tv},clear:{d:"M16 0q-4.352 0-8.064 2.176-3.616 2.144-5.76 5.76-2.176 3.712-2.176 8.064t2.176 8.064q2.144 3.616 5.76 5.76 3.712 2.176 8.064 2.176t8.064-2.176q3.616-2.144 5.76-5.76 2.176-3.712 2.176-8.064t-2.176-8.064q-2.144-3.616-5.76-5.76-3.712-2.176-8.064-2.176zM22.688 21.408q0.32 0.32 0.304 0.752t-0.336 0.736-0.752 0.304-0.752-0.32l-5.184-5.376-5.376 5.184q-0.32 0.32-0.752 0.304t-0.736-0.336-0.304-0.752 0.32-0.752l5.376-5.184-5.184-5.376q-0.32-0.32-0.304-0.752t0.336-0.752 0.752-0.304 0.752 0.336l5.184 5.376 5.376-5.184q0.32-0.32 0.752-0.304t0.752 0.336 0.304 0.752-0.336 0.752l-5.376 5.184 5.184 5.376z",c:tv}};const rv=Vf({name:"Icon",props:{type:{type:String,required:!0,default:""},size:{type:[String,Number],default:23},color:{type:String,default:""}},setup(e){var t=dl((()=>nv[e.type]));return()=>{var{value:n}=t;return Ws("uni-icon",null,[n&&n.d&&ku(n.d,e.color||n.c,bu(e.size))])}}});var iv={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},av={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},ov={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]};const sv=Vf({name:"Image",props:iv,setup(e,t){var{emit:n}=t,r=Aa(null),i=function(e,t){var n=Aa(""),r=dl((()=>{var e="auto",r="",i=ov[t.mode];return i?(i[0]&&(r=i[0]),i[1]&&(e=i[1])):(r="0% 0%",e="100% 100%"),"background-image:".concat(n.value?'url("'+n.value+'")':"none",";background-position:").concat(r,";background-size:").concat(e,";")})),i=ma({rootEl:e,src:dl((()=>t.src?ac(t.src):"")),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:r,imgSrc:n});return Po((()=>{var t=e.value.style;i.origWidth=Number(t.width)||0,i.origHeight=Number(t.height)||0})),i}(r,e),a=Yf(r,n),{fixSize:o}=function(e,t,n){var r=()=>{var{mode:r}=t,i=av[r];if(i){var{origWidth:a,origHeight:o}=n,s=a&&o?a/o:0;if(s){var l=e.value,u=l[i[0]];u&&(l.style[i[1]]=function(e){lv&&e>10&&(e=2*Math.round(e/2));return e}(i[2](u,s))+"px"),window.dispatchEvent(new CustomEvent("updateview"))}}},i=()=>{var{style:t}=e.value,{origStyle:{width:r,height:i}}=n;t.width=r,t.height=i};return xo((()=>t.mode),((e,t)=>{av[t]&&i(),av[e]&&r()})),{fixSize:r,resetSize:i}}(r,e,i);return function(e,t,n,r,i){var a,o,s=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";e.origWidth=t,e.origHeight=n,e.imgSrc=r},l=l=>{if(!l)return u(),void s();(a=a||new Image).onload=e=>{var{width:c,height:d}=a;s(c,d,l),r(),a.draggable=t.draggable,o&&o.remove(),o=a,n.value.appendChild(a),u(),i("load",e,{width:c,height:d})},a.onerror=t=>{s(),u(),i("error",t,{errMsg:"GET ".concat(e.src," 404 (Not Found)")})},a.src=l},u=()=>{a&&(a.onload=null,a.onerror=null,a=null)};xo((()=>e.src),(e=>l(e))),xo((()=>e.imgSrc),(e=>{!e&&o&&(o.remove(),o=null)})),Po((()=>l(e.src))),Fo((()=>u()))}(i,e,r,o,a),()=>Ws("uni-image",{ref:r},[Ws("div",{style:i.modeStyle},null,4),av[e.mode]?Ws(rp,{onResize:o},null,8,["onResize"]):Ws("span",null,null)],512)}});var lv="Google Inc."===navigator.vendor;var uv,cv=ur(!0),dv=[],hv=0,fv=e=>dv.forEach((t=>t.userAction=e));function pv(){var e=ma({userAction:!1});return Po((()=>{!function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{userAction:!1};if(!uv){["touchstart","touchmove","touchend","mousedown","mouseup"].forEach((e=>{document.addEventListener(e,(function(){!hv&&fv(!0),hv++,setTimeout((()=>{!--hv&&fv(!1)}),0)}),cv)})),uv=!0}dv.push(e)}(e)})),Fo((()=>{var t,n;t=e,(n=dv.indexOf(t))>=0&&dv.splice(n,1)})),{state:e}}function vv(){var e=ma({attrs:{}});return Po((()=>{for(var t=tl();t;){var n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&"page"===t.proxy.$mpType?null:t.parent}})),{state:e}}function gv(e,t){var n=document.activeElement;if(!n)return t({});var r={};["input","textarea"].includes(n.tagName.toLowerCase())&&(r.start=n.selectionStart,r.end=n.selectionEnd),t(r)}var mv;function _v(e,t){return"number"===t&&isNaN(Number(e))&&(e=""),null===e?"":String(e)}var yv=["none","text","decimal","numeric","tel","search","email","url"],bv=hn({},{name:{type:String,default:""},modelValue:{type:[String,Number],default:""},value:{type:[String,Number],default:""},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~yv.indexOf(e)}},Tp),wv=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend",...Ep];function xv(e,t,n,r){var i=function(e,t,n){var r,{clearTimeout:i,setTimeout:a}=n,o=function(){i(r),r=a((()=>e.apply(this,arguments)),t)};return o.cancel=function(){i(r)},o}((n=>{t.value=_v(n,e.type)}),100,{setTimeout:setTimeout,clearTimeout:clearTimeout});xo((()=>e.modelValue),i),xo((()=>e.value),i);var a=function(e,t){var n,r,i=0,a=function(){for(var a=arguments.length,o=new Array(a),s=0;s<a;s++)o[s]=arguments[s];var l=Date.now();clearTimeout(n),r=()=>{r=null,i=l,e.apply(this,o)},l-i<t?n=setTimeout(r,t-(l-i)):r()};return a.cancel=function(){clearTimeout(n),r=null},a.flush=function(){clearTimeout(n),r&&r()},a}(((e,t)=>{i.cancel(),n("update:modelValue",t.value),n("update:value",t.value),r("input",e,t)}),100);return Ro((()=>{i.cancel(),a.cancel()})),{trigger:r,triggerInput:(e,t,n)=>{i.cancel(),a(e,t),n&&a.flush()}}}function Sv(e,t){var{state:n}=pv(),r=dl((()=>e.autoFocus||e.focus));function i(){if(r.value){var e=t.value;if(e&&"plus"in window){var a=200-(Date.now()-mv);a>0?setTimeout(i,a):(e.focus(),n.userAction||plus.key.showSoftKeybord())}else setTimeout(i,100)}}xo((()=>e.focus),(e=>{var n;e?i():(n=t.value)&&n.blur()})),Po((()=>{mv=mv||Date.now(),r.value&&Ja(i)}))}function kv(e,t,n,r){Gr(Eu(),"getSelectedTextRange",gv);var{fieldRef:i,state:a,trigger:o}=function(e,t,n){var r=Aa(null),i=Yf(t,n),a=dl((()=>{var t=Number(e.selectionStart);return isNaN(t)?-1:t})),o=dl((()=>{var t=Number(e.selectionEnd);return isNaN(t)?-1:t})),s=dl((()=>{var t=Number(e.cursor);return isNaN(t)?-1:t})),l=dl((()=>{var t=Number(e.maxlength);return isNaN(t)?140:t})),u=_v(e.modelValue,e.type)||_v(e.value,e.type),c=ma({value:u,valueOrigin:u,maxlength:l,focus:e.focus,composing:!1,selectionStart:a,selectionEnd:o,cursor:s});return xo((()=>c.focus),(e=>n("update:focus",e))),xo((()=>c.maxlength),(e=>c.value=c.value.slice(0,e))),{fieldRef:r,state:c,trigger:i}}(e,t,n),{triggerInput:s}=xv(e,a,n,o);Sv(e,i),Cp(e,i,o);var{state:l}=vv();return function(e,t){var n=yo(Xf,!1);if(n){var r=tl(),i={submit(){var n=r.proxy;return[n[e],wn(t)?n[t]:t.value]},reset(){wn(t)?r.proxy[t]="":t.value=""}};n.addField(i),Fo((()=>{n.removeField(i)}))}}("name",a),function(e,t,n,r,i,a){function o(){var n=e.value;n&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&"number"!==n.type&&(n.selectionStart=t.selectionStart,n.selectionEnd=t.selectionEnd)}function s(){var n=e.value;n&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&"number"!==n.type&&(n.selectionEnd=n.selectionStart=t.cursor)}function l(e){return"number"===e.type?null:e.selectionEnd}xo([()=>t.selectionStart,()=>t.selectionEnd],o),xo((()=>t.cursor),s),xo((()=>e.value),(function(){var u=e.value;if(u){var c=function(e,r){e.stopPropagation(),bn(a)&&!1===a(e,t)||(t.value=u.value,t.composing&&n.ignoreCompositionEvent||i(e,{value:u.value,cursor:l(u)},r))};u.addEventListener("change",(e=>e.stopPropagation())),u.addEventListener("focus",(function(e){t.focus=!0,r("focus",e,{value:t.value}),o(),s()})),u.addEventListener("blur",(function(e){t.composing&&(t.composing=!1,c(e,!0)),t.focus=!1,r("blur",e,{value:t.value,cursor:l(e.target)})})),u.addEventListener("input",c),u.addEventListener("compositionstart",(e=>{e.stopPropagation(),t.composing=!0,d(e)})),u.addEventListener("compositionend",(e=>{e.stopPropagation(),t.composing&&(t.composing=!1,c(e)),d(e)})),u.addEventListener("compositionupdate",d)}function d(e){n.ignoreCompositionEvent||r(e.type,e,{value:e.data})}}))}(i,a,e,o,s,r),{fieldRef:i,state:a,scopedAttrsState:l,fixDisabledColor:0===String(navigator.vendor).indexOf("Apple")&&CSS.supports("image-orientation:from-image"),trigger:o}}const Tv=Vf({name:"Input",props:hn({},bv,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),emits:["confirm",...wv],setup(e,t){var n,{emit:r}=t,i=["text","number","idcard","digit","password","tel"],a=["off","one-time-code"],o=dl((()=>{var t="";switch(e.type){case"text":"search"===e.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=~i.includes(e.type)?e.type:"text"}return e.password?"password":t})),s=dl((()=>{var t=a.indexOf(e.textContentType),n=a.indexOf(Nn(e.textContentType));return a[-1!==t?t:-1!==n?n:0]})),l=Aa(""),u=Aa(null),{fieldRef:c,state:d,scopedAttrsState:h,fixDisabledColor:f,trigger:p}=kv(e,u,r,((e,t)=>{var r=e.target;if("number"===o.value){if(n&&(r.removeEventListener("blur",n),n=null),r.validity&&!r.validity.valid){if((!l.value||!r.value)&&"-"===e.data||"-"===l.value[0]&&"deleteContentBackward"===e.inputType)return l.value="-",t.value="",n=()=>{l.value=r.value=""},r.addEventListener("blur",n),!1;if(l.value)if(-1!==l.value.indexOf(".")){if("."!==e.data&&"deleteContentBackward"===e.inputType){var i=l.value.indexOf(".");return l.value=r.value=t.value=l.value.slice(0,i),!0}}else if("."===e.data)return l.value+=".",n=()=>{l.value=r.value=l.value.slice(0,-1)},r.addEventListener("blur",n),!1;return l.value=t.value=r.value="-"===l.value?"":l.value,!1}l.value=r.value;var a=t.maxlength;if(a>0&&r.value.length>a)return r.value=r.value.slice(0,a),t.value=r.value,!1}}));xo((()=>d.value),(t=>{"number"!==e.type||"-"===l.value&&""===t||(l.value=t)}));var v=["number","digit"],g=dl((()=>v.includes(e.type)?e.step:""));function m(t){if("Enter"===t.key){var n=t.target;t.stopPropagation(),p("confirm",t,{value:n.value}),!e.confirmHold&&n.blur()}}return()=>{var t=e.disabled&&f?Ws("input",{key:"disabled-input",ref:c,value:d.value,tabindex:"-1",readonly:!!e.disabled,type:o.value,maxlength:d.maxlength,step:g.value,class:"uni-input-input",onFocus:e=>e.target.blur()},null,40,["value","readonly","type","maxlength","step","onFocus"]):Ho(Ws("input",{key:"input",ref:c,"onUpdate:modelValue":e=>d.value=e,disabled:!!e.disabled,type:o.value,maxlength:d.maxlength,step:g.value,enterkeyhint:e.confirmType,pattern:"number"===e.type?"[0-9]*":void 0,class:"uni-input-input",autocomplete:s.value,onKeyup:m,inputmode:e.inputmode},null,40,["onUpdate:modelValue","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]),[[Yl,d.value]]);return Ws("uni-input",{ref:u},[Ws("div",{class:"uni-input-wrapper"},[Ho(Ws("div",Gs(h.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Jl,!(d.value.length||"-"===l.value)]]),"search"===e.confirmType?Ws("form",{action:"",onSubmit:e=>e.preventDefault(),class:"uni-input-form"},[t],40,["onSubmit"]):t])],512)}}});function Ev(e){return Object.keys(e).map((t=>[t,e[t]]))}var Cv,Mv,Ov=["class","style"],Iv=/^on[A-Z]+/,Lv=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{excludeListeners:t=!1,excludeKeys:n=[]}=e,r=tl(),i=Ba({}),a=Ba({}),o=Ba({}),s=n.concat(Ov);return r.attrs=ma(r.attrs),bo((()=>{var e=Ev(r.attrs).reduce(((e,n)=>{var[r,i]=n;return s.includes(r)?e.exclude[r]=i:Iv.test(r)?(t||(e.attrs[r]=i),e.listeners[r]=i):e.attrs[r]=i,e}),{exclude:{},attrs:{},listeners:{}});i.value=e.attrs,a.value=e.listeners,o.value=e.exclude})),{$attrs:i,$listeners:a,$excludeAttrs:o}};function Av(){dr((()=>{Cv||(Cv=plus.webview.currentWebview()),Mv||(Mv=(Cv.getStyle()||{}).pullToRefresh||{})}))}function Bv(e){var{disable:t}=e;Mv&&Mv.support&&Cv.setPullToRefresh(Object.assign({},Mv,{support:!t}))}function Nv(e){var t=[];return gn(e)&&e.forEach((e=>{Ds(e)?e.type===As?t.push(...Nv(e.children)):t.push(e):gn(e)&&t.push(...Nv(e))})),t}function Rv(e){tl().rebuild=e}const Pv=Vf({inheritAttrs:!1,name:"MovableArea",props:{scaleArea:{type:Boolean,default:!1}},setup(e,t){var{slots:n}=t,r=Aa(null),i=Aa(!1),{setContexts:a,events:o}=function(e,t){var n=Aa(0),r=Aa(0),i=ma({x:null,y:null}),a=Aa(null),o=null,s=[];function l(t){t&&1!==t&&(e.scaleArea?s.forEach((function(e){e._setScale(t)})):o&&o._setScale(t))}function u(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s,r=t.value;function i(e){for(var t=0;t<n.length;t++){var a=n[t];if(e===a.rootRef.value)return a}return e===r||e===document.body||e===document?null:i(e.parentNode)}return i(e)}var c=qf((t=>{Bv({disable:!0});var n=t.touches;if(n&&n.length>1){var r={x:n[1].pageX-n[0].pageX,y:n[1].pageY-n[0].pageY};if(a.value=Dv(r),i.x=r.x,i.y=r.y,!e.scaleArea){var s=u(n[0].target),l=u(n[1].target);o=s&&s===l?s:null}}})),d=qf((e=>{var t=e.touches;if(t&&t.length>1){e.preventDefault();var n={x:t[1].pageX-t[0].pageX,y:t[1].pageY-t[0].pageY};if(null!==i.x&&a.value&&a.value>0)l(Dv(n)/a.value);i.x=n.x,i.y=n.y}})),h=qf((t=>{Bv({disable:!1});var n=t.touches;n&&n.length||t.changedTouches&&(i.x=0,i.y=0,a.value=null,e.scaleArea?s.forEach((function(e){e._endScale()})):o&&o._endScale())}));function f(){p(),s.forEach((function(e,t){e.setParent()}))}function p(){var e=window.getComputedStyle(t.value),i=t.value.getBoundingClientRect();n.value=i.width-["Left","Right"].reduce((function(t,n){var r="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[r])}),0),r.value=i.height-["Top","Bottom"].reduce((function(t,n){var r="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[r])}),0)}return _o("movableAreaWidth",n),_o("movableAreaHeight",r),{setContexts(e){s=e},events:{_onTouchstart:c,_onTouchmove:d,_onTouchend:h,_resize:f}}}(e,r),{$listeners:s,$attrs:l,$excludeAttrs:u}=Lv(),c=s.value;["onTouchstart","onTouchmove","onTouchend"].forEach((e=>{var t=c[e],n=o["_".concat(e)];c[e]=t?[].concat(t,n):n})),Po((()=>{o._resize(),Av(),i.value=!0}));var d=[],h=[];function f(){for(var e=[],t=function(t){var n=d[t];n instanceof Element||(n=n.el);var r=h.find((e=>n===e.rootRef.value));r&&e.push(Ea(r))},n=0;n<d.length;n++)t(n);a(e)}Rv((()=>{d=r.value.children,f()}));return _o("_isMounted",i),_o("movableAreaRootRef",r),_o("addMovableViewContext",(e=>{h.push(e),f()})),_o("removeMovableViewContext",(e=>{var t=h.indexOf(e);t>=0&&(h.splice(t,1),f())})),()=>(n.default&&n.default(),Ws("uni-movable-area",Gs({ref:r},l.value,u.value,c),[Ws(rp,{onResize:o._resize},null,8,["onResize"]),d],16))}});function Dv(e){return Math.sqrt(e.x*e.x+e.y*e.y)}var zv,Fv,$v=function(e,t,n,r){e.addEventListener(t,(e=>{bn(n)&&!1===n(e)&&((void 0===e.cancelable||e.cancelable)&&e.preventDefault(),e.stopPropagation())}),{passive:!1})};function Vv(e,t,n){Fo((()=>{document.removeEventListener("mousemove",zv),document.removeEventListener("mouseup",Fv)}));var r,i,a=0,o=0,s=0,l=0,u=function(e,n,r,i){if(!1===t({cancelable:e.cancelable,target:e.target,currentTarget:e.currentTarget,preventDefault:e.preventDefault.bind(e),stopPropagation:e.stopPropagation.bind(e),touches:e.touches,changedTouches:e.changedTouches,detail:{state:n,x:r,y:i,dx:r-a,dy:i-o,ddx:r-s,ddy:i-l,timeStamp:e.timeStamp}}))return!1},c=null;$v(e,"touchstart",(function(e){if(r=!0,1===e.touches.length&&!c)return c=e,a=s=e.touches[0].pageX,o=l=e.touches[0].pageY,u(e,"start",a,o)})),$v(e,"mousedown",(function(e){if(i=!0,!r&&!c)return c=e,a=s=e.pageX,o=l=e.pageY,u(e,"start",a,o)})),$v(e,"touchmove",(function(e){if(1===e.touches.length&&c){var t=u(e,"move",e.touches[0].pageX,e.touches[0].pageY);return s=e.touches[0].pageX,l=e.touches[0].pageY,t}}));var d=zv=function(e){if(!r&&i&&c){var t=u(e,"move",e.pageX,e.pageY);return s=e.pageX,l=e.pageY,t}};document.addEventListener("mousemove",d),$v(e,"touchend",(function(e){if(0===e.touches.length&&c)return r=!1,c=null,u(e,"end",e.changedTouches[0].pageX,e.changedTouches[0].pageY)}));var h=Fv=function(e){if(i=!1,!r&&c)return c=null,u(e,"end",e.pageX,e.pageY)};document.addEventListener("mouseup",h),$v(e,"touchcancel",(function(e){if(c){r=!1;var t=c;return c=null,u(e,n?"cancel":"end",t.touches[0].pageX,t.touches[0].pageY)}}))}function jv(e,t,n){return e>t-n&&e<t+n}function Wv(e,t){return jv(e,0,t)}function Uv(){}function Hv(e,t){this._m=e,this._f=1e3*t,this._startTime=0,this._v=0}function qv(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}function Yv(e,t,n){this._springX=new qv(e,t,n),this._springY=new qv(e,t,n),this._springScale=new qv(e,t,n),this._startTime=0}function Xv(e,t){return+((1e3*e-1e3*t)/1e3).toFixed(1)}Uv.prototype.x=function(e){return Math.sqrt(e)},Hv.prototype.setV=function(e,t){var n=Math.pow(Math.pow(e,2)+Math.pow(t,2),.5);this._x_v=e,this._y_v=t,this._x_a=-this._f*this._x_v/n,this._y_a=-this._f*this._y_v/n,this._t=Math.abs(e/this._x_a)||Math.abs(t/this._y_a),this._lastDt=null,this._startTime=(new Date).getTime()},Hv.prototype.setS=function(e,t){this._x_s=e,this._y_s=t},Hv.prototype.s=function(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t,this._lastDt=e);var t=this._x_v*e+.5*this._x_a*Math.pow(e,2)+this._x_s,n=this._y_v*e+.5*this._y_a*Math.pow(e,2)+this._y_s;return(this._x_a>0&&t<this._endPositionX||this._x_a<0&&t>this._endPositionX)&&(t=this._endPositionX),(this._y_a>0&&n<this._endPositionY||this._y_a<0&&n>this._endPositionY)&&(n=this._endPositionY),{x:t,y:n}},Hv.prototype.ds=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t),{dx:this._x_v+this._x_a*e,dy:this._y_v+this._y_a*e}},Hv.prototype.delta=function(){return{x:-1.5*Math.pow(this._x_v,2)/this._x_a||0,y:-1.5*Math.pow(this._y_v,2)/this._y_a||0}},Hv.prototype.dt=function(){return-this._x_v/this._x_a},Hv.prototype.done=function(){var e=jv(this.s().x,this._endPositionX)||jv(this.s().y,this._endPositionY)||this._lastDt===this._t;return this._lastDt=null,e},Hv.prototype.setEnd=function(e,t){this._endPositionX=e,this._endPositionY=t},Hv.prototype.reconfigure=function(e,t){this._m=e,this._f=1e3*t},qv.prototype._solve=function(e,t){var n=this._c,r=this._m,i=this._k,a=n*n-4*r*i;if(0===a){var o=-n/(2*r),s=e,l=t/(o*e);return{x:function(e){return(s+l*e)*Math.pow(Math.E,o*e)},dx:function(e){var t=Math.pow(Math.E,o*e);return o*(s+l*e)*t+l*t}}}if(a>0){var u=(-n-Math.sqrt(a))/(2*r),c=(-n+Math.sqrt(a))/(2*r),d=(t-u*e)/(c-u),h=e-d;return{x:function(e){var t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,u*e)),n||(n=this._powER2T=Math.pow(Math.E,c*e)),h*t+d*n},dx:function(e){var t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,u*e)),n||(n=this._powER2T=Math.pow(Math.E,c*e)),h*u*t+d*c*n}}}var f=Math.sqrt(4*r*i-n*n)/(2*r),p=-n/2*r,v=e,g=(t-p*e)/f;return{x:function(e){return Math.pow(Math.E,p*e)*(v*Math.cos(f*e)+g*Math.sin(f*e))},dx:function(e){var t=Math.pow(Math.E,p*e),n=Math.cos(f*e),r=Math.sin(f*e);return t*(g*f*n-v*f*r)+p*t*(g*r+v*n)}}},qv.prototype.x=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0},qv.prototype.dx=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0},qv.prototype.setEnd=function(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!Wv(t,.1)){t=t||0;var r=this._endPosition;this._solution&&(Wv(t,.1)&&(t=this._solution.dx((n-this._startTime)/1e3)),r=this._solution.x((n-this._startTime)/1e3),Wv(t,.1)&&(t=0),Wv(r,.1)&&(r=0),r+=this._endPosition),this._solution&&Wv(r-e,.1)&&Wv(t,.1)||(this._endPosition=e,this._solution=this._solve(r-this._endPosition,t),this._startTime=n)}},qv.prototype.snap=function(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}},qv.prototype.done=function(e){return e||(e=(new Date).getTime()),jv(this.x(),this._endPosition,.1)&&Wv(this.dx(),.1)},qv.prototype.reconfigure=function(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())},qv.prototype.springConstant=function(){return this._k},qv.prototype.damping=function(){return this._c},qv.prototype.configuration=function(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]},Yv.prototype.setEnd=function(e,t,n,r){var i=(new Date).getTime();this._springX.setEnd(e,r,i),this._springY.setEnd(t,r,i),this._springScale.setEnd(n,r,i),this._startTime=i},Yv.prototype.x=function(){var e=((new Date).getTime()-this._startTime)/1e3;return{x:this._springX.x(e),y:this._springY.x(e),scale:this._springScale.x(e)}},Yv.prototype.done=function(){var e=(new Date).getTime();return this._springX.done(e)&&this._springY.done(e)&&this._springScale.done(e)},Yv.prototype.reconfigure=function(e,t,n){this._springX.reconfigure(e,t,n),this._springY.reconfigure(e,t,n),this._springScale.reconfigure(e,t,n)};const Zv=Vf({name:"MovableView",props:{direction:{type:String,default:"none"},inertia:{type:[Boolean,String],default:!1},outOfBounds:{type:[Boolean,String],default:!1},x:{type:[Number,String],default:0},y:{type:[Number,String],default:0},damping:{type:[Number,String],default:20},friction:{type:[Number,String],default:2},disabled:{type:[Boolean,String],default:!1},scale:{type:[Boolean,String],default:!1},scaleMin:{type:[Number,String],default:.5},scaleMax:{type:[Number,String],default:10},scaleValue:{type:[Number,String],default:1},animation:{type:[Boolean,String],default:!0}},emits:["change","scale"],setup(e,t){var{slots:n,emit:r}=t,i=Aa(null),a=Yf(i,r),{setParent:o}=function(e,t,n){var r,i,a=yo("_isMounted",Aa(!1)),o=yo("addMovableViewContext",(()=>{})),s=yo("removeMovableViewContext",(()=>{})),l=Aa(1),u=Aa(1),c=Aa(!1),d=Aa(0),h=Aa(0),f=null,p=null,v=!1,g=null,m=null,_=new Uv,y=new Uv,b={historyX:[0,0],historyY:[0,0],historyT:[0,0]},w=dl((()=>{var t=Number(e.friction);return isNaN(t)||t<=0?2:t})),x=new Hv(1,w.value);xo((()=>e.disabled),(()=>{H()}));var{_updateOldScale:S,_endScale:k,_setScale:T,scaleValueSync:E,_updateBoundary:C,_updateOffset:M,_updateWH:O,_scaleOffset:I,minX:L,minY:A,maxX:B,maxY:N,FAandSFACancel:R,_getLimitXY:P,_setTransform:D,_revise:z,dampingNumber:F,xMove:$,yMove:V,xSync:j,ySync:W,_STD:U}=function(e,t,n,r,i,a,o,s,l,u){var c=dl((()=>{var t=Number(e.scaleMin);return isNaN(t)?.5:t})),d=dl((()=>{var t=Number(e.scaleMax);return isNaN(t)?10:t})),h=Aa(Number(e.scaleValue)||1);xo(h,(e=>{D(e)})),xo(c,(()=>{P()})),xo(d,(()=>{P()})),xo((()=>e.scaleValue),(e=>{h.value=Number(e)||0}));var{_updateBoundary:f,_updateOffset:p,_updateWH:v,_scaleOffset:g,minX:m,minY:_,maxX:y,maxY:b}=function(e,t,n){var r=yo("movableAreaWidth",Aa(0)),i=yo("movableAreaHeight",Aa(0)),a=yo("movableAreaRootRef"),o={x:0,y:0},s={x:0,y:0},l=Aa(0),u=Aa(0),c=Aa(0),d=Aa(0),h=Aa(0),f=Aa(0);function p(){var e=0-o.x+s.x,t=r.value-l.value-o.x-s.x;c.value=Math.min(e,t),h.value=Math.max(e,t);var n=0-o.y+s.y,a=i.value-u.value-o.y-s.y;d.value=Math.min(n,a),f.value=Math.max(n,a)}function v(){o.x=Jv(e.value,a.value),o.y=Qv(e.value,a.value)}function g(r){r=r||t.value,r=n(r);var i=e.value.getBoundingClientRect();u.value=i.height/t.value,l.value=i.width/t.value;var a=u.value*r,o=l.value*r;s.x=(o-l.value)/2,s.y=(a-u.value)/2}return{_updateBoundary:p,_updateOffset:v,_updateWH:g,_scaleOffset:s,minX:c,minY:d,maxX:h,maxY:f}}(t,r,R),{FAandSFACancel:w,_getLimitXY:x,_animationTo:S,_setTransform:k,_revise:T,dampingNumber:E,xMove:C,yMove:M,xSync:O,ySync:I,_STD:L}=function(e,t,n,r,i,a,o,s,l,u,c,d,h,f){var p=dl((()=>{var e=Number(t.damping);return isNaN(e)?20:e})),v=dl((()=>"all"===t.direction||"horizontal"===t.direction)),g=dl((()=>"all"===t.direction||"vertical"===t.direction)),m=Aa(tg(t.x)),_=Aa(tg(t.y));xo((()=>t.x),(e=>{m.value=tg(e)})),xo((()=>t.y),(e=>{_.value=tg(e)})),xo(m,(e=>{T(e)})),xo(_,(e=>{E(e)}));var y=new Yv(1,9*Math.pow(p.value,2)/40,p.value);function b(e,t){var n=!1;return e>i.value?(e=i.value,n=!0):e<o.value&&(e=o.value,n=!0),t>a.value?(t=a.value,n=!0):t<s.value&&(t=s.value,n=!0),{x:e,y:t,outOfBounds:n}}function w(){d&&d.cancel(),c&&c.cancel()}function x(e,n,i,a,o,s){w(),v.value||(e=l.value),g.value||(n=u.value),t.scale||(i=r.value);var d=b(e,n);e=d.x,n=d.y,t.animation?(y._springX._solution=null,y._springY._solution=null,y._springScale._solution=null,y._springX._endPosition=l.value,y._springY._endPosition=u.value,y._springScale._endPosition=r.value,y.setEnd(e,n,i,1),c=eg(y,(function(){var e=y.x();S(e.x,e.y,e.scale,a,o,s)}),(function(){c.cancel()}))):S(e,n,i,a,o,s)}function S(i,a,o){var s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",c=arguments.length>4?arguments[4]:void 0,d=arguments.length>5?arguments[5]:void 0;null!==i&&"NaN"!==i.toString()&&"number"==typeof i||(i=l.value||0),null!==a&&"NaN"!==a.toString()&&"number"==typeof a||(a=u.value||0),i=Number(i.toFixed(1)),a=Number(a.toFixed(1)),o=Number(o.toFixed(1)),l.value===i&&u.value===a||c||f("change",{},{x:Xv(i,n.x),y:Xv(a,n.y),source:s}),t.scale||(o=r.value),o=+(o=h(o)).toFixed(3),d&&o!==r.value&&f("scale",{},{x:i,y:a,scale:o});var p="translateX("+i+"px) translateY("+a+"px) translateZ(0px) scale("+o+")";e.value&&(e.value.style.transform=p,e.value.style.webkitTransform=p,l.value=i,u.value=a,r.value=o)}function k(e){var t=b(l.value,u.value),n=t.x,i=t.y,a=t.outOfBounds;return a&&x(n,i,r.value,e),a}function T(e){if(v.value){if(e+n.x===l.value)return l;c&&c.cancel(),x(e+n.x,_.value+n.y,r.value)}return e}function E(e){if(g.value){if(e+n.y===u.value)return u;c&&c.cancel(),x(m.value+n.x,e+n.y,r.value)}return e}return{FAandSFACancel:w,_getLimitXY:b,_animationTo:x,_setTransform:S,_revise:k,dampingNumber:p,xMove:v,yMove:g,xSync:m,ySync:_,_STD:y}}(t,e,g,r,y,b,m,_,o,s,l,u,R,n);function A(t,n){if(e.scale){t=R(t),v(t),f();var r=x(o.value,s.value),i=r.x,a=r.y;n?S(i,a,t,"",!0,!0):Kv((function(){k(i,a,t,"",!0,!0)}))}}function B(){a.value=!0}function N(e){i.value=e}function R(e){return e=Math.max(.5,c.value,e),e=Math.min(10,d.value,e)}function P(){if(!e.scale)return!1;A(r.value,!0),N(r.value)}function D(t){return!!e.scale&&(A(t=R(t),!0),N(t),t)}function z(){a.value=!1,N(r.value)}function F(e){e&&(e=i.value*e,B(),A(e))}return{_updateOldScale:N,_endScale:z,_setScale:F,scaleValueSync:h,_updateBoundary:f,_updateOffset:p,_updateWH:v,_scaleOffset:g,minX:m,minY:_,maxX:y,maxY:b,FAandSFACancel:w,_getLimitXY:x,_animationTo:S,_setTransform:k,_revise:T,dampingNumber:E,xMove:C,yMove:M,xSync:O,ySync:I,_STD:L}}(e,n,t,l,u,c,d,h,f,p);function H(){c.value||e.disabled||(Bv({disable:!0}),R(),b.historyX=[0,0],b.historyY=[0,0],b.historyT=[0,0],$.value&&(r=d.value),V.value&&(i=h.value),n.value.style.willChange="transform",g=null,m=null,v=!0)}function q(t){if(!c.value&&!e.disabled&&v){var n=d.value,a=h.value;if(null===m&&(m=Math.abs(t.detail.dx/t.detail.dy)>1?"htouchmove":"vtouchmove"),$.value&&(n=t.detail.dx+r,b.historyX.shift(),b.historyX.push(n),V.value||null!==g||(g=Math.abs(t.detail.dx/t.detail.dy)<1)),V.value&&(a=t.detail.dy+i,b.historyY.shift(),b.historyY.push(a),$.value||null!==g||(g=Math.abs(t.detail.dy/t.detail.dx)<1)),b.historyT.shift(),b.historyT.push(t.detail.timeStamp),!g){t.preventDefault();var o="touch";n<L.value?e.outOfBounds?(o="touch-out-of-bounds",n=L.value-_.x(L.value-n)):n=L.value:n>B.value&&(e.outOfBounds?(o="touch-out-of-bounds",n=B.value+_.x(n-B.value)):n=B.value),a<A.value?e.outOfBounds?(o="touch-out-of-bounds",a=A.value-y.x(A.value-a)):a=A.value:a>N.value&&(e.outOfBounds?(o="touch-out-of-bounds",a=N.value+y.x(a-N.value)):a=N.value),Kv((function(){D(n,a,l.value,o)}))}}}function Y(){if(!c.value&&!e.disabled&&v&&(Bv({disable:!1}),n.value.style.willChange="auto",v=!1,!g&&!z("out-of-bounds")&&e.inertia)){var t=1e3*(b.historyX[1]-b.historyX[0])/(b.historyT[1]-b.historyT[0]),r=1e3*(b.historyY[1]-b.historyY[0])/(b.historyT[1]-b.historyT[0]),i=d.value,a=h.value;x.setV(t,r),x.setS(i,a);var o=x.delta().x,s=x.delta().y,u=o+i,f=s+a;u<L.value?(u=L.value,f=a+(L.value-i)*s/o):u>B.value&&(u=B.value,f=a+(B.value-i)*s/o),f<A.value?(f=A.value,u=i+(A.value-a)*o/s):f>N.value&&(f=N.value,u=i+(N.value-a)*o/s),x.setEnd(u,f),p=eg(x,(function(){var e=x.s(),t=e.x,n=e.y;D(t,n,l.value,"friction")}),(function(){p.cancel()}))}e.outOfBounds||e.inertia||R()}function X(){if(a.value){R();var t=e.scale?E.value:1;M(),O(t),C();var n=P(j.value+I.x,W.value+I.y),r=n.x,i=n.y;D(r,i,t,"",!0),S(t)}}return Po((()=>{Vv(n.value,(e=>{switch(e.detail.state){case"start":H();break;case"move":q(e);break;case"end":Y()}})),X(),x.reconfigure(1,w.value),U.reconfigure(1,9*Math.pow(F.value,2)/40,F.value),n.value.style.transformOrigin="center",Av();var e={rootRef:n,setParent:X,_endScale:k,_setScale:T};o(e),$o((()=>{s(e)}))})),$o((()=>{R()})),{setParent:X}}(e,a,i);return()=>Ws("uni-movable-view",{ref:i},[Ws(rp,{onResize:o},null,8,["onResize"]),n.default&&n.default()],512)}});var Gv=!1;function Kv(e){Gv||(Gv=!0,requestAnimationFrame((function(){e(),Gv=!1})))}function Jv(e,t){if(e===t)return 0;var n=e.offsetLeft;return e.offsetParent?n+=Jv(e.offsetParent,t):0}function Qv(e,t){if(e===t)return 0;var n=e.offsetTop;return e.offsetParent?n+=Qv(e.offsetParent,t):0}function eg(e,t,n){var r={id:0,cancelled:!1};return function e(t,n,r,i){if(!t||!t.cancelled){r(n);var a=n.done();a||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,r,i))),a&&i&&i(n)}}(r,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,r),model:e}}function tg(e){return/\d+[ur]px$/i.test(e)?uni.upx2px(parseFloat(e)):Number(e)||0}var ng=["navigate","redirect","switchTab","reLaunch","navigateBack"],rg=["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"],ig=["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"],ag={hoverClass:{type:String,default:"navigator-hover"},url:{type:String,default:""},openType:{type:String,default:"navigate",validator:e=>Boolean(~ng.indexOf(e))},delta:{type:Number,default:1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:600},exists:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},animationType:{type:String,default:"",validator:e=>!e||rg.concat(ig).includes(e)},animationDuration:{type:[String,Number],default:300}};const og=Vf({name:"Navigator",inheritAttrs:!1,compatConfig:{MODE:3},props:hn({},ag,{renderLink:{type:Boolean,default:!0}}),setup(e,t){var{slots:n}=t,r=tl(),i=r&&r.vnode.scopeId||"",{hovering:a,binding:o}=Uf(e),s=function(e){return()=>{if("navigateBack"===e.openType||e.url){var t=parseInt(e.animationDuration);switch(e.openType){case"navigate":uni.navigateTo({url:e.url,animationType:e.animationType||"pop-in",animationDuration:t});break;case"redirect":uni.redirectTo({url:e.url,exists:e.exists});break;case"switchTab":uni.switchTab({url:e.url});break;case"reLaunch":uni.reLaunch({url:e.url});break;case"navigateBack":uni.navigateBack({delta:e.delta,animationType:e.animationType||"pop-out",animationDuration:t})}}else console.error("<navigator/> should have url attribute when using navigateTo, redirectTo, reLaunch or switchTab")}}(e);return()=>{var{hoverClass:t,url:l}=e,u=e.hoverClass&&"none"!==e.hoverClass,c=Ws("uni-navigator",Gs({class:u&&a.value?t:""},u&&o,r?r.attrs:{},{[i]:""},{onClick:s}),[n.default&&n.default()],16,["onClick"]);return e.renderLink?Ws("a",{class:"navigator-wrap",href:l,onClick:vu,onMousedown:vu},[c],40,["href","onClick","onMousedown"]):c}}});const sg=Vf({name:"PickerView",props:{value:{type:Array,default:()=>[],validator:function(e){return gn(e)&&e.filter((e=>"number"==typeof e)).length===e.length}},indicatorStyle:{type:String,default:""},indicatorClass:{type:String,default:""},maskStyle:{type:String,default:""},maskClass:{type:String,default:""}},emits:["change","pickstart","pickend","update:value"],setup(e,t){var{slots:n,emit:r}=t,i=Aa(null),a=Aa(null),o=Yf(i,r),s=function(e){var t=ma([...e.value]),n=ma({value:t,height:34});return xo((()=>e.value),((e,t)=>{(e===t||e.length!==t.length||e.findIndex(((e,n)=>e!==t[n]))>=0)&&(n.value.length=e.length,e.forEach(((e,t)=>{e!==n.value[t]&&n.value.splice(t,1,e)})))})),n}(e),l=Aa(null),u=Aa([]),c=Aa([]);function d(e){var t=c.value;if(t instanceof HTMLCollection)return Array.prototype.indexOf.call(t,e.el);var n=(t=t.filter((e=>e.type!==Ns))).indexOf(e);return-1!==n?n:u.value.indexOf(e)}return _o("getPickerViewColumn",(function(e){return dl({get(){var t=d(e.vnode);return s.value[t]||0},set(t){var n=d(e.vnode);if(!(n<0)&&s.value[n]!==t){s.value[n]=t;var i=s.value.map((e=>e));r("update:value",i),o("change",{},{value:i})}}})})),_o("pickerViewProps",e),_o("pickerViewState",s),Rv((()=>{var e;(e=l.value)&&(s.height=e.$el.offsetHeight),a.value&&(c.value=a.value.children)})),()=>{var e=n.default&&n.default();return Ws("uni-picker-view",{ref:i},[Ws(rp,{ref:l,onResize:e=>{var{height:t}=e;return s.height=t}},null,8,["onResize"]),Ws("div",{ref:a,class:"uni-picker-view-wrapper"},[e],512)],512)}}});class lg{constructor(e){this._drag=e,this._dragLog=Math.log(e),this._x=0,this._v=0,this._startTime=0}set(e,t){this._x=e,this._v=t,this._startTime=(new Date).getTime()}setVelocityByEnd(e){this._v=(e-this._x)*this._dragLog/(Math.pow(this._drag,100)-1)}x(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);var t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._x+this._v*t/this._dragLog-this._v/this._dragLog}dx(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);var t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._v*t}done(){return Math.abs(this.dx())<3}reconfigure(e){var t=this.x(),n=this.dx();this._drag=e,this._dragLog=Math.log(e),this.set(t,n)}configuration(){var e=this;return[{label:"Friction",read:function(){return e._drag},write:function(t){e.reconfigure(t)},min:.001,max:.1,step:.001}]}}function ug(e,t,n){return e>t-n&&e<t+n}function cg(e,t){return ug(e,0,t)}class dg{constructor(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}_solve(e,t){var n=this._c,r=this._m,i=this._k,a=n*n-4*r*i;if(0===a){var o=-n/(2*r),s=e,l=t/(o*e);return{x:function(e){return(s+l*e)*Math.pow(Math.E,o*e)},dx:function(e){var t=Math.pow(Math.E,o*e);return o*(s+l*e)*t+l*t}}}if(a>0){var u=(-n-Math.sqrt(a))/(2*r),c=(-n+Math.sqrt(a))/(2*r),d=(t-u*e)/(c-u),h=e-d;return{x:function(e){var t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,u*e)),n||(n=this._powER2T=Math.pow(Math.E,c*e)),h*t+d*n},dx:function(e){var t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,u*e)),n||(n=this._powER2T=Math.pow(Math.E,c*e)),h*u*t+d*c*n}}}var f=Math.sqrt(4*r*i-n*n)/(2*r),p=-n/2*r,v=e,g=(t-p*e)/f;return{x:function(e){return Math.pow(Math.E,p*e)*(v*Math.cos(f*e)+g*Math.sin(f*e))},dx:function(e){var t=Math.pow(Math.E,p*e),n=Math.cos(f*e),r=Math.sin(f*e);return t*(g*f*n-v*f*r)+p*t*(g*r+v*n)}}}x(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0}dx(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0}setEnd(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!cg(t,.4)){t=t||0;var r=this._endPosition;this._solution&&(cg(t,.4)&&(t=this._solution.dx((n-this._startTime)/1e3)),r=this._solution.x((n-this._startTime)/1e3),cg(t,.4)&&(t=0),cg(r,.4)&&(r=0),r+=this._endPosition),this._solution&&cg(r-e,.4)&&cg(t,.4)||(this._endPosition=e,this._solution=this._solve(r-this._endPosition,t),this._startTime=n)}}snap(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}}done(e){return ug(this.x(),this._endPosition,.4)&&cg(this.dx(),.4)}reconfigure(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())}springConstant(){return this._k}damping(){return this._c}configuration(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]}}class hg{constructor(e,t,n){this._extent=e,this._friction=t||new lg(.01),this._spring=n||new dg(1,90,20),this._startTime=0,this._springing=!1,this._springOffset=0}snap(e,t){this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(t)}set(e,t){this._friction.set(e,t),e>0&&t>=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(0)):e<-this._extent&&t<=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(-this._extent)):this._springing=!1,this._startTime=(new Date).getTime()}x(e){if(!this._startTime)return 0;if(e||(e=((new Date).getTime()-this._startTime)/1e3),this._springing)return this._spring.x()+this._springOffset;var t=this._friction.x(e),n=this.dx(e);return(t>0&&n>=0||t<-this._extent&&n<=0)&&(this._springing=!0,this._spring.setEnd(0,n),t<-this._extent?this._springOffset=-this._extent:this._springOffset=0,t=this._spring.x()+this._springOffset),t}dx(e){var t;return t=this._lastTime===e?this._lastDx:this._springing?this._spring.dx(e):this._friction.dx(e),this._lastTime=e,this._lastDx=t,t}done(){return this._springing?this._spring.done():this._friction.done()}setVelocityByEnd(e){this._friction.setVelocityByEnd(e)}configuration(){var e=this._friction.configuration();return e.push.apply(e,this._spring.configuration()),e}}class fg{constructor(e,t){t=t||{},this._element=e,this._options=t,this._enableSnap=t.enableSnap||!1,this._itemSize=t.itemSize||0,this._enableX=t.enableX||!1,this._enableY=t.enableY||!1,this._shouldDispatchScrollEvent=!!t.onScroll,this._enableX?(this._extent=(t.scrollWidth||this._element.offsetWidth)-this._element.parentElement.offsetWidth,this._scrollWidth=t.scrollWidth):(this._extent=(t.scrollHeight||this._element.offsetHeight)-this._element.parentElement.offsetHeight,this._scrollHeight=t.scrollHeight),this._position=0,this._scroll=new hg(this._extent,t.friction,t.spring),this._onTransitionEnd=this.onTransitionEnd.bind(this),this.updatePosition()}onTouchStart(){this._startPosition=this._position,this._lastChangePos=this._startPosition,this._startPosition>0?this._startPosition/=.5:this._startPosition<-this._extent&&(this._startPosition=(this._startPosition+this._extent)/.5-this._extent),this._animation&&(this._animation.cancel(),this._scrolling=!1),this.updatePosition()}onTouchMove(e,t){var n=this._startPosition;this._enableX?n+=e:this._enableY&&(n+=t),n>0?n*=.5:n<-this._extent&&(n=.5*(n+this._extent)-this._extent),this._position=n,this.updatePosition(),this.dispatchScroll()}onTouchEnd(e,t,n){if(this._enableSnap&&this._position>-this._extent&&this._position<0){if(this._enableY&&(Math.abs(t)<this._itemSize&&Math.abs(n.y)<300||Math.abs(n.y)<150))return void this.snap();if(this._enableX&&(Math.abs(e)<this._itemSize&&Math.abs(n.x)<300||Math.abs(n.x)<150))return void this.snap()}var r,i,a;if(this._enableX?this._scroll.set(this._position,n.x):this._enableY&&this._scroll.set(this._position,n.y),this._enableSnap){var o=this._scroll._friction.x(100),s=o%this._itemSize;(r=Math.abs(s)>this._itemSize/2?o-(this._itemSize-Math.abs(s)):o-s)<=0&&r>=-this._extent&&this._scroll.setVelocityByEnd(r)}this._lastTime=Date.now(),this._lastDelay=0,this._scrolling=!0,this._lastChangePos=this._position,this._lastIdx=Math.floor(Math.abs(this._position/this._itemSize)),this._animation=(i=this._scroll,function e(t,n,r,i){if(!t||!t.cancelled){r(n);var a=n.done();a||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,r,i))),a&&i&&i(n)}}(a={id:0,cancelled:!1},i,(()=>{var e=Date.now(),t=(e-this._scroll._startTime)/1e3,n=this._scroll.x(t);this._position=n,this.updatePosition();var r=this._scroll.dx(t);this._shouldDispatchScrollEvent&&e-this._lastTime>this._lastDelay&&(this.dispatchScroll(),this._lastDelay=Math.abs(2e3/r),this._lastTime=e)}),(()=>{this._enableSnap&&(r<=0&&r>=-this._extent&&(this._position=r,this.updatePosition()),bn(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._shouldDispatchScrollEvent&&this.dispatchScroll(),this._scrolling=!1})),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,a),model:i})}onTransitionEnd(){this._element.style.webkitTransition="",this._element.style.transition="",this._element.removeEventListener("transitionend",this._onTransitionEnd),this._snapping&&(this._snapping=!1),this.dispatchScroll()}snap(){var e=this._itemSize,t=this._position%e,n=Math.abs(t)>this._itemSize/2?this._position-(e-Math.abs(t)):this._position-t;this._position!==n&&(this._snapping=!0,this.scrollTo(-n),bn(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize)))}scrollTo(e,t){this._animation&&(this._animation.cancel(),this._scrolling=!1),"number"==typeof e&&(this._position=-e),this._position<-this._extent?this._position=-this._extent:this._position>0&&(this._position=0);var n="transform "+(t||.2)+"s ease-out";this._element.style.webkitTransition="-webkit-"+n,this._element.style.transition=n,this.updatePosition(),this._element.addEventListener("transitionend",this._onTransitionEnd)}dispatchScroll(){if(bn(this._options.onScroll)&&Math.round(Number(this._lastPos))!==Math.round(this._position)){this._lastPos=this._position;var e={target:{scrollLeft:this._enableX?-this._position:0,scrollTop:this._enableY?-this._position:0,scrollHeight:this._scrollHeight||this._element.offsetHeight,scrollWidth:this._scrollWidth||this._element.offsetWidth,offsetHeight:this._element.parentElement.offsetHeight,offsetWidth:this._element.parentElement.offsetWidth}};this._options.onScroll(e)}}update(e,t,n){var r=0,i=this._position;this._enableX?(r=this._element.childNodes.length?(t||this._element.offsetWidth)-this._element.parentElement.offsetWidth:0,this._scrollWidth=t):(r=this._element.childNodes.length?(t||this._element.offsetHeight)-this._element.parentElement.offsetHeight:0,this._scrollHeight=t),"number"==typeof e&&(this._position=-e),this._position<-r?this._position=-r:this._position>0&&(this._position=0),this._itemSize=n||this._itemSize,this.updatePosition(),i!==this._position&&(this.dispatchScroll(),bn(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._extent=r,this._scroll._extent=r}updatePosition(){var e="";this._enableX?e="translateX("+this._position+"px) translateZ(0)":this._enableY&&(e="translateY("+this._position+"px) translateZ(0)"),this._element.style.webkitTransform=e,this._element.style.transform=e}isScrolling(){return this._scrolling||this._snapping}}var pg=0;const vg=Vf({name:"PickerViewColumn",setup(e,t){var n,r,{slots:i,emit:a}=t,o=Aa(null),s=Aa(null),l=yo("getPickerViewColumn"),u=tl(),c=l?l(u):Aa(0),d=yo("pickerViewProps"),h=yo("pickerViewState"),f=Aa(34),p=Aa(null),v=dl((()=>(h.height-f.value)/2)),{state:g}=vv(),m=function(e){var t="uni-picker-view-content-".concat(pg++);return xo((()=>e.value),(function(){var n=document.createElement("style");n.innerText=".uni-picker-view-content.".concat(t,">*{height: ").concat(e.value,"px;overflow: hidden;}"),document.head.appendChild(n)})),t}(f),_=ma({current:c.value,length:0});function y(){n&&!r&&(r=!0,Ja((()=>{r=!1;var e=Math.min(_.current,_.length-1);e=Math.max(e,0),n.update(e*f.value,void 0,f.value)})))}xo((()=>c.value),(e=>{e!==_.current&&(_.current=e,y())})),xo((()=>_.current),(e=>c.value=e)),xo([()=>f.value,()=>_.length,()=>h.height],y);var b=0;function w(e){var t=b+e.deltaY;if(Math.abs(t)>10){b=0;var r=Math.min(_.current+(t<0?-1:1),_.length-1);_.current=r=Math.max(r,0),n.scrollTo(r*f.value)}else b=t;e.preventDefault()}function x(e){var{clientY:t}=e,r=o.value;if(!n.isScrolling()){var i=t-r.getBoundingClientRect().top-h.height/2,a=f.value/2;if(!(Math.abs(i)<=a)){var s=Math.ceil((Math.abs(i)-a)/f.value),l=i<0?-s:s,u=Math.min(_.current+l,_.length-1);_.current=u=Math.max(u,0),n.scrollTo(u*f.value)}}}var S=()=>{var e,t,r,i=o.value,a=s.value,{scroller:l,handleTouchStart:u,handleTouchMove:c,handleTouchEnd:d}=function(e,t){var n={trackingID:-1,maxDy:0,maxDx:0},r=new fg(e,t);function i(e){var t=e,r=e;return"move"===t.detail.state||"end"===t.detail.state?{x:t.detail.dx,y:t.detail.dy}:{x:r.screenX-n.x,y:r.screenY-n.y}}return{scroller:r,handleTouchStart:function(e){var t=e,i=e;"start"===t.detail.state?(n.trackingID="touch",n.x=t.detail.x,n.y=t.detail.y):(n.trackingID="mouse",n.x=i.screenX,n.y=i.screenY),n.maxDx=0,n.maxDy=0,n.historyX=[0],n.historyY=[0],n.historyTime=[t.detail.timeStamp||i.timeStamp],n.listener=r,r.onTouchStart&&r.onTouchStart(),("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault()},handleTouchMove:function(e){var t=e,r=e;if(-1!==n.trackingID){("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault();var a=i(e);if(a){for(n.maxDy=Math.max(n.maxDy,Math.abs(a.y)),n.maxDx=Math.max(n.maxDx,Math.abs(a.x)),n.historyX.push(a.x),n.historyY.push(a.y),n.historyTime.push(t.detail.timeStamp||r.timeStamp);n.historyTime.length>10;)n.historyTime.shift(),n.historyX.shift(),n.historyY.shift();n.listener&&n.listener.onTouchMove&&n.listener.onTouchMove(a.x,a.y)}}},handleTouchEnd:function(e){if(-1!==n.trackingID){e.preventDefault();var t=i(e);if(t){var r=n.listener;n.trackingID=-1,n.listener=null;var a={x:0,y:0};if(n.historyTime.length>2)for(var o=n.historyTime.length-1,s=n.historyTime[o],l=n.historyX[o],u=n.historyY[o];o>0;){o--;var c=s-n.historyTime[o];if(c>30&&c<50){a.x=(l-n.historyX[o])/(c/1e3),a.y=(u-n.historyY[o])/(c/1e3);break}}n.historyTime=[],n.historyX=[],n.historyY=[],r&&r.onTouchEnd&&r.onTouchEnd(t.x,t.y,a)}}}}}(a,{enableY:!0,enableX:!1,enableSnap:!0,itemSize:f.value,friction:new lg(1e-4),spring:new dg(2,90,20),onSnap:e=>{isNaN(e)||e===_.current||(_.current=e)}});n=l,Vv(i,(e=>{switch(e.detail.state){case"start":u(e),Bv({disable:!0});break;case"move":c(e),e.stopPropagation();break;case"end":case"cancel":d(e),Bv({disable:!1})}}),!0),t=0,r=0,(e=i).addEventListener("touchstart",(e=>{var n=e.changedTouches[0];t=n.clientX,r=n.clientY})),e.addEventListener("touchend",(e=>{var n=e.changedTouches[0];if(Math.abs(n.clientX-t)<20&&Math.abs(n.clientY-r)<20){var i={bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget},a=new CustomEvent("click",i);["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((e=>{a[e]=n[e]})),e.target.dispatchEvent(a)}})),Av(),y()},k=!1;return Rv((()=>{var e;s.value&&(_.length=s.value.children.length),k||(k=!0,e=p.value,f.value=e.$el.offsetHeight,S())})),()=>{var e=i.default&&i.default(),t="".concat(v.value,"px 0");return Ws("uni-picker-view-column",{ref:o},[Ws("div",{onWheel:w,onClick:x,class:"uni-picker-view-group"},[Ws("div",Gs(g.attrs,{class:["uni-picker-view-mask",d.maskClass],style:"background-size: 100% ".concat(v.value,"px;").concat(d.maskStyle)}),null,16),Ws("div",Gs(g.attrs,{class:["uni-picker-view-indicator",d.indicatorClass],style:d.indicatorStyle}),[Ws(rp,{ref:p,onResize:e=>{var{height:t}=e;return f.value=t}},null,8,["onResize"])],16),Ws("div",{ref:s,class:["uni-picker-view-content",m],style:{padding:t}},[e],6)],40,["onWheel","onClick"])],512)}}});var gg=Xn,mg="backwards";const _g=Vf({name:"Progress",props:{percent:{type:[Number,String],default:0,validator:e=>!isNaN(parseFloat(e))},fontSize:{type:[String,Number],default:16},showInfo:{type:[Boolean,String],default:!1},strokeWidth:{type:[Number,String],default:6,validator:e=>!isNaN(parseFloat(e))},color:{type:String,default:gg},activeColor:{type:String,default:gg},backgroundColor:{type:String,default:"#EBEBEB"},active:{type:[Boolean,String],default:!1},activeMode:{type:String,default:mg},duration:{type:[Number,String],default:30,validator:e=>!isNaN(parseFloat(e))},borderRadius:{type:[Number,String],default:0}},setup(e){var t=function(e){var t=Aa(0),n=dl((()=>"background-color: ".concat(e.backgroundColor,"; height: ").concat(e.strokeWidth,"px;"))),r=dl((()=>{var n=e.color!==gg&&e.activeColor===gg?e.color:e.activeColor;return"width: ".concat(t.value,"%;background-color: ").concat(n)})),i=dl((()=>{var t=parseFloat(e.percent);return t<0&&(t=0),t>100&&(t=100),t}));return ma({outerBarStyle:n,innerBarStyle:r,realPercent:i,currentPercent:t,strokeTimer:0,lastPercent:0})}(e);return yg(t,e),xo((()=>t.realPercent),((n,r)=>{t.strokeTimer&&clearInterval(t.strokeTimer),t.lastPercent=r||0,yg(t,e)})),()=>{var{showInfo:n}=e,{outerBarStyle:r,innerBarStyle:i,currentPercent:a}=t;return Ws("uni-progress",{class:"uni-progress"},[Ws("div",{style:r,class:"uni-progress-bar"},[Ws("div",{style:i,class:"uni-progress-inner-bar"},null,4)],4),n?Ws("p",{class:"uni-progress-info"},[a+"%"]):""])}}});function yg(e,t){t.active?(e.currentPercent=t.activeMode===mg?0:e.lastPercent,e.strokeTimer=setInterval((()=>{e.currentPercent+1>e.realPercent?(e.currentPercent=e.realPercent,e.strokeTimer&&clearInterval(e.strokeTimer)):e.currentPercent+=1}),parseFloat(t.duration))):e.currentPercent=e.realPercent}var bg=_u("ucg");const wg=Vf({name:"RadioGroup",props:{name:{type:String,default:""}},setup(e,t){var{emit:n,slots:r}=t,i=Aa(null);return function(e,t){var n=[];Po((()=>{s(n.length-1)}));var r=()=>{var e;return null===(e=n.find((e=>e.value.radioChecked)))||void 0===e?void 0:e.value.value};_o(bg,{addField(e){n.push(e)},removeField(e){n.splice(n.indexOf(e),1)},radioChange(e,i){s(n.indexOf(i),!0),t("change",e,{value:r()})}});var i=yo(Xf,!1),a={submit:()=>{var t=["",null];return""!==e.name&&(t[0]=e.name,t[1]=r()),t}};i&&(i.addField(a),Fo((()=>{i.removeField(a)})));function o(e,t){e.value={radioChecked:t,value:e.value.value}}function s(e,t){n.forEach(((r,i)=>{i!==e&&(t?o(n[i],!1):n.forEach(((e,t)=>{i>=t||n[t].value.radioChecked&&o(n[i],!1)})))}))}}(e,Yf(i,n)),()=>Ws("uni-radio-group",{ref:i},[r.default&&r.default()],512)}});const xg=Vf({name:"Radio",props:{checked:{type:[Boolean,String],default:!1},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},value:{type:String,default:""},color:{type:String,default:"#007aff"},backgroundColor:{type:String,default:""},borderColor:{type:String,default:""},activeBackgroundColor:{type:String,default:""},activeBorderColor:{type:String,default:""},iconColor:{type:String,default:"#ffffff"}},setup(e,t){var{slots:n}=t,r=Aa(e.checked),i=Aa(e.value),a=dl((()=>{if(e.disabled)return{backgroundColor:"#E1E1E1",borderColor:"#D1D1D1"};var t={};return r.value?(t.backgroundColor=e.activeBackgroundColor||e.color,t.borderColor=e.activeBorderColor||t.backgroundColor):(e.borderColor&&(t.borderColor=e.borderColor),e.backgroundColor&&(t.backgroundColor=e.backgroundColor)),t}));xo([()=>e.checked,()=>e.value],(e=>{var[t,n]=e;r.value=t,i.value=n}));var{uniCheckGroup:o,uniLabel:s,field:l}=function(e,t,n){var r=dl({get:()=>({radioChecked:Boolean(e.value),value:t.value}),set:t=>{var{radioChecked:n}=t;e.value=n}}),i={reset:n},a=yo(bg,!1);a&&a.addField(r);var o=yo(Xf,!1);o&&o.addField(i);var s=yo(Kf,!1);return Fo((()=>{a&&a.removeField(r),o&&o.removeField(i)})),{uniCheckGroup:a,uniForm:o,uniLabel:s,field:r}}(r,i,(()=>{r.value=!1})),u=t=>{e.disabled||r.value||(r.value=!0,o&&o.radioChange(t,l),t.stopPropagation())};return s&&(s.addHandler(u),Fo((()=>{s.removeHandler(u)}))),Qf(e,{"label-click":u}),()=>{var t=Hf(e,"disabled");return Ws("uni-radio",Gs(t,{onClick:u}),[Ws("div",{class:"uni-radio-wrapper",style:{"--HOVER-BD-COLOR":r.value?a.value.borderColor:e.activeBorderColor}},[Ws("div",{class:["uni-radio-input",{"uni-radio-input-disabled":e.disabled}],style:a.value},[r.value?ku(Su,e.disabled?"#ADADAD":e.iconColor,18):""],6),n.default&&n.default()],4)],16,["onClick"])}}});var Sg={a:"",abbr:"",address:"",article:"",aside:"",b:"",bdi:"",bdo:["dir"],big:"",blockquote:"",br:"",caption:"",center:"",cite:"",code:"",col:["span","width"],colgroup:["span","width"],dd:"",del:"",div:"",dl:"",dt:"",em:"",fieldset:"",font:"",footer:"",h1:"",h2:"",h3:"",h4:"",h5:"",h6:"",header:"",hr:"",i:"",img:["alt","src","height","width"],ins:"",label:"",legend:"",li:"",mark:"",nav:"",ol:["start","type"],p:"",pre:"",q:"",rt:"",ruby:"",s:"",section:"",small:"",span:"",strong:"",sub:"",sup:"",table:["width"],tbody:"",td:["colspan","height","rowspan","width"],tfoot:"",th:["colspan","height","rowspan","width"],thead:"",tr:["colspan","height","rowspan","width"],tt:"",u:"",ul:""},kg={amp:"&",gt:">",lt:"<",nbsp:" ",quot:'"',apos:"'",ldquo:"“",rdquo:"”",yen:"￥",radic:"√",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",hellip:"…"};var Tg=(e,t,n)=>!n||gn(n)&&!n.length?[]:n.map((n=>{if(Cn(n)){if(!vn(n,"type")||"node"===n.type){var r={[e]:""},i=n.name.toLowerCase();if(!vn(Sg,i))return;return function(e,t){if(Cn(t))for(var n in t)if(vn(t,n)){var r=t[n];"img"===e&&"src"===n&&(t[n]=ac(r))}}(i,n.attrs),r=hn(r,function(e,t){if(["a","img"].includes(e.name)&&t)return{onClick:n=>{t(n,{node:e}),n.stopPropagation(),n.preventDefault(),n.returnValue=!1}}}(n,t),n.attrs),hl(n.name,r,Tg(e,t,n.children))}return"text"===n.type&&wn(n.text)&&""!==n.text?qs((n.text||"").replace(/&(([a-zA-Z]+)|(#x{0,1}[\da-zA-Z]+));/gi,(function(e,t){return vn(kg,t)&&kg[t]?kg[t]:/^#[0-9]{1,4}$/.test(t)?String.fromCharCode(t.slice(1)):/^#x[0-9a-f]{1,4}$/i.test(t)?String.fromCharCode(0+t.slice(1)):e}))):void 0}}));function Eg(e){e=function(e){return e.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(e);var t=[],n={node:"root",children:[]};return Dp(e,{start:function(e,r,i){var a={name:e};if(0!==r.length&&(a.attrs=function(e){return e.reduce((function(e,t){var n=t.value,r=t.name;return n.match(/ /)&&-1===["style","src"].indexOf(r)&&(n=n.split(" ")),e[r]?Array.isArray(e[r])?e[r].push(n):e[r]=[e[r],n]:e[r]=n,e}),{})}(r)),i){var o=t[0]||n;o.children||(o.children=[]),o.children.push(a)}else t.unshift(a)},end:function(e){var r=t.shift();if(r.name!==e&&console.error("invalid state: mismatch end tag"),0===t.length)n.children.push(r);else{var i=t[0];i.children||(i.children=[]),i.children.push(r)}},chars:function(e){var r={type:"text",text:e};if(0===t.length)n.children.push(r);else{var i=t[0];i.children||(i.children=[]),i.children.push(r)}},comment:function(e){var n={node:"comment",text:e},r=t[0];r.children||(r.children=[]),r.children.push(n)}}),n.children}const Cg=Vf({name:"RichText",compatConfig:{MODE:3},props:{nodes:{type:[Array,String],default:function(){return[]}}},emits:["click","touchstart","touchmove","touchcancel","touchend","longpress","itemclick"],setup(e,t){var{emit:n}=t,r=tl(),i=r&&r.vnode.scopeId||"",a=Aa(null),o=Aa([]),s=Yf(a,n);function l(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};s("itemclick",e,t)}return xo((()=>e.nodes),(function(){var t=e.nodes;wn(t)&&(t=Eg(e.nodes)),o.value=Tg(i,l,t)}),{immediate:!0}),()=>hl("uni-rich-text",{ref:a},hl("div",{},o.value))}});var Mg=ur(!0);const Og=Vf({name:"ScrollView",compatConfig:{MODE:3},props:{scrollX:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},upperThreshold:{type:[Number,String],default:50},lowerThreshold:{type:[Number,String],default:50},scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},scrollIntoView:{type:String,default:""},scrollWithAnimation:{type:[Boolean,String],default:!1},enableBackToTop:{type:[Boolean,String],default:!1},refresherEnabled:{type:[Boolean,String],default:!1},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"back"},refresherBackground:{type:String,default:"#fff"},refresherTriggered:{type:[Boolean,String],default:!1}},emits:["scroll","scrolltoupper","scrolltolower","refresherrefresh","refresherrestore","refresherpulling","refresherabort","update:refresherTriggered"],setup(e,t){var{emit:n,slots:r}=t,i=Aa(null),a=Aa(null),o=Aa(null),s=Aa(null),l=Aa(null),u=Yf(i,n),{state:c,scrollTopNumber:d,scrollLeftNumber:h}=function(e){var t=dl((()=>Number(e.scrollTop)||0)),n=dl((()=>Number(e.scrollLeft)||0));return{state:ma({lastScrollTop:t.value,lastScrollLeft:n.value,lastScrollToUpperTime:0,lastScrollToLowerTime:0,refresherHeight:0,refreshRotate:0,refreshState:""}),scrollTopNumber:t,scrollLeftNumber:n}}(e);!function(e,t,n,r,i,a,o,s,l){var u=!1,c=0,d=!1,h=()=>{},f=dl((()=>{var t=Number(e.upperThreshold);return isNaN(t)?50:t})),p=dl((()=>{var t=Number(e.lowerThreshold);return isNaN(t)?50:t}));function v(e,t){var n=o.value,r=0,i="";if(e<0?e=0:"x"===t&&e>n.scrollWidth-n.offsetWidth?e=n.scrollWidth-n.offsetWidth:"y"===t&&e>n.scrollHeight-n.offsetHeight&&(e=n.scrollHeight-n.offsetHeight),"x"===t?r=n.scrollLeft-e:"y"===t&&(r=n.scrollTop-e),0!==r){var a=s.value;a.style.transition="transform .3s ease-out",a.style.webkitTransition="-webkit-transform .3s ease-out","x"===t?i="translateX("+r+"px) translateZ(0)":"y"===t&&(i="translateY("+r+"px) translateZ(0)"),a.removeEventListener("transitionend",h),a.removeEventListener("webkitTransitionEnd",h),h=()=>b(e,t),a.addEventListener("transitionend",h),a.addEventListener("webkitTransitionEnd",h),"x"===t?n.style.overflowX="hidden":"y"===t&&(n.style.overflowY="hidden"),a.style.transform=i,a.style.webkitTransform=i}}function g(n){var r=n.target;i("scroll",n,{scrollLeft:r.scrollLeft,scrollTop:r.scrollTop,scrollHeight:r.scrollHeight,scrollWidth:r.scrollWidth,deltaX:t.lastScrollLeft-r.scrollLeft,deltaY:t.lastScrollTop-r.scrollTop}),e.scrollY&&(r.scrollTop<=f.value&&t.lastScrollTop-r.scrollTop>0&&n.timeStamp-t.lastScrollToUpperTime>200&&(i("scrolltoupper",n,{direction:"top"}),t.lastScrollToUpperTime=n.timeStamp),r.scrollTop+r.offsetHeight+p.value>=r.scrollHeight&&t.lastScrollTop-r.scrollTop<0&&n.timeStamp-t.lastScrollToLowerTime>200&&(i("scrolltolower",n,{direction:"bottom"}),t.lastScrollToLowerTime=n.timeStamp)),e.scrollX&&(r.scrollLeft<=f.value&&t.lastScrollLeft-r.scrollLeft>0&&n.timeStamp-t.lastScrollToUpperTime>200&&(i("scrolltoupper",n,{direction:"left"}),t.lastScrollToUpperTime=n.timeStamp),r.scrollLeft+r.offsetWidth+p.value>=r.scrollWidth&&t.lastScrollLeft-r.scrollLeft<0&&n.timeStamp-t.lastScrollToLowerTime>200&&(i("scrolltolower",n,{direction:"right"}),t.lastScrollToLowerTime=n.timeStamp)),t.lastScrollTop=r.scrollTop,t.lastScrollLeft=r.scrollLeft}function m(t){e.scrollY&&(e.scrollWithAnimation?v(t,"y"):o.value.scrollTop=t)}function _(t){e.scrollX&&(e.scrollWithAnimation?v(t,"x"):o.value.scrollLeft=t)}function y(t){if(t){if(!/^[_a-zA-Z][-_a-zA-Z0-9:]*$/.test(t))return void console.error("id error: scroll-into-view=".concat(t));var n=a.value.querySelector("#"+t);if(n){var r=o.value.getBoundingClientRect(),i=n.getBoundingClientRect();if(e.scrollX){var s=i.left-r.left,l=o.value.scrollLeft+s;e.scrollWithAnimation?v(l,"x"):o.value.scrollLeft=l}if(e.scrollY){var u=i.top-r.top,c=o.value.scrollTop+u;e.scrollWithAnimation?v(c,"y"):o.value.scrollTop=c}}}}function b(t,n){s.value.style.transition="",s.value.style.webkitTransition="",s.value.style.transform="",s.value.style.webkitTransform="";var r=o.value;"x"===n?(r.style.overflowX=e.scrollX?"auto":"hidden",r.scrollLeft=t):"y"===n&&(r.style.overflowY=e.scrollY?"auto":"hidden",r.scrollTop=t),s.value.removeEventListener("transitionend",h),s.value.removeEventListener("webkitTransitionEnd",h)}function w(n){if(e.refresherEnabled){switch(n){case"refreshing":t.refresherHeight=e.refresherThreshold,u||(u=!0,i("refresherrefresh",{},{}),l("update:refresherTriggered",!0));break;case"restore":case"refresherabort":u=!1,t.refresherHeight=c=0,"restore"===n&&(d=!1,i("refresherrestore",{},{})),"refresherabort"===n&&d&&(d=!1,i("refresherabort",{},{}))}t.refreshState=n}}Po((()=>{Ja((()=>{m(n.value),_(r.value)})),y(e.scrollIntoView);var a=function(e){e.preventDefault(),e.stopPropagation(),g(e)},s={x:0,y:0},l=null,h=function(n){if(null!==s){var r=n.touches[0].pageX,a=n.touches[0].pageY,h=o.value;if(Math.abs(r-s.x)>Math.abs(a-s.y))if(e.scrollX){if(0===h.scrollLeft&&r>s.x)return void(l=!1);if(h.scrollWidth===h.offsetWidth+h.scrollLeft&&r<s.x)return void(l=!1);l=!0}else l=!1;else if(e.scrollY)if(0===h.scrollTop&&a>s.y)l=!1,e.refresherEnabled&&!1!==n.cancelable&&n.preventDefault();else{if(h.scrollHeight===h.offsetHeight+h.scrollTop&&a<s.y)return void(l=!1);l=!0}else l=!1;if(l&&n.stopPropagation(),0===h.scrollTop&&1===n.touches.length&&w("pulling"),e.refresherEnabled&&"pulling"===t.refreshState){var f=a-s.y;0===c&&(c=a),u?(t.refresherHeight=f+e.refresherThreshold,d=!1):(t.refresherHeight=a-c,t.refresherHeight>0&&(d=!0,i("refresherpulling",n,{deltaY:f})));var p=t.refresherHeight/e.refresherThreshold;t.refreshRotate=360*(p>1?1:p)}}},f=function(e){1===e.touches.length&&(Bv({disable:!0}),s={x:e.touches[0].pageX,y:e.touches[0].pageY})},p=function(n){s=null,Bv({disable:!1}),t.refresherHeight>=e.refresherThreshold?w("refreshing"):w("refresherabort")};o.value.addEventListener("touchstart",f,Mg),o.value.addEventListener("touchmove",h,ur(!1)),o.value.addEventListener("scroll",a,ur(!1)),o.value.addEventListener("touchend",p,Mg),Av(),Fo((()=>{o.value.removeEventListener("touchstart",f),o.value.removeEventListener("touchmove",h),o.value.removeEventListener("scroll",a),o.value.removeEventListener("touchend",p)}))})),Oo((()=>{e.scrollY&&(o.value.scrollTop=t.lastScrollTop),e.scrollX&&(o.value.scrollLeft=t.lastScrollLeft)})),xo(n,(e=>{m(e)})),xo(r,(e=>{_(e)})),xo((()=>e.scrollIntoView),(e=>{y(e)})),xo((()=>e.refresherTriggered),(e=>{!0===e?w("refreshing"):!1===e&&w("restore")}))}(e,c,d,h,u,i,a,s,n);var f=dl((()=>{var t="";return e.scrollX?t+="overflow-x:auto;":t+="overflow-x:hidden;",e.scrollY?t+="overflow-y:auto;":t+="overflow-y:hidden;",t}));return()=>{var{refresherEnabled:t,refresherBackground:n,refresherDefaultStyle:u}=e,{refresherHeight:d,refreshState:h,refreshRotate:p}=c;return Ws("uni-scroll-view",{ref:i},[Ws("div",{ref:o,class:"uni-scroll-view"},[Ws("div",{ref:a,style:f.value,class:"uni-scroll-view"},[Ws("div",{ref:s,class:"uni-scroll-view-content"},[t?Ws("div",{ref:l,style:{backgroundColor:n,height:d+"px"},class:"uni-scroll-view-refresher"},["none"!==u?Ws("div",{class:"uni-scroll-view-refresh"},[Ws("div",{class:"uni-scroll-view-refresh-inner"},["pulling"==h?Ws("svg",{key:"refresh__icon",style:{transform:"rotate("+p+"deg)"},fill:"#2BD009",class:"uni-scroll-view-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},[Ws("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null),Ws("path",{d:"M0 0h24v24H0z",fill:"none"},null)],4):null,"refreshing"==h?Ws("svg",{key:"refresh__spinner",class:"uni-scroll-view-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},[Ws("circle",{cx:"50",cy:"50",r:"20",fill:"none",style:"color: #2bd009","stroke-width":"3"},null)]):null])]):null,"none"==u?r.refresher&&r.refresher():null],4):null,r.default&&r.default()],512)],4)],512)],512)}}});const Ig=Vf({name:"Slider",props:{name:{type:String,default:""},min:{type:[Number,String],default:0},max:{type:[Number,String],default:100},value:{type:[Number,String],default:0},step:{type:[Number,String],default:1},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:"#e9e9e9"},backgroundColor:{type:String,default:"#e9e9e9"},activeColor:{type:String,default:"#007aff"},selectedColor:{type:String,default:"#007aff"},blockColor:{type:String,default:"#ffffff"},blockSize:{type:[Number,String],default:28},showValue:{type:[Boolean,String],default:!1}},emits:["changing","change"],setup(e,t){var{emit:n}=t,r=Aa(null),i=Aa(null),a=Aa(null),o=Aa(Number(e.value));xo((()=>e.value),(e=>{o.value=Number(e)}));var s=Yf(r,n),l=function(e,t){var n=()=>{var n=Number(e.max),r=Number(e.min);return 100*(t.value-r)/(n-r)+"%"},r=()=>"#e9e9e9"!==e.backgroundColor?e.backgroundColor:"#007aff"!==e.color?e.color:"#007aff",i=()=>"#007aff"!==e.activeColor?e.activeColor:"#e9e9e9"!==e.selectedColor?e.selectedColor:"#e9e9e9";return{setBgColor:dl((()=>({backgroundColor:r()}))),setBlockBg:dl((()=>({left:n()}))),setActiveColor:dl((()=>({backgroundColor:i(),width:n()}))),setBlockStyle:dl((()=>({width:e.blockSize+"px",height:e.blockSize+"px",marginLeft:-e.blockSize/2+"px",marginTop:-e.blockSize/2+"px",left:n(),backgroundColor:e.blockColor})))}}(e,o),{_onClick:u,_onTrack:c}=function(e,t,n,r,i){var a=n=>{e.disabled||(s(n),i("change",n,{value:t.value}))},o=t=>{var n=Number(e.max),r=Number(e.min),i=Number(e.step);return t<r?r:t>n?n:Lg.mul.call(Math.round((t-r)/i),i)+r},s=i=>{var a=Number(e.max),s=Number(e.min),l=r.value,u=getComputedStyle(l,null).marginLeft,c=l.offsetWidth;c+=parseInt(u);var d=n.value,h=d.offsetWidth-(e.showValue?c:0),f=d.getBoundingClientRect().left,p=(i.x-f)*(a-s)/h+s;t.value=o(p)},l=n=>{if(!e.disabled)return"move"===n.detail.state?(s({x:n.detail.x}),i("changing",n,{value:t.value}),!1):"end"===n.detail.state&&i("change",n,{value:t.value})},u=yo(Xf,!1);if(u){var c={reset:()=>t.value=Number(e.min),submit:()=>{var n=["",null];return""!==e.name&&(n[0]=e.name,n[1]=t.value),n}};u.addField(c),Fo((()=>{u.removeField(c)}))}return{_onClick:a,_onTrack:l}}(e,o,r,i,s);return Po((()=>{Vv(a.value,c)})),()=>{var{setBgColor:t,setBlockBg:n,setActiveColor:s,setBlockStyle:c}=l;return Ws("uni-slider",{ref:r,onClick:qf(u)},[Ws("div",{class:"uni-slider-wrapper"},[Ws("div",{class:"uni-slider-tap-area"},[Ws("div",{style:t.value,class:"uni-slider-handle-wrapper"},[Ws("div",{ref:a,style:n.value,class:"uni-slider-handle"},null,4),Ws("div",{style:c.value,class:"uni-slider-thumb"},null,4),Ws("div",{style:s.value,class:"uni-slider-track"},null,4)],4)]),Ho(Ws("span",{ref:i,class:"uni-slider-value"},[o.value],512),[[Jl,e.showValue]])]),Ws("slot",null,null)],8,["onClick"])}}});var Lg={mul:function(e){var t=0,n=this.toString(),r=e.toString();try{t+=n.split(".")[1].length}catch(i){}try{t+=r.split(".")[1].length}catch(i){}return Number(n.replace(".",""))*Number(r.replace(".",""))/Math.pow(10,t)}};function Ag(e,t,n,r,i,a){function o(){u&&(clearTimeout(u),u=null)}var s,l,u=null,c=!0,d=0,h=1,f=null,p=!1,v=0,g="",m=dl((()=>n.value.length>t.displayMultipleItems)),_=dl((()=>e.circular&&m.value));function y(i){Math.floor(2*d)===Math.floor(2*i)&&Math.ceil(2*d)===Math.ceil(2*i)||_.value&&function(r){if(!c)for(var i=n.value,a=i.length,o=r+t.displayMultipleItems,s=0;s<a;s++){var l=i[s],u=Math.floor(r/a)*a+s,d=u+a,h=u-a,f=Math.max(r-(u+1),u-o,0),p=Math.max(r-(d+1),d-o,0),v=Math.max(r-(h+1),h-o,0),g=Math.min(f,p,v),m=[u,d,h][[f,p,v].indexOf(g)];l.updatePosition(m,e.vertical)}}(i);var o="translate("+(e.vertical?"0":100*-i*h+"%")+", "+(e.vertical?100*-i*h+"%":"0")+") translateZ(0)",l=r.value;if(l&&(l.style.webkitTransform=o,l.style.transform=o),d=i,!s){if(i%1==0)return;s=i}i-=Math.floor(s);var u=n.value;i<=-(u.length-1)?i+=u.length:i>=u.length&&(i-=u.length),i=s%1>.5||s<0?i-1:i,a("transition",{},{dx:e.vertical?0:i*l.offsetWidth,dy:e.vertical?i*l.offsetHeight:0})}function b(e){var r=n.value.length;if(!r)return-1;var i=(Math.round(e)%r+r)%r;if(_.value){if(r<=t.displayMultipleItems)return 0}else if(i>r-t.displayMultipleItems)return r-t.displayMultipleItems;return i}function w(){f=null}function x(){if(f){var e=f,r=e.toPos,i=e.acc,o=e.endTime,u=e.source,c=o-Date.now();if(c<=0){y(r),f=null,p=!1,s=null;var d=n.value[t.current];if(d){var h=d.getItemId();a("animationfinish",{},{current:t.current,currentItemId:h,source:u})}}else{y(r+i*c*c/2),l=requestAnimationFrame(x)}}else p=!1}function S(e,r,i){w();var a=t.duration,o=n.value.length,s=d;if(_.value)if(i<0){for(;s<e;)s+=o;for(;s-o>e;)s-=o}else if(i>0){for(;s>e;)s-=o;for(;s+o<e;)s+=o;s+o-e<e-s&&(s+=o)}else{for(;s+o<e;)s+=o;for(;s-o>e;)s-=o;s+o-e<e-s&&(s+=o)}else"click"===r&&(e=e+t.displayMultipleItems-1<o?e:0);f={toPos:e,acc:2*(s-e)/(a*a),endTime:Date.now()+a,source:r},p||(p=!0,l=requestAnimationFrame(x))}function k(){o();var e=n.value,r=function(){u=null,g="autoplay",_.value?t.current=b(t.current+1):t.current=t.current+t.displayMultipleItems<e.length?t.current+1:0,S(t.current,"autoplay",_.value?1:0),u=setTimeout(r,t.interval)};c||e.length<=t.displayMultipleItems||(u=setTimeout(r,t.interval))}function T(e){e?k():o()}return xo([()=>e.current,()=>e.currentItemId,()=>[...n.value]],(()=>{var r=-1;if(e.currentItemId)for(var i=0,a=n.value;i<a.length;i++){if(a[i].getItemId()===e.currentItemId){r=i;break}}r<0&&(r=Math.round(e.current)||0),r=r<0?0:r,t.current!==r&&(g="",t.current=r)})),xo([()=>e.vertical,()=>_.value,()=>t.displayMultipleItems,()=>[...n.value]],(function(){o(),f&&(y(f.toPos),f=null);for(var i=n.value,a=0;a<i.length;a++)i[a].updatePosition(a,e.vertical);h=1;var s=r.value;if(1===t.displayMultipleItems&&i.length){var l=i[0].getBoundingClientRect(),u=s.getBoundingClientRect();(h=l.width/u.width)>0&&h<1||(h=1)}var p=d;d=-2;var g=t.current;g>=0?(c=!1,t.userTracking?(y(p+g-v),v=g):(y(g),e.autoplay&&k())):(c=!0,y(-t.displayMultipleItems-1))})),xo((()=>t.interval),(()=>{u&&(o(),k())})),xo((()=>t.current),((e,r)=>{!function(e,r){var i=g;g="";var o=n.value;if(!i){var s=o.length;S(e,"",_.value&&r+(s-e)%s>s/2?1:0)}var l=o[e];if(l){var u=t.currentItemId=l.getItemId();a("change",{},{current:t.current,currentItemId:u,source:i})}}(e,r),i("update:current",e)})),xo((()=>t.currentItemId),(e=>{i("update:currentItemId",e)})),xo((()=>e.autoplay&&!t.userTracking),T),T(e.autoplay&&!t.userTracking),Po((()=>{var i=!1,a=0,s=0;function l(e){t.userTracking=!1;var n=a/Math.abs(a),r=0;!e&&Math.abs(a)>.2&&(r=.5*n);var i=b(d+r);e?y(v):(g="touch",t.current=i,S(i,"touch",0!==r?r:0===i&&_.value&&d>=1?1:0))}Vv(r.value,(u=>{if(!e.disableTouch&&!c){if("start"===u.detail.state)return t.userTracking=!0,i=!1,o(),v=d,a=0,s=Date.now(),void w();if("end"===u.detail.state)return l(!1);if("cancel"===u.detail.state)return l(!0);if(t.userTracking){if(!i){i=!0;var h=Math.abs(u.detail.dx),f=Math.abs(u.detail.dy);if((h>=f&&e.vertical||h<=f&&!e.vertical)&&(t.userTracking=!1),!t.userTracking)return void(e.autoplay&&k())}return function(i){var o=s;s=Date.now();var l=n.value.length-t.displayMultipleItems;function u(e){return.5-.25/(e+.5)}function c(e,t){var n=v+e;a=.6*a+.4*t,_.value||(n<0||n>l)&&(n<0?n=-u(-n):n>l&&(n=l+u(n-l)),a=0),y(n)}var d=s-o||1,h=r.value;e.vertical?c(-i.dy/h.offsetHeight,-i.ddy/d):c(-i.dx/h.offsetWidth,-i.ddx/d)}(u.detail),!1}}}))})),$o((()=>{o(),cancelAnimationFrame(l)})),{onSwiperDotClick:function(e){S(t.current=e,g="click",_.value?1:0)},circularEnabled:_,swiperEnabled:m}}const Bg=Vf({name:"Swiper",props:{indicatorDots:{type:[Boolean,String],default:!1},vertical:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},circular:{type:[Boolean,String],default:!1},interval:{type:[Number,String],default:5e3},duration:{type:[Number,String],default:500},current:{type:[Number,String],default:0},indicatorColor:{type:String,default:""},indicatorActiveColor:{type:String,default:""},previousMargin:{type:String,default:""},nextMargin:{type:String,default:""},currentItemId:{type:String,default:""},skipHiddenItemLayout:{type:[Boolean,String],default:!1},displayMultipleItems:{type:[Number,String],default:1},disableTouch:{type:[Boolean,String],default:!1},navigation:{type:[Boolean,String],default:!1},navigationColor:{type:String,default:"#fff"},navigationActiveColor:{type:String,default:"rgba(53, 53, 53, 0.6)"}},emits:["change","transition","animationfinish","update:current","update:currentItemId"],setup(e,t){var{slots:n,emit:r}=t,i=Aa(null),a=Yf(i,r),o=Aa(null),s=Aa(null),l=function(e){return ma({interval:dl((()=>{var t=Number(e.interval);return isNaN(t)?5e3:t})),duration:dl((()=>{var t=Number(e.duration);return isNaN(t)?500:t})),displayMultipleItems:dl((()=>{var t=Math.round(e.displayMultipleItems);return isNaN(t)?1:t})),current:Math.round(e.current)||0,currentItemId:e.currentItemId,userTracking:!1})}(e),u=dl((()=>{var t={};return(e.nextMargin||e.previousMargin)&&(t=e.vertical?{left:0,right:0,top:bu(e.previousMargin,!0),bottom:bu(e.nextMargin,!0)}:{top:0,bottom:0,left:bu(e.previousMargin,!0),right:bu(e.nextMargin,!0)}),t})),c=dl((()=>{var t=Math.abs(100/l.displayMultipleItems)+"%";return{width:e.vertical?"100%":t,height:e.vertical?t:"100%"}})),d=[],h=[],f=Aa([]);function p(){for(var e=[],t=function(t){var n=d[t];n instanceof Element||(n=n.el);var r=h.find((e=>n===e.rootRef.value));r&&e.push(Ea(r))},n=0;n<d.length;n++)t(n);f.value=e}Rv((()=>{d=s.value.children,p()}));_o("addSwiperContext",(function(e){h.push(e),p()}));_o("removeSwiperContext",(function(e){var t=h.indexOf(e);t>=0&&(h.splice(t,1),p())}));var{onSwiperDotClick:v,circularEnabled:g,swiperEnabled:m}=Ag(e,l,f,s,r,a);return()=>{var t=n.default&&n.default();return d=Nv(t),Ws("uni-swiper",{ref:i},[Ws("div",{ref:o,class:"uni-swiper-wrapper"},[Ws("div",{class:"uni-swiper-slides",style:u.value},[Ws("div",{ref:s,class:"uni-swiper-slide-frame",style:c.value},[t],4)],4),e.indicatorDots&&Ws("div",{class:["uni-swiper-dots",e.vertical?"uni-swiper-dots-vertical":"uni-swiper-dots-horizontal"]},[f.value.map(((t,n,r)=>Ws("div",{onClick:()=>v(n),class:{"uni-swiper-dot":!0,"uni-swiper-dot-active":n<l.current+l.displayMultipleItems&&n>=l.current||n<l.current+l.displayMultipleItems-r.length},style:{background:n===l.current?e.indicatorActiveColor:e.indicatorColor}},null,14,["onClick"])))],2),null],512)],512)}}});const Ng=Vf({name:"SwiperItem",props:{itemId:{type:String,default:""}},setup(e,t){var{slots:n}=t,r=Aa(null),i={rootRef:r,getItemId:()=>e.itemId,getBoundingClientRect:()=>r.value.getBoundingClientRect(),updatePosition(e,t){var n=t?"0":100*e+"%",i=t?100*e+"%":"0",a=r.value,o="translate(".concat(n,",").concat(i,") translateZ(0)");a&&(a.style.webkitTransform=o,a.style.transform=o)}};return Po((()=>{var e=yo("addSwiperContext");e&&e(i)})),$o((()=>{var e=yo("removeSwiperContext");e&&e(i)})),()=>Ws("uni-swiper-item",{ref:r,style:{position:"absolute",width:"100%",height:"100%"}},[n.default&&n.default()],512)}});const Rg=Vf({name:"Switch",props:{name:{type:String,default:""},checked:{type:[Boolean,String],default:!1},type:{type:String,default:"switch"},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:""}},emits:["change"],setup(e,t){var{emit:n}=t,r=Aa(null),i=Aa(e.checked),a=function(e,t){var n=yo(Xf,!1),r=yo(Kf,!1),i={submit:()=>{var n=["",null];return e.name&&(n[0]=e.name,n[1]=t.value),n},reset:()=>{t.value=!1}};n&&(n.addField(i),$o((()=>{n.removeField(i)})));return r}(e,i),o=Yf(r,n);xo((()=>e.checked),(e=>{i.value=e}));var s=t=>{e.disabled||(i.value=!i.value,o("change",t,{value:i.value}))};return a&&(a.addHandler(s),Fo((()=>{a.removeHandler(s)}))),Qf(e,{"label-click":s}),()=>{var{color:t,type:n}=e,a=Hf(e,"disabled"),o={};return t&&i.value&&(o.backgroundColor=t,o.borderColor=t),Ws("uni-switch",Gs({ref:r},a,{onClick:s}),[Ws("div",{class:"uni-switch-wrapper"},[Ho(Ws("div",{class:["uni-switch-input",[i.value?"uni-switch-input-checked":""]],style:o},null,6),[[Jl,"switch"===n]]),Ho(Ws("div",{class:"uni-checkbox-input"},[i.value?ku(Su,e.color,22):""],512),[[Jl,"checkbox"===n]])])],16,["onClick"])}}});var Pg={ensp:" ",emsp:" ",nbsp:" "};function Dg(e,t){return e.replace(/\\n/g,Yn).split(Yn).map((e=>function(e,t){var{space:n,decode:r}=t;if(!e)return e;n&&Pg[n]&&(e=e.replace(/ /g,Pg[n]));if(!r)return e;return e.replace(/&nbsp;/g,Pg.nbsp).replace(/&ensp;/g,Pg.ensp).replace(/&emsp;/g,Pg.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'")}(e,t)))}var zg=hn({},bv,{placeholderClass:{type:String,default:"input-placeholder"},autoHeight:{type:[Boolean,String],default:!1},confirmType:{type:String,default:"return",validator:e=>$g.concat("return").includes(e)}}),Fg=!1,$g=["done","go","next","search","send"];const Vg=Vf({name:"Textarea",props:zg,emits:["confirm","linechange",...wv],setup(e,t){var n,{emit:r}=t,i=Aa(null),a=Aa(null),{fieldRef:o,state:s,scopedAttrsState:l,fixDisabledColor:u,trigger:c}=kv(e,i,r),d=dl((()=>s.value.split(Yn))),h=dl((()=>$g.includes(e.confirmType))),f=Aa(0),p=Aa(null);function v(e){var{height:t}=e;f.value=t}function g(e){"Enter"===e.key&&h.value&&e.preventDefault()}function m(t){if("Enter"===t.key&&h.value){!function(e){c("confirm",e,{value:s.value})}(t);var n=t.target;!e.confirmHold&&n.blur()}}return xo((()=>f.value),(t=>{var n=i.value,r=p.value,o=a.value,s=parseFloat(getComputedStyle(n).lineHeight);isNaN(s)&&(s=r.offsetHeight);var l=Math.round(t/s);c("linechange",{},{height:t,heightRpx:750/window.innerWidth*t,lineCount:l}),e.autoHeight&&(n.style.height="auto",o.style.height=t+"px")})),n="(prefers-color-scheme: dark)",Fg=0===String(navigator.platform).indexOf("iP")&&0===String(navigator.vendor).indexOf("Apple")&&window.matchMedia(n).media!==n,()=>{var t=e.disabled&&u?Ws("textarea",{key:"disabled-textarea",ref:o,value:s.value,tabindex:"-1",readonly:!!e.disabled,maxlength:s.maxlength,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":Fg},style:{overflowY:e.autoHeight?"hidden":"auto"},onFocus:e=>e.target.blur()},null,46,["value","readonly","maxlength","onFocus"]):Ws("textarea",{key:"textarea",ref:o,value:s.value,disabled:!!e.disabled,maxlength:s.maxlength,enterkeyhint:e.confirmType,inputmode:e.inputmode,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":Fg},style:{overflowY:e.autoHeight?"hidden":"auto"},onKeydown:g,onKeyup:m},null,46,["value","disabled","maxlength","enterkeyhint","inputmode","onKeydown","onKeyup"]);return Ws("uni-textarea",{ref:i},[Ws("div",{ref:a,class:"uni-textarea-wrapper"},[Ho(Ws("div",Gs(l.attrs,{style:e.placeholderStyle,class:["uni-textarea-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Jl,!s.value.length]]),Ws("div",{ref:p,class:"uni-textarea-line"},[" "],512),Ws("div",{class:"uni-textarea-compute"},[d.value.map((e=>Ws("div",null,[e.trim()?e:"."]))),Ws(rp,{initial:!0,onResize:v},null,8,["initial","onResize"])]),"search"===e.confirmType?Ws("form",{action:"",onSubmit:()=>!1,class:"uni-input-form"},[t],40,["onSubmit"]):t],512)],512)}}});function jg(e,t){if(t||(t=e.id),t)return e.$options.name.toLowerCase()+"."+t}function Wg(e,t,n){e&&Gr(n||Eu(),e,((e,n)=>{var{type:r,data:i}=e;t(r,i,n)}))}function Ug(e,t){e&&function(e,t){t=Zr(e,t),delete Xr[t]}(t||Eu(),e)}function Hg(e,t,n,r){var i=tl().proxy;Po((()=>{Wg(t||jg(i),e,r),!n&&t||xo((()=>i.id),((t,n)=>{Wg(jg(i,t),e,r),Ug(n&&jg(i,n))}))})),Fo((()=>{Ug(t||jg(i),r)}))}hn({},Wf);var qg=0;function Yg(e){var t=Tu(),n=tl().proxy,r=n.$options.name.toLowerCase(),i=e||n.id||"context".concat(qg++);return Po((()=>{n.$el.__uniContextInfo={id:i,type:r,page:t}})),"".concat(r,".").concat(i)}class Xg extends Df{constructor(e,t,n,r,i){super(e,t,n,r,i,[...$f.props,...arguments.length>5&&void 0!==arguments[5]?arguments[5]:[]])}call(e){var t={animation:this.$props.animation,$el:this.$};e.call(t)}setAttribute(e,t){return"animation"===e&&(this.$animate=!0),super.setAttribute(e,t)}update(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.$animate)return e?this.call($f.mounted):void(this.$animate&&(this.$animate=!1,this.call($f.watch.animation.handler)))}}var Zg=["space","decode"];var Gg=["hover-class","hover-stop-propagation","hover-start-time","hover-stay-time"];class Kg extends Xg{constructor(e,t,n,r,i){super(e,t,n,r,i,[...Gg,...arguments.length>5&&void 0!==arguments[5]?arguments[5]:[]])}update(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.$props["hover-class"];t&&"none"!==t?(this._hover||(this._hover=new Jg(this.$,this.$props)),this._hover.addEvent()):this._hover&&this._hover.removeEvent(),super.update(e)}}class Jg{constructor(e,t){this._listening=!1,this._hovering=!1,this._hoverTouch=!1,this.$=e,this.props=t,this.__hoverTouchStart=this._hoverTouchStart.bind(this),this.__hoverTouchEnd=this._hoverTouchEnd.bind(this),this.__hoverTouchCancel=this._hoverTouchCancel.bind(this)}get hovering(){return this._hovering}set hovering(e){this._hovering=e;var t=this.props["hover-class"].split(" ").filter(Boolean),n=this.$.classList;e?this.$.classList.add.apply(n,t):this.$.classList.remove.apply(n,t)}addEvent(){this._listening||(this._listening=!0,this.$.addEventListener("touchstart",this.__hoverTouchStart),this.$.addEventListener("touchend",this.__hoverTouchEnd),this.$.addEventListener("touchcancel",this.__hoverTouchCancel))}removeEvent(){this._listening&&(this._listening=!1,this.$.removeEventListener("touchstart",this.__hoverTouchStart),this.$.removeEventListener("touchend",this.__hoverTouchEnd),this.$.removeEventListener("touchcancel",this.__hoverTouchCancel))}_hoverTouchStart(e){if(!e._hoverPropagationStopped){var t=this.props["hover-class"];t&&"none"!==t&&!this.$.disabled&&(e.touches.length>1||(this.props["hover-stop-propagation"]&&(e._hoverPropagationStopped=!0),this._hoverTouch=!0,this._hoverStartTimer=setTimeout((()=>{this.hovering=!0,this._hoverTouch||this._hoverReset()}),this.props["hover-start-time"])))}}_hoverTouchEnd(){this._hoverTouch=!1,this.hovering&&this._hoverReset()}_hoverReset(){requestAnimationFrame((()=>{clearTimeout(this._hoverStayTimer),this._hoverStayTimer=setTimeout((()=>{this.hovering=!1}),this.props["hover-stay-time"])}))}_hoverTouchCancel(){this._hoverTouch=!1,this.hovering=!1,clearTimeout(this._hoverStartTimer)}}function Qg(){return plus.navigator.isImmersedStatusbar()?Math.round("iOS"===plus.os.name?plus.navigator.getSafeAreaInsets().top:plus.navigator.getStatusbarHeight()):0}function em(){var e=plus.webview.currentWebview().getStyle(),t=e&&e.titleNView;return t&&"default"===t.type?44+Qg():0}var tm=Symbol("onDraw");function nm(e,t){return dl((()=>{var n={};return Object.keys(e).forEach((r=>{if(!t||!t.includes(r)){var i=e[r];i="src"===r?ac(i):i,n[r.replace(/[A-Z]/g,(e=>"-"+e.toLowerCase()))]=i}})),n}))}function rm(e){var t=ma({top:"0px",left:"0px",width:"0px",height:"0px",position:"static"}),n=Aa(!1);function r(){var r=e.value,i=r.getBoundingClientRect(),a=["width","height"];n.value=0===i.width||0===i.height,n.value||(t.position=function(e){for(var t;e;){var n=getComputedStyle(e),r=n.transform||n.webkitTransform;t=(!r||"none"===r)&&t,t="fixed"===n.position||t,e=e.parentElement}return t}(r)?"absolute":"static",a.push("top","left")),a.forEach((e=>{var n=i[e];n="top"===e?n+("static"===t.position?document.documentElement.scrollTop||document.body.scrollTop||0:em()):n,t[e]=n+"px"}))}var i=null;function a(){i&&cancelAnimationFrame(i),i=requestAnimationFrame((()=>{i=null,r()}))}window.addEventListener("updateview",a);var o=[],s=[];return _o(tm,(function(e){o?o.push(e):e(t)})),Po((()=>{r(),s.forEach((e=>e())),s=null})),Fo((()=>{window.removeEventListener("updateview",a)})),{position:t,hidden:n,onParentReady:function(e){var n=yo(tm),r=n=>{e(n),o.forEach((e=>e(t))),o=null};!function(e){s?s.push(e):e()}((()=>{n?n(r):r({top:"0px",left:"0px",width:Number.MAX_SAFE_INTEGER+"px",height:Number.MAX_SAFE_INTEGER+"px",position:"static"})}))}}}const im=Vf({name:"Ad",props:{adpid:{type:[Number,String],default:""},data:{type:Object,default:null},dataCount:{type:Number,default:5},channel:{type:String,default:""}},setup(e,t){var n,{emit:r}=t,i=Aa(null),a=Aa(null),o=Yf(i,r),s=nm(e,["id"]),{position:l,onParentReady:u}=rm(a);return u((()=>{function t(){var t={adpid:e.adpid,width:l.width,count:e.dataCount};void 0!==e.channel&&(t.ext={channel:e.channel}),UniViewJSBridge.invokeServiceMethod("getAdData",t,(e=>{var{code:t,data:r,message:i}=e;0===t?n.renderingBind(r):o("error",{},{errMsg:i})}))}n=plus.ad.createAdView(Object.assign({},s.value,l)),plus.webview.currentWebview().append(n),n.setDislikeListener((e=>{a.value.style.height="0",window.dispatchEvent(new CustomEvent("updateview")),o("close",{},e)})),n.setRenderingListener((e=>{0===e.result?(a.value.style.height=e.height+"px",window.dispatchEvent(new CustomEvent("updateview"))):o("error",{},{errCode:e.result})})),n.setAdClickedListener((()=>{o("adclicked",{},{})})),xo((()=>l),(e=>n.setStyle(e)),{deep:!0}),xo((()=>e.adpid),(e=>{e&&t()})),xo((()=>e.data),(e=>{e&&n.renderingBind(e)})),e.adpid&&t()})),Fo((()=>{n&&n.close()})),()=>Ws("uni-ad",{ref:i},[Ws("div",{ref:a,class:"uni-ad-container"},null,512)],512)}});class am extends df{constructor(e,t,n,r,i,a,o){super(e,t,r);var s=document.createElement("div");s.__vueParent=function(e){for(;e&&e.pid>0;)if(e=Nm(e.pid)){var{__vueParentComponent:t}=e.$;if(t)return t}return null}(this),this.$props=ma({}),this.init(a),this.$app=ru(function(e,t){return()=>hl(e,t)}(n,this.$props)),this.$app.mount(s),this.$=s.firstElementChild,this.$.__id=e,o&&(this.$holder=this.$.querySelector(o)),vn(a,"t")&&this.setText(a.t||""),a.a&&vn(a.a,mr)&&Pf(this.$,a.a[".vShow"]),this.insert(r,i),no()}init(e){var{a:t,e:n,w:r}=e;t&&(this.setWxsProps(t),Object.keys(t).forEach((e=>{this.setAttr(e,t[e])}))),vn(e,"s")&&this.setAttr("style",e.s),n&&Object.keys(n).forEach((e=>{this.addEvent(e,n[e])})),r&&this.addWxsEvents(e.w)}setText(e){(this.$holder||this.$).textContent=e,this.updateView()}addWxsEvent(e,t,n){this.$props[e]=Rf(this,t,n)}addEvent(e,t){this.$props[e]=Bf(this.id,t,fr(e)[1])}removeEvent(e){this.$props[e]=null}setAttr(e,t){if(e===mr)this.$&&Pf(this.$,t);else if(e===_r)this.$.__ownerId=t;else if(e===yr)Kh((()=>sf(this,t)),3);else if(e===gr){var n=uf(t,this.$||Nm(this.pid).$),r=this.$props.style;Cn(n)&&Cn(r)?Object.keys(n).forEach((e=>{r[e]=n[e]})):this.$props.style=n}else cf(e)?this.$.style.setProperty(e,ff(t)):(t=uf(t,this.$||Nm(this.pid).$),this.wxsPropsInvoke(e,t,!0)||(this.$props[e]=t));this.updateView()}removeAttr(e){cf(e)?this.$.style.removeProperty(e):this.$props[e]=null,this.updateView()}remove(){this.removeUniParent(),this.isUnmounted=!0,this.$app.unmount(),Rm(this.id),this.removeUniChildren(),this.updateView()}appendChild(e){var t=(this.$holder||this.$).appendChild(e);return this.updateView(!0),t}insertBefore(e,t){var n=(this.$holder||this.$).insertBefore(e,t);return this.updateView(!0),n}}class om extends am{constructor(e,t,n,r,i,a,o){super(e,t,n,r,i,a,o)}getRebuildFn(){return this._rebuild||(this._rebuild=this.rebuild.bind(this)),this._rebuild}setText(e){return Kh(this.getRebuildFn(),2),super.setText(e)}appendChild(e){return Kh(this.getRebuildFn(),2),super.appendChild(e)}insertBefore(e,t){return Kh(this.getRebuildFn(),2),super.insertBefore(e,t)}removeUniChild(e){return Kh(this.getRebuildFn(),2),super.removeUniChild(e)}rebuild(){var e=this.$.__vueParentComponent;e.rebuild&&e.rebuild()}}function sm(e,t,n){e.childNodes.forEach((n=>{n instanceof Element?-1===n.className.indexOf(t)&&e.removeChild(n):e.removeChild(n)})),e.appendChild(document.createTextNode(n))}var lm=["value","modelValue"];function um(e){lm.forEach((t=>{if(vn(e,t)){var n="onUpdate:"+t;vn(e,n)||(e[n]=n=>e[t]=n)}}))}class cm extends df{constructor(e,t,n,r){super(e,t,n),this.insert(n,r)}}var dm=0;function hm(e,t,n){var r,i,{position:a,hidden:o,onParentReady:s}=rm(e);s((s=>{var l=dl((()=>{var e={};for(var t in a){var n=a[t],r=parseFloat(n),i=parseFloat(s[t]);if("top"===t||"left"===t)n=Math.max(r,i)+"px";else if("width"===t||"height"===t){var o="width"===t?"left":"top",l=parseFloat(s[o]),u=parseFloat(a[o]),c=Math.max(l-u,0),d=Math.max(u+r-(l+i),0);n=Math.max(r-c-d,0)+"px"}e[t]=n}return e})),u=["borderRadius","borderColor","borderWidth","backgroundColor"],c=["paddingTop","paddingRight","paddingBottom","paddingLeft","color","textAlign","lineHeight","fontSize","fontWeight","textOverflow","whiteSpace"],d=[],h={start:"left",end:"right"};function f(t){var n=getComputedStyle(e.value);return u.concat(c,d).forEach((e=>{t[e]=n[e]})),t}var p=ma(f({})),v=null;i=function(){v&&cancelAnimationFrame(v),v=requestAnimationFrame((()=>{v=null,f(p)}))},window.addEventListener("updateview",i);var g=dl((()=>{var e=function(){var e={};for(var t in e){var n=e[t];"top"!==t&&"left"!==t||(n=Math.min(parseFloat(n)-parseFloat(s[t]),0)+"px"),e[t]=n}return e}(),t=[{tag:"rect",position:e,rectStyles:{color:p.backgroundColor,radius:p.borderRadius,borderColor:p.borderColor,borderWidth:p.borderWidth}}];if("src"in n)n.src&&t.push({tag:"img",position:e,src:n.src});else{var r=parseFloat(p.lineHeight)-parseFloat(p.fontSize),i=parseFloat(e.width)-parseFloat(p.paddingLeft)-parseFloat(p.paddingRight);i=i<0?0:i;var a=parseFloat(e.height)-parseFloat(p.paddingTop)-r/2-parseFloat(p.paddingBottom);a=a<0?0:a,t.push({tag:"font",position:{top:"".concat(parseFloat(e.top)+parseFloat(p.paddingTop)+r/2,"px"),left:"".concat(parseFloat(e.left)+parseFloat(p.paddingLeft),"px"),width:"".concat(i,"px"),height:"".concat(a,"px")},textStyles:{align:h[p.textAlign]||p.textAlign,color:p.color,decoration:"none",lineSpacing:"".concat(r,"px"),margin:"0px",overflow:p.textOverflow,size:p.fontSize,verticalAlign:"top",weight:p.fontWeight,whiteSpace:p.whiteSpace},text:n.text})}return t}));r=new plus.nativeObj.View("cover-".concat(Date.now(),"-").concat(dm++),l.value,g.value),plus.webview.currentWebview().append(r),o.value&&r.hide(),r.addEventListener("click",(()=>{t("click",{},{})})),xo((()=>o.value),(e=>{r[e?"hide":"show"]()})),xo((()=>l.value),(e=>{r.setStyle(e)}),{deep:!0}),xo((()=>g.value),(()=>{r.reset(),r.draw(g.value)}),{deep:!0})})),Fo((()=>{r&&r.close(),i&&window.removeEventListener("updateview",i)}))}const fm=Vf({name:"CoverImage",props:{src:{type:String,default:""},autoSize:{type:[Boolean,String],default:!1}},emits:["click","load","error"],setup(e,t){var{emit:n}=t,r=Aa(null),i=Yf(r,n),a=ma({src:""}),o=function(e,t,n){var r,i=Aa("");function a(){t.src="",i.value=e.autoSize?"width:0;height:0;":"";var a=e.src?ac(e.src):"";0===a.indexOf("http://")||0===a.indexOf("https://")?(r=plus.downloader.createDownload(a,{filename:"_doc/uniapp_temp//download/"},((e,t)=>{200===t?o(e.filename):n("error",{},{errMsg:"error"})}))).start():a&&o(a)}function o(r){t.src=r,plus.io.getImageInfo({src:r,success:t=>{var{width:r,height:a}=t;e.autoSize&&(i.value="width:".concat(r,"px;height:").concat(a,"px;"),window.dispatchEvent(new CustomEvent("updateview"))),n("load",{},{width:r,height:a})},fail:()=>{n("error",{},{errMsg:"error"})}})}return e.src&&a(),xo((()=>e.src),a),Fo((()=>{r&&r.abort()})),i}(e,a,i);return hm(r,i,a),()=>Ws("uni-cover-image",{ref:r,style:o.value},[Ws("div",{class:"uni-cover-image"},null)],4)}});const pm=Vf({name:"CoverView",emits:["click"],setup(e,t){var{emit:n}=t,r=Aa(null),i=Aa(null),a=Yf(r,n),o=ma({text:""});return hm(r,a,o),Rv((()=>{var e=i.value.childNodes[0];o.text=e&&e instanceof Text?e.textContent:"",window.dispatchEvent(new CustomEvent("updateview"))})),()=>Ws("uni-cover-view",{ref:r},[Ws("div",{ref:i,class:"uni-cover-view"},null,512)],512)}});var vm={id:{type:String,default:""},url:{type:String,default:""},mode:{type:String,default:"SD"},muted:{type:[Boolean,String],default:!1},enableCamera:{type:[Boolean,String],default:!0},autoFocus:{type:[Boolean,String],default:!0},beauty:{type:[Number,String],default:0},whiteness:{type:[Number,String],default:0},aspect:{type:[String],default:"3:2"},minBitrate:{type:[Number],default:200}},gm=["statechange","netstatus","error"];const mm=Vf({name:"LivePusher",props:vm,emits:gm,setup(e,t){var n,{emit:r}=t,i=Aa(null),a=Yf(i,r),o=Aa(null),s=nm(e,["id"]),{position:l,hidden:u,onParentReady:c}=rm(o);return c((()=>{n=new plus.video.LivePusher("livePusher"+Date.now(),Object.assign({},s.value,l)),plus.webview.currentWebview().append(n),gm.forEach((e=>{n.addEventListener(e,(t=>{a(e,{},t.detail)}))})),xo((()=>s.value),(e=>n.setStyles(e)),{deep:!0}),xo((()=>l),(e=>n.setStyles(e)),{deep:!0}),xo((()=>u.value),(e=>{e||n.setStyles(l)}))})),Hg(((e,t)=>{n&&n[e](t)}),Yg(),!0),Fo((()=>{n&&n.close()})),()=>Ws("uni-live-pusher",{ref:i,id:e.id},[Ws("div",{ref:o,class:"uni-live-pusher-container"},null,512)],8,["id"])}});function _m(e){if(0!==e.indexOf("#"))return{color:e,opacity:1};var t=e.slice(7,9);return{color:e.slice(0,7),opacity:t?Number("0x"+t)/255:1}}const ym=Vf({name:"Map",props:{id:{type:String,default:""},latitude:{type:[Number,String],default:""},longitude:{type:[Number,String],default:""},scale:{type:[String,Number],default:16},markers:{type:Array,default:()=>[]},polyline:{type:Array,default:()=>[]},circles:{type:Array,default:()=>[]},polygons:{type:Array,default:()=>[]},controls:{type:Array,default:()=>[]}},emits:["click","regionchange","controltap","markertap","callouttap"],setup(e,t){var n,{emit:r}=t,i=Aa(null),a=Yf(i,r),o=Aa(null),s=nm(e,["id"]),{position:l,hidden:u,onParentReady:c}=rm(o),{_addMarkers:d,_addMapLines:h,_addMapCircles:f,_addMapPolygons:p,_setMap:v}=function(e,t){var n;function r(t){var{longitude:r,latitude:i}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};n&&(n.setCenter(new plus.maps.Point(Number(r||e.longitude),Number(i||e.latitude))),t({errMsg:"moveToLocation:ok"}))}function i(e){n&&n.getCurrentCenter(((t,n)=>{e({longitude:n.getLng(),latitude:n.getLat(),errMsg:"getCenterLocation:ok"})}))}function a(e){if(n){var t=n.getBounds();e({southwest:t.getSouthWest(),northeast:t.getNorthEast(),errMsg:"getRegion:ok"})}}function o(e){n&&e({scale:n.getZoom(),errMsg:"getScale:ok"})}function s(e){if(n){var{id:r,latitude:i,longitude:a,iconPath:o,callout:s,label:l}=e;(e=>{var i,{latitude:a,longitude:u}=e.coord,c=new plus.maps.Marker(new plus.maps.Point(u,a));o&&c.setIcon(ac(o)),l&&l.content&&c.setLabel(l.content);var d=void 0;s&&s.content&&(d=new plus.maps.Bubble(s.content)),d&&c.setBubble(d),(r||0===r)&&(c.onclick=e=>{t("markertap",{},{markerId:r,latitude:a,longitude:u})},d&&(d.onclick=()=>{t("callouttap",{},{markerId:r})})),null===(i=n)||void 0===i||i.addOverlay(c),n.__markers__.push(c)})({coord:{latitude:i,longitude:a}})}}function l(){n&&(n.__markers__.forEach((e=>{var t;null===(t=n)||void 0===t||t.removeOverlay(e)})),n.__markers__=[])}function u(e,t){t&&l(),e.forEach((e=>{s(e)}))}function c(e){n&&(n.__lines__.length>0&&(n.__lines__.forEach((e=>{var t;null===(t=n)||void 0===t||t.removeOverlay(e)})),n.__lines__=[]),e.forEach((e=>{var t,{color:r,width:i}=e,a=e.points.map((e=>new plus.maps.Point(e.longitude,e.latitude))),o=new plus.maps.Polyline(a);if(r){var s=_m(r);o.setStrokeColor(s.color),o.setStrokeOpacity(s.opacity)}i&&o.setLineWidth(i),null===(t=n)||void 0===t||t.addOverlay(o),n.__lines__.push(o)})))}function d(e){n&&(n.__circles__.length>0&&(n.__circles__.forEach((e=>{var t;null===(t=n)||void 0===t||t.removeOverlay(e)})),n.__circles__=[]),e.forEach((e=>{var t,{latitude:r,longitude:i,color:a,fillColor:o,radius:s,strokeWidth:l}=e,u=new plus.maps.Circle(new plus.maps.Point(i,r),s);if(a){var c=_m(a);u.setStrokeColor(c.color),u.setStrokeOpacity(c.opacity)}if(o){var d=_m(o);u.setFillColor(d.color),u.setFillOpacity(d.opacity)}l&&u.setLineWidth(l),null===(t=n)||void 0===t||t.addOverlay(u),n.__circles__.push(u)})))}function h(e){if(n){var t=n.__polygons__;t.forEach((e=>{var t;null===(t=n)||void 0===t||t.removeOverlay(e)})),t.length=0,e.forEach((e=>{var r,{points:i,strokeWidth:a,strokeColor:o,fillColor:s}=e,l=[];i&&i.forEach((e=>{l.push(new plus.maps.Point(e.longitude,e.latitude))}));var u=new plus.maps.Polygon(l);if(o){var c=_m(o);u.setStrokeColor(c.color),u.setStrokeOpacity(c.opacity)}if(s){var d=_m(s);u.setFillColor(d.color),u.setFillOpacity(d.opacity)}a&&u.setLineWidth(a),null===(r=n)||void 0===r||r.addOverlay(u),t.push(u)}))}}var f={moveToLocation:r,getCenterLocation:i,getRegion:a,getScale:o};return Hg(((e,t,n)=>{f[e]&&f[e](n,t)}),Yg(),!0),{_addMarkers:u,_addMapLines:c,_addMapCircles:d,_addMapPolygons:h,_setMap(e){n=e}}}(e,a);c((()=>{(n=hn(plus.maps.create(Eu()+"-map-"+(e.id||Date.now()),Object.assign({},s.value,l,(()=>{if(e.latitude&&e.longitude)return{center:new plus.maps.Point(Number(e.longitude),Number(e.latitude))}})())),{__markers__:[],__lines__:[],__circles__:[],__polygons__:[]})).setZoom(parseInt(String(e.scale))),plus.webview.currentWebview().append(n),u.value&&n.hide(),n.onclick=e=>{a("tap",{},e),a("click",{},e)},n.onstatuschanged=e=>{a("regionchange",{},{})},v(n),d(e.markers),h(e.polyline),f(e.circles),p(e.polygons),xo((()=>s.value),(e=>n&&n.setStyles(e)),{deep:!0}),xo((()=>l),(e=>n&&n.setStyles(e)),{deep:!0}),xo(u,(e=>{n&&n[e?"hide":"show"]()})),xo((()=>e.scale),(e=>{n&&n.setZoom(parseInt(String(e)))})),xo([()=>e.latitude,()=>e.longitude],(e=>{var[t,r]=e;n&&n.setStyles({center:new plus.maps.Point(Number(r),Number(t))})})),xo((()=>e.markers),(e=>{d(e,!0)}),{deep:!0}),xo((()=>e.polyline),(e=>{h(e)}),{deep:!0}),xo((()=>e.circles),(e=>{f(e)}),{deep:!0}),xo((()=>e.polygons),(e=>{p(e)}),{deep:!0})}));var g=dl((()=>e.controls.map((e=>{var t={position:"absolute"};return["top","left","width","height"].forEach((n=>{e.position[n]&&(t[n]=e.position[n]+"px")})),{id:e.id,iconPath:ac(e.iconPath),position:t,clickable:e.clickable}}))));return Fo((()=>{n&&(n.close(),v(null))})),()=>Ws("uni-map",{ref:i,id:e.id},[Ws("div",{ref:o,class:"uni-map-container"},null,512),g.value.map(((e,t)=>Ws(fm,{key:t,src:e.iconPath,style:e.position,"auto-size":!0,onClick:()=>e.clickable&&a("controltap",{},{controlId:e.id})},null,8,["src","style","auto-size","onClick"]))),Ws("div",{class:"uni-map-slot"},null)],8,["id"])}});var bm={SELECTOR:"selector",MULTISELECTOR:"multiSelector",TIME:"time",DATE:"date"},wm={YEAR:"year",MONTH:"month",DAY:"day"};function xm(e){return e>9?e:"0".concat(e)}function Sm(e,t){e=String(e||"");var n=new Date;if(t===bm.TIME){var r=e.split(":");2===r.length&&n.setHours(parseInt(r[0]),parseInt(r[1]))}else{var i=e.split("-");3===i.length&&n.setFullYear(parseInt(i[0]),parseInt(String(parseFloat(i[1])-1)),parseInt(i[2]))}return n}const km=Vf({name:"Picker",props:{name:{type:String,default:""},range:{type:Array,default:()=>[]},rangeKey:{type:String,default:""},value:{type:[Number,String,Array],default:0},mode:{type:String,default:bm.SELECTOR,validator:e=>Object.values(bm).indexOf(e)>=0},fields:{type:String,default:""},start:{type:String,default:function(e){if(e.mode===bm.TIME)return"00:00";if(e.mode===bm.DATE){var t=(new Date).getFullYear()-100;switch(e.fields){case wm.YEAR:return t;case wm.MONTH:return t+"-01";default:return t+"-01-01"}}return""}},end:{type:String,default:function(e){if(e.mode===bm.TIME)return"23:59";if(e.mode===bm.DATE){var t=(new Date).getFullYear()+100;switch(e.fields){case wm.YEAR:return t;case wm.MONTH:return t+"-12";default:return t+"-12-31"}}return""}},disabled:{type:[Boolean,String],default:!1}},emits:["change","cancel","columnchange"],setup(e,t){var{emit:n}=t;jr();var{t:r,getLocale:i}=$r(),a=Aa(null),o=Yf(a,n),s=Aa(null),l=Aa(null),u=__uniConfig.darkmode?plus.navigator.getUIStyle():"light";function c(e){u=e.theme}UniViewJSBridge.subscribe(Qn,c),Fo((()=>{UniViewJSBridge.unsubscribe(Qn,c)}));var d=()=>{var t=e.value;switch(e.mode){case bm.MULTISELECTOR:gn(t)||(t=[]),gn(s.value)||(s.value=[]);for(var n=s.value.length=Math.max(t.length,e.range.length),r=0;r<n;r++){var i=Number(t[r]),a=Number(s.value[r]),o=isNaN(i)?isNaN(a)?0:a:i;s.value.splice(r,1,o<0?0:o)}break;case bm.TIME:case bm.DATE:s.value=String(t);break;default:var l=Number(t);s.value=l<0?0:l}},h=e=>{l.value&&l.value.sendMessage(e)},f=(t,n)=>{t.mode!==bm.TIME&&t.mode!==bm.DATE||t.fields?(t.fields=Object.values(wm).includes(t.fields)?t.fields:wm.DAY,(e=>{var t={event:"cancel"};l.value=Vu({url:"__uniapppicker",data:hn({},e,{theme:u}),style:{titleNView:!1,animationType:"none",animationDuration:0,background:"rgba(0,0,0,0)",popGesture:"none"},onMessage:n=>{var r=n.event;if("created"!==r)return"columnchange"===r?(delete n.event,void o(r,{},n)):void(t=n);h(e)},onClose:()=>{l.value=null;var e=t.event;delete t.event,e&&o(e,{},t)}})})(t)):((t,n)=>{plus.nativeUI[e.mode===bm.TIME?"pickTime":"pickDate"]((t=>{var n=t.date;o("change",{},{value:e.mode===bm.TIME?"".concat(xm(n.getHours()),":").concat(xm(n.getMinutes())):"".concat(n.getFullYear(),"-").concat(xm(n.getMonth()+1),"-").concat(xm(n.getDate()))})}),(()=>{o("cancel",{},{})}),e.mode===bm.TIME?{time:Sm(e.value,bm.TIME),popover:n}:{date:Sm(e.value,bm.DATE),minDate:Sm(e.start,bm.DATE),maxDate:Sm(e.end,bm.DATE),popover:n})})(0,n)},p=t=>{if(!e.disabled){var n=t.currentTarget.getBoundingClientRect();f(Object.assign({},e,{value:s.value,locale:i(),messages:{done:r("uni.picker.done"),cancel:r("uni.picker.cancel")}}),{top:n.top+em(),left:n.left,width:n.width,height:n.height})}},v=yo(Xf,!1),g={submit:()=>[e.name,s.value],reset:()=>{switch(e.mode){case bm.SELECTOR:s.value=0;break;case bm.MULTISELECTOR:gn(e.value)&&(s.value=e.value.map((e=>0)));break;case bm.DATE:case bm.TIME:s.value=""}}};return v&&(v.addField(g),Fo((()=>v.removeField(g)))),Object.keys(e).forEach((t=>{"name"!==t&&xo((()=>e[t]),(e=>{var n={};n[t]=e,h(n)}),{deep:!0})})),xo((()=>e.value),d,{deep:!0}),d(),()=>Ws("uni-picker",{ref:a,onClick:p},[Ws("slot",null,null)],8,["onClick"])}});var Tm={id:{type:String,default:""},src:{type:String,default:""},duration:{type:[Number,String],default:""},controls:{type:[Boolean,String],default:!0},danmuList:{type:Array,default:()=>[]},danmuBtn:{type:[Boolean,String],default:!1},enableDanmu:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},loop:{type:[Boolean,String],default:!1},muted:{type:[Boolean,String],default:!1},objectFit:{type:String,default:"contain"},poster:{type:String,default:""},direction:{type:[String,Number],default:""},showProgress:{type:Boolean,default:!0},initialTime:{type:[String,Number],default:0},showFullscreenBtn:{type:[Boolean,String],default:!0},pageGesture:{type:[Boolean,String],default:!1},enableProgressGesture:{type:[Boolean,String],default:!0},vslideGesture:{type:[Boolean,String],default:!1},vslideGestureInFullscreen:{type:[Boolean,String],default:!1},showPlayBtn:{type:[Boolean,String],default:!0},showMuteBtn:{type:[Boolean,String],default:!1},enablePlayGesture:{type:[Boolean,String],default:!0},showCenterPlayBtn:{type:[Boolean,String],default:!0},showLoading:{type:[Boolean,String],default:!0},codec:{type:String,default:"hardware"},httpCache:{type:[Boolean,String],default:!1},playStrategy:{type:[Number,String],default:0},header:{type:Object,default:()=>({})},advanced:{type:Array,default:()=>[]},title:{type:String,default:""},isLive:{type:Boolean,default:!1}},Em=["play","pause","ended","timeupdate","fullscreenchange","fullscreenclick","waiting","error"],Cm=["play","pause","stop","seek","sendDanmu","playbackRate","requestFullScreen","exitFullScreen"];const Mm=Vf({name:"Video",props:Tm,emits:Em,setup(e,t){var n,{emit:r}=t,i=Aa(null),a=Yf(i,r),o=Aa(null),s=nm(e,["id"]),{position:l,hidden:u,onParentReady:c}=rm(o),d=Number(e.isLive?3:e.playStrategy);return c((()=>{n=plus.video.createVideoPlayer("video"+Date.now(),Object.assign({},s.value,l,{playStrategy:isNaN(d)?0:d})),plus.webview.currentWebview().append(n),u.value&&n.hide(),Em.forEach((e=>{n.addEventListener(e,(t=>{a(e,{},t.detail)}))})),xo((()=>s.value),(e=>n.setStyles(e)),{deep:!0}),xo((()=>l),(e=>n.setStyles(e)),{deep:!0}),xo((()=>u.value),(e=>{n[e?"hide":"show"](),e||n.setStyles(l)}))})),Hg(((e,t)=>{if(Cm.includes(e)){var r;switch(e){case"seek":r=t.position;break;case"sendDanmu":r=t;break;case"playbackRate":r=t.rate;break;case"requestFullScreen":r=t.direction}n&&n[e](r)}}),Yg(),!0),Fo((()=>{n&&n.close()})),()=>Ws("uni-video",{ref:i,id:e.id},[Ws("div",{ref:o,class:"uni-video-container"},null,512),Ws("div",{class:"uni-video-slot"},null)],8,["id"])}});var Om,Im={src:{type:String,default:""},updateTitle:{type:Boolean,default:!0},webviewStyles:{type:Object,default:()=>({})}};const Lm=Vf({name:"WebView",props:Im,setup(e){var t=Eu(),n=Aa(null),{hidden:r,onParentReady:i}=rm(n),a=dl((()=>e.webviewStyles));return i((()=>{var n;(e=>{var{htmlId:t,src:n,webviewStyles:r,props:i}=e,a=plus.webview.currentWebview(),o=hn({"uni-app":"none",isUniH5:!0,contentAdjust:!1},r),s=a.getTitleNView();if(s){var l=44+parseFloat(o.top||"0");plus.navigator.isImmersedStatusbar()&&(l+=Qg()),o.top=String(l),o.bottom=o.bottom||"0"}Om=plus.webview.create(n,t,o),s&&Om.addEventListener("titleUpdate",(function(){var e;if(i.updateTitle){var t=null===(e=Om)||void 0===e?void 0:e.getTitle();a.setStyle({titleNView:{titleText:t&&"null"!==t?t:" "}})}})),plus.webview.currentWebview().append(Om)})({htmlId:Aa("webviewId"+t).value,src:ac(e.src),webviewStyles:a.value,props:e}),UniViewJSBridge.publishHandler("webviewInserted",{},t),r.value&&(null===(n=Om)||void 0===n||n.hide())})),Fo((()=>{var e;plus.webview.currentWebview().remove(Om),null===(e=Om)||void 0===e||e.close("none"),Om=null,UniViewJSBridge.publishHandler("webviewRemoved",{},t)})),xo((()=>e.src),(t=>{var n,r=ac(t)||"";if(r){var i;if(/^(http|https):\/\//.test(r)&&e.webviewStyles.progress)null===(i=Om)||void 0===i||i.setStyle({progress:{color:e.webviewStyles.progress.color}});null===(n=Om)||void 0===n||n.loadURL(r)}})),xo(a,(e=>{var t;null===(t=Om)||void 0===t||t.setStyle(e)})),xo(r,(e=>{Om&&Om[e?"hide":"show"]()})),()=>Ws("uni-web-view",{ref:n},null,512)}});var Am={"#text":class extends df{constructor(e,t,n,r){super(e,"#text",t,document.createTextNode("")),this._text="",this.init(r),this.insert(t,n)}init(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this._text=e.t||"",t&&this.update()}setText(e){this._text=e,this.update(),this.updateView()}update(){var{space:e,decode:t}=this.$parent&&this.$parent.$props||{};this.$.textContent=Dg(this._text,{space:e,decode:t}).join(Yn)}},"#comment":class extends df{constructor(e,t,n){super(e,"#comment",t,document.createComment("")),this.insert(t,n)}},VIEW:class extends Kg{constructor(e,t,n,r){super(e,document.createElement("uni-view"),t,n,r)}},IMAGE:class extends am{constructor(e,t,n,r){super(e,"uni-image",sv,t,n,r)}},TEXT:class extends Xg{constructor(e,t,n,r){super(e,document.createElement("uni-text"),t,n,r,Zg),this._text=""}init(e){this._text=e.t||"",super.init(e)}setText(e){this._text=e,this.update(),this.updateView()}update(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],{$props:{space:t,decode:n}}=this;this.$.textContent=Dg(this._text,{space:t,decode:n}).join(Yn),super.update(e)}},NAVIGATOR:class extends am{constructor(e,t,n,r){super(e,"uni-navigator",og,t,n,r,"uni-navigator")}},FORM:class extends am{constructor(e,t,n,r){super(e,"uni-form",Zf,t,n,r,"span")}},BUTTON:class extends am{constructor(e,t,n,r){super(e,"uni-button",np,t,n,r)}},INPUT:class extends am{constructor(e,t,n,r){super(e,"uni-input",Tv,t,n,r)}init(e){super.init(e),um(this.$props)}},LABEL:class extends am{constructor(e,t,n,r){super(e,"uni-label",Jf,t,n,r)}},RADIO:class extends am{constructor(e,t,n,r){super(e,"uni-radio",xg,t,n,r,".uni-radio-wrapper")}setText(e){sm(this.$holder,"uni-radio-input",e)}},CHECKBOX:class extends am{constructor(e,t,n,r){super(e,"uni-checkbox",gp,t,n,r,".uni-checkbox-wrapper")}setText(e){sm(this.$holder,"uni-checkbox-input",e)}},"CHECKBOX-GROUP":class extends am{constructor(e,t,n,r){super(e,"uni-checkbox-group",vp,t,n,r)}},AD:class extends am{constructor(e,t,n,r){super(e,"uni-ad",im,t,n,r)}},CAMERA:class extends cm{constructor(e,t,n){super(e,"uni-camera",t,n)}},CANVAS:class extends am{constructor(e,t,n,r){super(e,"uni-canvas",fp,t,n,r,"uni-canvas > div")}},"COVER-IMAGE":class extends am{constructor(e,t,n,r){super(e,"uni-cover-image",fm,t,n,r)}},"COVER-VIEW":class extends om{constructor(e,t,n,r){super(e,"uni-cover-view",pm,t,n,r,".uni-cover-view")}},EDITOR:class extends am{constructor(e,t,n,r){super(e,"uni-editor",Qp,t,n,r)}},"FUNCTIONAL-PAGE-NAVIGATOR":class extends cm{constructor(e,t,n){super(e,"uni-functional-page-navigator",t,n)}},ICON:class extends am{constructor(e,t,n,r){super(e,"uni-icon",rv,t,n,r)}},"RADIO-GROUP":class extends am{constructor(e,t,n,r){super(e,"uni-radio-group",wg,t,n,r)}},"LIVE-PLAYER":class extends cm{constructor(e,t,n){super(e,"uni-live-player",t,n)}},"LIVE-PUSHER":class extends am{constructor(e,t,n,r){super(e,"uni-live-pusher",mm,t,n,r,".uni-live-pusher-slot")}},MAP:class extends am{constructor(e,t,n,r){super(e,"uni-map",ym,t,n,r,".uni-map-slot")}},"MOVABLE-AREA":class extends om{constructor(e,t,n,r){super(e,"uni-movable-area",Pv,t,n,r)}},"MOVABLE-VIEW":class extends am{constructor(e,t,n,r){super(e,"uni-movable-view",Zv,t,n,r)}},"OFFICIAL-ACCOUNT":class extends cm{constructor(e,t,n){super(e,"uni-official-account",t,n)}},"OPEN-DATA":class extends cm{constructor(e,t,n){super(e,"uni-open-data",t,n)}},PICKER:class extends am{constructor(e,t,n,r){super(e,"uni-picker",km,t,n,r)}},"PICKER-VIEW":class extends om{constructor(e,t,n,r){super(e,"uni-picker-view",sg,t,n,r,".uni-picker-view-wrapper")}},"PICKER-VIEW-COLUMN":class extends om{constructor(e,t,n,r){super(e,"uni-picker-view-column",vg,t,n,r,".uni-picker-view-content")}},PROGRESS:class extends am{constructor(e,t,n,r){super(e,"uni-progress",_g,t,n,r)}},"RICH-TEXT":class extends am{constructor(e,t,n,r){super(e,"uni-rich-text",Cg,t,n,r)}},"SCROLL-VIEW":class extends am{constructor(e,t,n,r){super(e,"uni-scroll-view",Og,t,n,r,".uni-scroll-view-content")}setText(e){sm(this.$holder,"uni-scroll-view-refresher",e)}},SLIDER:class extends am{constructor(e,t,n,r){super(e,"uni-slider",Ig,t,n,r)}},SWIPER:class extends om{constructor(e,t,n,r){super(e,"uni-swiper",Bg,t,n,r,".uni-swiper-slide-frame")}},"SWIPER-ITEM":class extends am{constructor(e,t,n,r){super(e,"uni-swiper-item",Ng,t,n,r)}},SWITCH:class extends am{constructor(e,t,n,r){super(e,"uni-switch",Rg,t,n,r)}},TEXTAREA:class extends am{constructor(e,t,n,r){super(e,"uni-textarea",Vg,t,n,r)}init(e){super.init(e),um(this.$props)}},VIDEO:class extends am{constructor(e,t,n,r){super(e,"uni-video",Mm,t,n,r,".uni-video-slot")}},"WEB-VIEW":class extends am{constructor(e,t,n,r){super(e,"uni-web-view",Lm,t,n,r)}}};var Bm=new Map;function Nm(e){return Bm.get(e)}function Rm(e){return Bm.delete(e)}function Pm(e,t,n,r){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};if(0===e)i=new df(e,t,n,document.createElement(t));else{var o=Am[t];i=o?new o(e,n,r,a):new Df(e,document.createElement(t),n,r,a)}return Bm.set(e,i),i}var Dm=[],zm=!1;function Fm(e){if(zm)return e();Dm.push(e)}function $m(){zm=!0,Dm.forEach((e=>{try{e()}catch(t){console.error(t)}})),Dm.length=0}function Vm(e){var{css:t,route:n,platform:r,pixelRatio:i,windowWidth:a,disableScroll:o,statusbarHeight:s,windowTop:l,windowBottom:u}=e;!function(e){window.__PAGE_INFO__={route:e}}(n),function(e,t,n){window.__SYSTEM_INFO__={platform:e,pixelRatio:t,windowWidth:n}}(r,i,a),Pm(0,"div",-1,-1).$=document.getElementById("app");var c=plus.webview.currentWebview().id;window.__id__=c,document.title="".concat(n,"[").concat(c,"]"),function(e,t,n){!function(e){var t=document.documentElement.style;Object.keys(e).forEach((n=>{t.setProperty(n,e[n])}))}({"--window-left":"0px","--window-right":"0px","--window-top":t+"px","--window-bottom":n+"px","--status-bar-height":e+"px"})}(s,l,u),o&&document.addEventListener("touchmove",Cu),t?function(e){var t=document.createElement("link");t.type="text/css",t.rel="stylesheet",t.href=e+".css",t.onload=$m,t.onerror=$m,document.head.appendChild(t)}(n):$m()}var jm=!1;function Wm(e,t){var{scrollTop:n,selector:r,duration:i}=e;!function(e,t,n){if(wn(e)){var r=document.querySelector(e);if(r){var{height:i,top:a}=r.getBoundingClientRect();e=a+window.pageYOffset,n&&(e-=i)}}e<0&&(e=0);var o=document.documentElement,{clientHeight:s,scrollHeight:l}=o;if(e=Math.min(e,l-s),0!==t){if(window.scrollY!==e){var u=t=>{if(t<=0)window.scrollTo(0,e);else{var n=e-window.scrollY;requestAnimationFrame((function(){window.scrollTo(0,window.scrollY+n/t*10),u(t-10)}))}};u(t)}}else o.scrollTop=document.body.scrollTop=e}(r||n||0,i),t()}function Um(e){var t=e[0];1===t[0]?Vm(t[1]):Fm((()=>function(e){var t=e[0],n=function(e){if(!e.length)return e=>e;var t=function(n){var r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if("number"==typeof n)return e[n];var i={};return n.forEach((e=>{var[n,a]=e;i[t(n)]=r?t(a):a})),i};return t}(0===t[0]?t[1]:[]);e.forEach((e=>{switch(e[0]){case 1:return Vm(e[1]);case 2:return;case 3:var t=e[3];return Pm(e[1],n(e[2]),-1===t?0:t,e[4],Zh(n,e[5]));case 4:return Nm(e[1]).insert(e[2],e[3],Zh(n,e[4]));case 5:return Nm(e[1]).remove();case 6:return Nm(e[1]).setAttr(n(e[2]),n(e[3]));case 7:return Nm(e[1]).removeAttr(n(e[2]));case 8:return Nm(e[1]).addEvent(n(e[2]),e[3]);case 12:return Nm(e[1]).addWxsEvent(n(e[2]),n(e[3]),e[4]);case 9:return Nm(e[1]).removeEvent(n(e[2]));case 10:return Nm(e[1]).setText(n(e[2]));case 15:return function(e){if(!jm){jm=!0;var t={onReachBottomDistance:e,onPageScroll(e){UniViewJSBridge.publishHandler("onPageScroll",{scrollTop:e})},onReachBottom(){UniViewJSBridge.publishHandler("onReachBottom")}};requestAnimationFrame((()=>document.addEventListener("scroll",Nu(t))))}}(e[1])}})),function(){try{[...Gh].sort(((e,t)=>e.priority-t.priority)).forEach((e=>e()))}finally{Gh.clear()}}()}(e)))}function Hm(){UniViewJSBridge.publishHandler(ec)}function qm(e){return window.__$__(e).$}function Ym(e,t){var n={},{top:r,topWindowHeight:i}=function(){var e=document.documentElement.style,t=mu(),n=gu(e,"--window-bottom"),r=gu(e,"--window-left"),i=gu(e,"--window-right"),a=gu(e,"--top-window-height");return{top:t,bottom:n?n+pu.bottom:0,left:r?r+pu.left:0,right:i?i+pu.right:0,topWindowHeight:a||0}}();if(t.id&&(n.id=e.id),t.dataset&&(n.dataset=sr(e)),t.rect||t.size){var a=e.getBoundingClientRect();t.rect&&(n.left=a.left,n.right=a.right,n.top=a.top-r-i,n.bottom=a.bottom-r-i),t.size&&(n.width=a.width,n.height=a.height)}if(gn(t.properties)&&t.properties.forEach((e=>{e=e.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}))})),t.scrollOffset)if("UNI-SCROLL-VIEW"===e.tagName){var o=e.children[0].children[0];n.scrollLeft=o.scrollLeft,n.scrollTop=o.scrollTop,n.scrollHeight=o.scrollHeight,n.scrollWidth=o.scrollWidth}else n.scrollLeft=0,n.scrollTop=0,n.scrollHeight=0,n.scrollWidth=0;if(gn(t.computedStyle)){var s=getComputedStyle(e);t.computedStyle.forEach((e=>{n[e]=s[e]}))}return t.context&&(n.contextInfo=function(e){return e.__uniContextInfo}(e)),n}function Xm(e,t){return(e.matches||e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector||function(e){for(var t=this.parentElement.querySelectorAll(e),n=t.length;--n>=0&&t.item(n)!==this;);return n>-1}).call(e,t)}function Zm(e,t,n,r,i){var a=function(e,t){return e?window.__$__(e).$:t.$el}(t,e),o=a.parentElement;if(!o)return r?null:[];var{nodeType:s}=a,l=3===s||8===s;if(r){var u=l?o.querySelector(n):Xm(a,n)?a:a.querySelector(n);return u?Ym(u,i):null}var c=[],d=(l?o:a).querySelectorAll(n);return d&&d.length&&[].forEach.call(d,(e=>{c.push(Ym(e,i))})),!l&&Xm(a,n)&&c.unshift(Ym(a,i)),c}function Gm(e,t,n){var r=[];t.forEach((t=>{var{component:n,selector:i,single:a,fields:o}=t;null===n?r.push(function(e){var t={};if(e.id&&(t.id=""),e.dataset&&(t.dataset={}),e.rect&&(t.left=0,t.right=0,t.top=0,t.bottom=0),e.size&&(t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight),e.scrollOffset){var n=document.documentElement,r=document.body;t.scrollLeft=n.scrollLeft||r.scrollLeft||0,t.scrollTop=n.scrollTop||r.scrollTop||0,t.scrollHeight=n.scrollHeight||r.scrollHeight||0,t.scrollWidth=n.scrollWidth||r.scrollWidth||0}return t}(o)):r.push(Zm(e,n,i,a,o))})),n(r)}function Km(e,t){var{reqId:n,component:r,options:i,callback:a}=e,o=qm(r);(o.__io||(o.__io={}))[n]=function(e,t,n){Hh();var r=t.relativeToSelector?e.querySelector(t.relativeToSelector):null,i=new IntersectionObserver((e=>{e.forEach((e=>{n({intersectionRatio:Yh(e),intersectionRect:qh(e.intersectionRect),boundingClientRect:qh(e.boundingClientRect),relativeRect:qh(e.rootBounds),time:Date.now(),dataset:sr(e.target),id:e.target.id})}))}),{root:r,rootMargin:t.rootMargin,threshold:t.thresholds});if(t.observeAll){i.USE_MUTATION_OBSERVER=!0;for(var a=e.querySelectorAll(t.selector),o=0;o<a.length;o++)i.observe(a[o])}else{i.USE_MUTATION_OBSERVER=!1;var s=e.querySelector(t.selector);s?i.observe(s):console.warn("Node ".concat(t.selector," is not found. Intersection observer will not trigger."))}return i}(o,i,a)}var Jm={},Qm={};function e_(e){return e.replace(/([A-Z])/g,"-$1").toLowerCase()}function t_(e,t){var{reqId:n,component:r,options:i,callback:a}=e,o=Jm[n]=window.matchMedia(function(e){var t=[];for(var n of["width","minWidth","maxWidth","height","minHeight","maxHeight","orientation"])"orientation"!==n&&e[n]&&Number(e[n]>=0)&&t.push("(".concat(e_(n),": ").concat(Number(e[n]),"px)")),"orientation"===n&&e[n]&&t.push("(".concat(e_(n),": ").concat(e[n],")"));return t.join(" and ")}(i)),s=Qm[n]=e=>a(e.matches);s(o),o.addListener(s)}function n_(e,t){var{family:n,source:r,desc:i}=e;(function(e,t,n){var r=document.fonts;if(r){var i=new FontFace(e,t,n);return i.load().then((()=>{r.add&&r.add(i)}))}return new Promise((r=>{var i=document.createElement("style"),a=[];if(n){var{style:o,weight:s,stretch:l,unicodeRange:u,variant:c,featureSettings:d}=n;o&&a.push("font-style:".concat(o)),s&&a.push("font-weight:".concat(s)),l&&a.push("font-stretch:".concat(l)),u&&a.push("unicode-range:".concat(u)),c&&a.push("font-variant:".concat(c)),d&&a.push("font-feature-settings:".concat(d))}i.innerText='@font-face{font-family:"'.concat(e,'";src:').concat(t,";").concat(a.join(";"),"}"),document.head.appendChild(i),r()}))})(n,r,i).then((()=>{t()})).catch((e=>{t(e.toString())}))}var r_={$el:document.body};function i_(){var e=Eu();!function(e,t){UniViewJSBridge.subscribe(Zr(e,Hr),t?t(Kr):Kr)}(e,(e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];Fm((()=>{e.apply(null,n)}))})),Gr(e,"requestComponentInfo",((e,t)=>{Gm(r_,e.reqs,t)})),Gr(e,"addIntersectionObserver",(e=>{Km(hn({},e,{callback(t){UniViewJSBridge.publishHandler(e.eventName,t)}}))})),Gr(e,"removeIntersectionObserver",(e=>{!function(e,t){var{reqId:n,component:r}=e,i=qm(r),a=i.__io&&i.__io[n];a&&(a.disconnect(),delete i.__io[n])}(e)})),Gr(e,"addMediaQueryObserver",(e=>{t_(hn({},e,{callback(t){UniViewJSBridge.publishHandler(e.eventName,t)}}))})),Gr(e,"removeMediaQueryObserver",(e=>{!function(e,t){var{reqId:n,component:r}=e,i=Qm[n],a=Jm[n];a&&(a.removeListener(i),delete Qm[n],delete Jm[n])}(e)})),Gr(e,"pageScrollTo",Wm),Gr(e,"loadFontFace",n_),Gr(e,"setPageMeta",(e=>{!function(e,t){var{pageStyle:n,rootFontSize:r}=t;n&&(document.querySelector("uni-page-body")||document.body).setAttribute("style",n),r&&document.documentElement.style.fontSize!==r&&(document.documentElement.style.fontSize=r)}(0,e)}))}function a_(){ui(),i_(),function(){var{subscribe:e}=UniViewJSBridge;e(Ju,Um),e("setLocale",(e=>$r().setLocale(e))),e(ec,Hm)}(),function(){if(0===String(navigator.vendor).indexOf("Apple")){var e,t=null;document.documentElement.addEventListener("click",(n=>{clearTimeout(e),t&&Math.abs(n.pageX-t.pageX)<=44&&Math.abs(n.pageY-t.pageY)<=44&&n.timeStamp-t.timeStamp<=450&&n.preventDefault(),t=n,e=setTimeout((()=>{t=null}),450)}))}}(),tc.publishHandler(ec)}window.uni=Xh,window.UniViewJSBridge=tc,window.rpx2px=Dh,window.normalizeStyleName=Ef,window.normalizeStyleValue=ff,window.__$__=Nm,window.__f__=function(e,t){for(var n=arguments.length,r=new Array(n>2?n-2:0),i=2;i<n;i++)r[i-2]=arguments[i];uni.__log__?uni.__log__(e,t,...r):console[e].apply(console,[...r,t])},"undefined"!=typeof plus?a_():document.addEventListener("plusready",a_)}));
