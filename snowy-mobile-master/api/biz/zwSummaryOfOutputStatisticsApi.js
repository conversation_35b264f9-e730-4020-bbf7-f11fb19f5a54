import request from '@/utils/request'

// 获取汇总分页
export function zwSummaryOfOutputStatisticsPage(data) {
    return request({
        url: '/biz/summaryOfOutputStatistics/page',
        method: 'get',
        data: data
    })
}
// 提交汇总表单 add为false时为编辑，默认为新增
export function zwSummaryOfOutputStatisticsSubmitForm(data, add = true) {
    return request({
        url: '/biz/summaryOfOutputStatistics/'+ (add ? 'add' : 'edit'),
        method: 'post',
        data: data
    })
}
// 删除汇总
export function zwSummaryOfOutputStatisticsDelete(data) {
    return request({
        url: '/biz/summaryOfOutputStatistics/delete',
        method: 'post',
        data: data
    })
}
//  获取汇总详情
export function zwSummaryOfOutputStatisticsDetail(data) {
    return request({
        url: '/biz/summaryOfOutputStatistics/detail',
        method: 'get',
        data: data
    })
}
