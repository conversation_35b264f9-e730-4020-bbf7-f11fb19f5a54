import request from '@/utils/request'

/**
 * MES事务记录Api接口管理器
 *
 * <AUTHOR>
 * @date 2025/07/24
 **/

// 获取事务记录分页
export function transactionPage(data) {
	return request({
		url: '/choiceway/transaction/page',
		method: 'get',
		data: data
	})
}

// 根据批次ID获取事务历史
export function getHistoryByBatch(batchId) {
	return request({
		url: '/choiceway/transaction/historyByBatch',
		method: 'get',
		data: { batchId }
	})
}

// 根据工单ID获取事务历史
export function getHistoryByWorkOrder(workOrderId) {
	return request({
		url: '/choiceway/transaction/historyByWorkOrder',
		method: 'get',
		data: { workOrderId }
	})
}

// 根据批次LOT ID获取事务历史
export function getHistoryByLotId(lotId) {
	return request({
		url: '/choiceway/transaction/historyByLotId',
		method: 'get',
		data: { lotId }
	})
}

// 获取事务详情
export function transactionDetail(data) {
	return request({
		url: '/choiceway/transaction/detail',
		method: 'get',
		data: data
	})
}
