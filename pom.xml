<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>vip.xiaonuo</groupId>
    <artifactId>snowy</artifactId>
    <name>snowy</name>
    <version>3.0.0</version>
    <description>snowy快速开发平台</description>
    <packaging>pom</packaging>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.1</version>
    </parent>

    <properties>
        <java.version>17</java.version>
        <snowy.version>3.0.0</snowy.version>
        <spring-boot.version>3.2.1</spring-boot.version>
        <spring-framework.version>6.1.2</spring-framework.version>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <modules>
        <!-- 基础通用规则模块 -->
        <module>snowy-common</module>

        <!-- 插件模块 -->
        <module>snowy-plugin</module>

        <!-- 插件API接口模块 -->
        <module>snowy-plugin-api</module>

        <!-- 主启动模块 -->
        <module>snowy-web-app</module>
    </modules>

    <dependencyManagement>
        <dependencies>

            <!-- snowy-common -->
            <dependency>
                <groupId>vip.xiaonuo</groupId>
                <artifactId>snowy-common</artifactId>
                <version>${snowy.version}</version>
            </dependency>

            <!-- snowy-plugin-auth-api -->
            <dependency>
                <groupId>vip.xiaonuo</groupId>
                <artifactId>snowy-plugin-auth-api</artifactId>
                <version>${snowy.version}</version>
            </dependency>

            <!-- snowy-plugin-biz-api -->
            <dependency>
                <groupId>vip.xiaonuo</groupId>
                <artifactId>snowy-plugin-biz-api</artifactId>
                <version>${snowy.version}</version>
            </dependency>

            <!-- snowy-plugin-client-api -->
            <dependency>
                <groupId>vip.xiaonuo</groupId>
                <artifactId>snowy-plugin-client-api</artifactId>
                <version>${snowy.version}</version>
            </dependency>

            <!-- snowy-plugin-dev-api -->
            <dependency>
                <groupId>vip.xiaonuo</groupId>
                <artifactId>snowy-plugin-dev-api</artifactId>
                <version>${snowy.version}</version>
            </dependency>

            <!-- snowy-plugin-gen-api -->
            <dependency>
                <groupId>vip.xiaonuo</groupId>
                <artifactId>snowy-plugin-gen-api</artifactId>
                <version>${snowy.version}</version>
            </dependency>

            <!-- snowy-plugin-mobile-api -->
            <dependency>
                <groupId>vip.xiaonuo</groupId>
                <artifactId>snowy-plugin-mobile-api</artifactId>
                <version>${snowy.version}</version>
            </dependency>

            <!-- snowy-plugin-sys-api -->
            <dependency>
                <groupId>vip.xiaonuo</groupId>
                <artifactId>snowy-plugin-sys-api</artifactId>
                <version>${snowy.version}</version>
            </dependency>

            <!-- snowy-plugin-choiceway-api -->
            <dependency>
                <groupId>vip.xiaonuo</groupId>
                <artifactId>snowy-plugin-choiceway-api</artifactId>
                <version>${snowy.version}</version>
            </dependency>

            <!-- snowy-plugin-auth -->
            <dependency>
                <groupId>vip.xiaonuo</groupId>
                <artifactId>snowy-plugin-auth</artifactId>
                <version>${snowy.version}</version>
            </dependency>

            <!-- snowy-plugin-biz -->
            <dependency>
                <groupId>vip.xiaonuo</groupId>
                <artifactId>snowy-plugin-biz</artifactId>
                <version>${snowy.version}</version>
            </dependency>

            <!-- snowy-plugin-client -->
            <dependency>
                <groupId>vip.xiaonuo</groupId>
                <artifactId>snowy-plugin-client</artifactId>
                <version>${snowy.version}</version>
            </dependency>

            <!-- snowy-plugin-dev -->
            <dependency>
                <groupId>vip.xiaonuo</groupId>
                <artifactId>snowy-plugin-dev</artifactId>
                <version>${snowy.version}</version>
            </dependency>

            <!-- snowy-plugin-gen -->
            <dependency>
                <groupId>vip.xiaonuo</groupId>
                <artifactId>snowy-plugin-gen</artifactId>
                <version>${snowy.version}</version>
            </dependency>

            <!-- snowy-plugin-mobile -->
            <dependency>
                <groupId>vip.xiaonuo</groupId>
                <artifactId>snowy-plugin-mobile</artifactId>
                <version>${snowy.version}</version>
            </dependency>

            <!-- snowy-plugin-sys -->
            <dependency>
                <groupId>vip.xiaonuo</groupId>
                <artifactId>snowy-plugin-sys</artifactId>
                <version>${snowy.version}</version>
            </dependency>

            <!-- snowy-plugin-choiceway -->
            <dependency>
                <groupId>vip.xiaonuo</groupId>
                <artifactId>snowy-plugin-choiceway</artifactId>
                <version>${snowy.version}</version>
            </dependency>

            <!-- nashorn-core -->
            <dependency>
                <groupId>org.openjdk.nashorn</groupId>
                <artifactId>nashorn-core</artifactId>
                <version>15.4</version>
            </dependency>

            <!-- lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.30</version>
            </dependency>

            <!-- druid -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>1.2.21</version>
            </dependency>

            <!-- mybatis-plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>3.5.5</version>
            </dependency>

            <!-- easy-trans -->
            <dependency>
                <groupId>com.fhs-opensource</groupId>
                <artifactId>easy-trans-spring-boot-starter</artifactId>
                <version>3.0.0</version>
            </dependency>

            <!-- easy-trans-mybatis-plus-extend -->
            <dependency>
                <groupId>com.fhs-opensource</groupId>
                <artifactId>easy-trans-mybatis-plus-extend</artifactId>
                <version>3.0.0</version>
            </dependency>

            <!-- redis -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>2.12.0</version>
            </dependency>

            <!-- hutool -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.25</version>
            </dependency>

            <!-- pinyin4j -->
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>2.5.1</version>
            </dependency>

            <!-- ip2region -->
            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>2.7.0</version>
            </dependency>

            <!-- knife4j -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>4.5.0</version>
            </dependency>

            <!-- easy-poi -->
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-spring-boot-starter</artifactId>
                <version>4.4.0</version>
            </dependency>

            <!-- sm-crypto -->
            <dependency>
                <groupId>com.antherd</groupId>
                <artifactId>sm-crypto</artifactId>
                <version>0.3.2</version>
            </dependency>

            <!-- easyexcel -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.3.3</version>
            </dependency>

            <!-- Sa-token-core -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-core</artifactId>
                <version>1.37.0</version>
            </dependency>

            <!-- Sa-token -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot3-starter</artifactId>
                <version>1.37.0</version>
            </dependency>

            <!-- Sa-token 整合 redis （使用jackson序列化方式） -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redis-jackson</artifactId>
                <version>1.37.0</version>
            </dependency>

            <!-- Sa-Token插件：权限缓存与业务缓存分离 -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-alone-redis</artifactId>
                <version>1.37.0</version>
            </dependency>

            <!-- Sa-Token 插件：整合SSO -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-sso</artifactId>
                <version>1.37.0</version>
            </dependency>

            <!-- JustAuth 第三方登录 -->
            <dependency>
                <groupId>me.zhyd.oauth</groupId>
                <artifactId>JustAuth</artifactId>
                <version>1.16.6</version>
            </dependency>

            <!-- beetl模板引擎 -->
            <dependency>
                <groupId>com.ibeetl</groupId>
                <artifactId>beetl-framework-starter</artifactId>
                <version>1.2.40.Beetl.RELEASE</version>
            </dependency>

            <!--x-file-storage文件sdk-->
            <dependency>
                <groupId>org.dromara.x-file-storage</groupId>
                <artifactId>x-file-storage-spring</artifactId>
                <version>2.1.0</version>
            </dependency>

            <!--腾讯云文件sdk-->
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>5.6.199</version>
            </dependency>

            <!--阿里云文件sdk-->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.15.1</version>
            </dependency>

            <!--minio文件sdk-->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>8.5.2</version>
            </dependency>

            <!--java邮件sdk-->
            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>javax.mail</artifactId>
                <version>1.5.4</version>
            </dependency>

            <!--阿里云邮件sdk-->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dm20151123</artifactId>
                <version>1.0.6</version>
            </dependency>

            <!-- 腾讯云邮件sdk -->
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java-ses</artifactId>
                <version>3.1.944</version>
            </dependency>

            <!--阿里云短信sdk-->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dysmsapi20170525</artifactId>
                <version>2.0.24</version>
            </dependency>

            <!--腾讯云短信sdk-->
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java-sms</artifactId>
                <version>3.1.893</version>
            </dependency>

            <!-- sms4j短信sdk -->
            <dependency>
                <groupId>org.dromara.sms4j</groupId>
                <artifactId>sms4j-javase-plugin</artifactId>
                <version>3.1.1</version>
            </dependency>

            <!--系统硬件信息-->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>6.4.11</version>
            </dependency>

            <!-- dynamic-datasource -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>4.3.0</version>
            </dependency>

            <!-- mysql -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>8.3.0</version>
            </dependency>

            <!-- postgresql -->
            <!--<dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>42.7.1</version>
            </dependency>-->

            <!-- 达梦数据库 -->
            <!--<dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>8.1.3.62</version>
            </dependency>-->

            <!-- 人大金仓数据库 -->
            <!--<dependency>
                <groupId>cn.com.kingbase</groupId>
                <artifactId>kingbase8</artifactId>
                <version>8.6.0</version>
            </dependency>-->

            <!-- oracle -->
            <!--<dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc10</artifactId>
                <version>19.21.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.nls</groupId>
                <artifactId>orai18n</artifactId>
                <version>23.3.0.23.09</version>
            </dependency>-->

            <!-- mssql -->
            <!--<dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>12.4.2.jre11</version>
            </dependency>-->
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.12.1</version>
                <configuration>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>
</project>
