
package vip.xiaonuo.biz.modular.updates.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * 更新记录表Id参数
 *
 * <AUTHOR>
 * @date  2024/06/25 08:04
 **/
@Getter
@Setter
public class UpdatesIdParam {

    /** 唯一主键 */
    @Schema(description = "唯一主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "id不能为空")
    private String id;
}
