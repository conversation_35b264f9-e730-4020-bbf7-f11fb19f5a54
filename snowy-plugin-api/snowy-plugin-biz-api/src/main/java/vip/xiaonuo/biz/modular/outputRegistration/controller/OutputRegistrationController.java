
package vip.xiaonuo.biz.modular.outputRegistration.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.biz.modular.conversionCoefficient.entity.ConversionCoefficient;
import vip.xiaonuo.biz.modular.conversionCoefficient.param.ConversionCoefficientIdParam;
import vip.xiaonuo.biz.modular.conversionCoefficient.param.ConversionCoefficientPageParam;
import vip.xiaonuo.biz.modular.conversionCoefficient.service.ConversionCoefficientService;
import vip.xiaonuo.biz.modular.summaryOfOutputStatistics.service.ZwSummaryOfOutputStatisticsService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.biz.modular.outputRegistration.entity.OutputRegistration;
import vip.xiaonuo.biz.modular.outputRegistration.param.OutputRegistrationAddParam;
import vip.xiaonuo.biz.modular.outputRegistration.param.OutputRegistrationEditParam;
import vip.xiaonuo.biz.modular.outputRegistration.param.OutputRegistrationIdParam;
import vip.xiaonuo.biz.modular.outputRegistration.param.OutputRegistrationPageParam;
import vip.xiaonuo.biz.modular.outputRegistration.service.OutputRegistrationService;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 产出登记控制器
 *
 * <AUTHOR>
 * @date  2024/06/17 15:15
 */
@Tag(name = "产出登记控制器")
@RestController
@Validated
public class OutputRegistrationController {

    @Resource
    private OutputRegistrationService outputRegistrationService;

    @Resource
    private ConversionCoefficientService conversionCoefficientService;

    @Resource
    private ZwSummaryOfOutputStatisticsService summaryOfOutpService;

    /**
     * 获取产出登记分页
     *
     * <AUTHOR>
     * @date  2024/06/17 15:15
     */
    @Operation(summary = "获取产出登记分页")
    @SaCheckPermission("/biz/outputRegistration/page")
    @GetMapping("/biz/outputRegistration/page")
    public CommonResult<Page<OutputRegistration>> page(OutputRegistrationPageParam outputRegistrationPageParam) {
        return CommonResult.data(outputRegistrationService.page(outputRegistrationPageParam));
    }

    /**
     * 添加产出登记
     *
     * <AUTHOR>
     * @date  2024/06/17 15:15
     */
    @Operation(summary = "添加产出登记")
    @CommonLog("添加产出登记")
    @SaCheckPermission("/biz/outputRegistration/add")
    @PostMapping("/biz/outputRegistration/add")
    public CommonResult<String> add(@RequestBody @Valid OutputRegistrationAddParam outputRegistrationAddParam) {
        //计算产出分布
        outputRegistrationAddParam.calculateEquivalentProductionCapacity(conversionCoefficientService.obtainAllCompositeCoefficients());
        outputRegistrationService.add(outputRegistrationAddParam);

        // 计算总产量
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");

        // 格式化日期
        String formattedDate = sdf.format(outputRegistrationAddParam.getDate());
        summaryOfOutpService.CalculateTheSpecifiedJobNumberProductionCapacity(outputRegistrationAddParam.getName(),formattedDate);
        return CommonResult.ok();
    }

    /**
     * 编辑产出登记
     *
     * <AUTHOR>
     * @date  2024/06/17 15:15
     */
    @Operation(summary = "编辑产出登记")
    @CommonLog("编辑产出登记")
    @SaCheckPermission("/biz/outputRegistration/edit")
    @PostMapping("/biz/outputRegistration/edit")
    public CommonResult<String> edit(@RequestBody @Valid OutputRegistrationEditParam outputRegistrationEditParam) {
        outputRegistrationService.edit(outputRegistrationEditParam);
        // 计算总产量
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");

        // 格式化日期
        String formattedDate = sdf.format(outputRegistrationEditParam.getDate());
        summaryOfOutpService.CalculateTheSpecifiedJobNumberProductionCapacity(outputRegistrationEditParam.getName(),formattedDate);
        return CommonResult.ok();
    }

    /**
     * 删除产出登记
     *
     * <AUTHOR>
     * @date  2024/06/17 15:15
     */
    @Operation(summary = "删除产出登记")
    @CommonLog("删除产出登记")
    @SaCheckPermission("/biz/outputRegistration/delete")
    @PostMapping("/biz/outputRegistration/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                               List<OutputRegistrationIdParam> outputRegistrationIdParamList) {
        outputRegistrationService.delete(outputRegistrationIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取产出登记详情
     *
     * <AUTHOR>
     * @date  2024/06/17 15:15
     */
    @Operation(summary = "获取产出登记详情")
    @SaCheckPermission("/biz/outputRegistration/detail")
    @GetMapping("/biz/outputRegistration/detail")
    public CommonResult<OutputRegistration> detail(@Valid OutputRegistrationIdParam outputRegistrationIdParam) {
        return CommonResult.data(outputRegistrationService.detail(outputRegistrationIdParam));
    }


    /**
     * 导出选中的数据
     *
     * <AUTHOR>
     * @date  2024/06/17 15:44
     */
    @Operation(summary = "导出选中的数据")
    @SaCheckPermission("/biz/outputRegistration/exportData")
    @PostMapping(value = "/biz/outputRegistration/exportData", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void conversionCoefficientExport(@RequestBody @Valid @NotEmpty(message = "集合不能为空") List<OutputRegistrationIdParam> outputRegistrationIdParamList, HttpServletResponse response) throws IOException {
        outputRegistrationService.exportFoldNumber(outputRegistrationIdParamList,response);
    }

    @Operation(summary = "获取当月的产能排行榜")
    @GetMapping("/biz/outputRegistration/getRankingList")
    public  String getRankingData(String jobId){
        //获取当前的年月
        Date date = new Date();
        // 格式化data，只取年和月
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String currentMonth = sdf.format(date);
        return outputRegistrationService.getRankingData(currentMonth, jobId);
    }
}
