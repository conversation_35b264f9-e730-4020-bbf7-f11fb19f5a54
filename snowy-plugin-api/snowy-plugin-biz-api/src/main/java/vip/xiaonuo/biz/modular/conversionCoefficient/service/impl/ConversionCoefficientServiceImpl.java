
package vip.xiaonuo.biz.modular.conversionCoefficient.service.impl;

import cn.afterturn.easypoi.cache.manager.POICacheManager;
import cn.afterturn.easypoi.entity.ImageEntity;
import cn.afterturn.easypoi.word.WordExportUtil;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.AbstractRowHeightStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fhs.trans.service.impl.TransService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.biz.modular.conversionCoefficient.param.*;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.excel.CommonExcelCustomMergeStrategy;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.conversionCoefficient.entity.ConversionCoefficient;
import vip.xiaonuo.biz.modular.conversionCoefficient.mapper.ConversionCoefficientMapper;
import vip.xiaonuo.biz.modular.conversionCoefficient.service.ConversionCoefficientService;
import vip.xiaonuo.common.util.CommonAvatarUtil;
import vip.xiaonuo.common.util.CommonDownloadUtil;
import vip.xiaonuo.common.util.CommonResponseUtil;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 折合系数Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/06/17 15:44
 **/
@Service
public class ConversionCoefficientServiceImpl extends ServiceImpl<ConversionCoefficientMapper, ConversionCoefficient> implements ConversionCoefficientService {


    @Resource
    private TransService transService;

    @Override
    public Page<ConversionCoefficient> page(ConversionCoefficientPageParam conversionCoefficientPageParam) {
        QueryWrapper<ConversionCoefficient> queryWrapper = new QueryWrapper<ConversionCoefficient>().checkSqlInjection();
        if(ObjectUtil.isNotEmpty(conversionCoefficientPageParam.getJobInformation())) {
            queryWrapper.lambda().like(ConversionCoefficient::getJobInformation, conversionCoefficientPageParam.getJobInformation());
        }
        if(ObjectUtil.isNotEmpty(conversionCoefficientPageParam.getStartTime()) && ObjectUtil.isNotEmpty(conversionCoefficientPageParam.getEndTime())) {
            queryWrapper.lambda().between(ConversionCoefficient::getTime, conversionCoefficientPageParam.getStartTime(), conversionCoefficientPageParam.getEndTime());
        }
        if(ObjectUtil.isAllNotEmpty(conversionCoefficientPageParam.getSortField(), conversionCoefficientPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(conversionCoefficientPageParam.getSortOrder());
            queryWrapper.orderBy(true, conversionCoefficientPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(conversionCoefficientPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(ConversionCoefficient::getObjectRrn);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(ConversionCoefficientAddParam conversionCoefficientAddParam) {
        ConversionCoefficient conversionCoefficient = BeanUtil.toBean(conversionCoefficientAddParam, ConversionCoefficient.class);
        this.save(conversionCoefficient);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(ConversionCoefficientEditParam conversionCoefficientEditParam) {
        ConversionCoefficient conversionCoefficient = this.queryEntity(conversionCoefficientEditParam.getObjectRrn());
        BeanUtil.copyProperties(conversionCoefficientEditParam, conversionCoefficient);
        this.updateById(conversionCoefficient);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<ConversionCoefficientIdParam> conversionCoefficientIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(conversionCoefficientIdParamList, ConversionCoefficientIdParam::getObjectRrn));
    }

    @Override
    public ConversionCoefficient detail(ConversionCoefficientIdParam conversionCoefficientIdParam) {
        return this.queryEntity(conversionCoefficientIdParam.getObjectRrn());
    }

    @Override
    public ConversionCoefficient queryEntity(String id) {
        ConversionCoefficient conversionCoefficient = this.getById(id);
        if(ObjectUtil.isEmpty(conversionCoefficient)) {
            throw new CommonException("折合系数不存在，id值为：{}", id);
        }
        return conversionCoefficient;
    }

    @Override
    public List<ConversionCoefficient> obtainAllCompositeCoefficients() {
        List<ConversionCoefficient> conversionCoefficients = this.list();

        return conversionCoefficients;
    }

    @Override
    public void exportFoldNumber(List<ConversionCoefficientIdParam> paramList, HttpServletResponse response) throws IOException {
        System.out.println(paramList);
        List<String> index_str = new ArrayList<String>();
        for (ConversionCoefficientIdParam param : paramList) {
            index_str.add(param.getObjectRrn());
        }
        List<ConversionCoefficient> coefficientList = this.listByIds(index_str);
        System.out.println(coefficientList.toString());


//        开始写出文件
        File tempFile = null;
        try {
            QueryWrapper<ConversionCoefficient> queryWrapper = new QueryWrapper<ConversionCoefficient>().checkSqlInjection();

            String fileName = "折合数据清单.xlsx";
            List<ConversionCoefficient> bizUserList = this.list(queryWrapper);
            if(ObjectUtil.isEmpty(bizUserList)) {
                throw new CommonException("无数据可导出");
            }

            bizUserList = coefficientList;
            //
            List<ConversionCoefficientExportResult> bizUserExportResultList = bizUserList.stream()
                    .map(bizUser -> {
                        ConversionCoefficientExportResult bizUserExportResult = new ConversionCoefficientExportResult();
                        BeanUtil.copyProperties(bizUser, bizUserExportResult);
              return bizUserExportResult;
                    }).collect(Collectors.toList());
            // 创建临时文件
            tempFile = FileUtil.file(FileUtil.getTmpDir() + FileUtil.FILE_SEPARATOR + fileName);

            // 头的策略
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setFontHeightInPoints((short) 14);
            headWriteCellStyle.setWriteFont(headWriteFont);
            // 水平垂直居中
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 内容的策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            // 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND 不然无法显示背景颜色.头默认了 FillPatternType所以可以不指定
            contentWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
            // 内容背景白色
            contentWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
            WriteFont contentWriteFont = new WriteFont();

            // 内容字体大小
            contentWriteFont.setFontHeightInPoints((short) 12);
            contentWriteCellStyle.setWriteFont(contentWriteFont);

            //设置边框样式，细实线
            contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
            contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
            contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
            contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);

            // 水平垂直居中
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
            contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle,
                    contentWriteCellStyle);

            // 写excel
            EasyExcel.write(tempFile.getPath(), ConversionCoefficientExportResult.class)
                    // 自定义样式
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    // 自动列宽
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    // 机构分组合并单元格
//                    .registerWriteHandler(new CommonExcelCustomMergeStrategy(bizUserExportResultList.stream().map(ConversionCoefficientExportResult::getGroupName)
//                            .collect(Collectors.toList()), 0))
                    // 设置第一行字体
                    .registerWriteHandler(new CellWriteHandler() {
                        @Override
                        public void afterCellDispose(CellWriteHandlerContext context) {
                            WriteCellData<?> cellData = context.getFirstCellData();
                            WriteCellStyle writeCellStyle = cellData.getOrCreateStyle();
                            Integer rowIndex = context.getRowIndex();
                            if(rowIndex == 0) {
                                WriteFont headWriteFont = new WriteFont();
                                headWriteFont.setFontName("宋体");
                                headWriteFont.setBold(true);
                                headWriteFont.setFontHeightInPoints((short) 16);
                                writeCellStyle.setWriteFont(headWriteFont);
                            }
                        }
                    })
                    // 设置表头行高
                    .registerWriteHandler(new AbstractRowHeightStyleStrategy() {
                        @Override
                        protected void setHeadColumnHeight(Row row, int relativeRowIndex) {
                            if(relativeRowIndex == 0) {
                                // 表头第一行
                                row.setHeightInPoints(34);
                            } else {
                                // 表头其他行
                                row.setHeightInPoints(30);
                            }
                        }
                        @Override
                        protected void setContentColumnHeight(Row row, int relativeRowIndex) {
                            // 内容行
                            row.setHeightInPoints(20);
                        }
                    })
                    .sheet("人员信息")
                    .doWrite(bizUserExportResultList);
            CommonDownloadUtil.download(tempFile, response);
        } catch (Exception e) {
            log.error(">>> 人员导出异常：", e);
            CommonResponseUtil.renderError(response, "导出失败");
        } finally {
            FileUtil.del(tempFile);
        }
    }
}
