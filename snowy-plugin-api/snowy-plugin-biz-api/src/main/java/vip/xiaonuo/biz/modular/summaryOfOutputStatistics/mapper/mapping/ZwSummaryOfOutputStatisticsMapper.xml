<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="vip.xiaonuo.biz.modular.summaryOfOutputStatistics.mapper.ZwSummaryOfOutputStatisticsMapper">

<!--selectOutputPercentage：查询产出百分比-->
<select id="getASpecifiedMonth" resultType="vip.xiaonuo.biz.modular.summaryOfOutputStatistics.param.PersonalData" >
    SELECT
        SUM( ccu.EQUIVALENT_PRODUCTION_CAPACITY ) AS productionCapacity,
        SUM( ccu.SCREWING ) AS screwing,
        SUM( ccu.PRESSURE_CAP ) AS pressureCap,
        SUM( ccu.INSTALLING_LENSES) AS installingLenses,
        SUM( ccu.FOCUSING ) AS focusing,
        SUM( ccu.COLUMN_MOUNTED_MIRROR ) AS columnMountedMirror,
        SUM( ccu.SOLDERING_WIRE ) AS solderingWire,
        SUM( ccu.GLUE_REPLENISHMENT ) AS glueReplenishment,
        SUM( ccu.ADJUSTING_A_CHARACTER_LINE ) AS adjustingACharacterLine,
        SUM( ccu.AGING_CABLE_TIE ) AS agingCableTie,
        SUM( ccu.FINISHED_CABLE_TIE ) AS finishedCableTie,
        SUM( ccu.SCREW_ADHESIVE_REPAIR ) AS screwAdhesiveRepair,
        SUM( ccu.AGING ) AS aging,
        SUM( ccu.SEMI_FINISHED_OPTOMETRY ) AS semiFinishedOptometry
    FROM
        zw_capacity_details ccu
    WHERE
        ccu.NAME = #{nameid}
      AND ccu.DATE > #{yearMonth}-01;
</select>
</mapper>