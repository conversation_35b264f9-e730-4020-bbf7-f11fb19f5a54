<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="vip.xiaonuo.biz.modular.outputRegistration.mapper.OutputRegistrationMapper">

<!--selectOutputPercentage：查询产出百分比-->
    <select id="getRankingData" resultType="vip.xiaonuo.biz.modular.outputRegistration.param.RankingData" >
        SELECT
            TRUNCATE(zu.total_capacity, 4) AS salary,
            zu.avatar as headimgurl,
            zu.NAME as nameId,
            user_name.NAME as name,
            position.NAME AS post
        FROM
            (
                SELECT
                    cn.NAME AS NAME,
                    SUM( cn.EQUIVALENT_PRODUCTION_CAPACITY ) AS total_capacity,
                    user_name.AVATAR AS avatar
                FROM
                    zw_capacity_details cn
                        JOIN sys_user user_name ON user_name.ACCOUNT = cn.NAME
                WHERE
                    cn.DATE > #{yearMonth}-01
                GROUP BY
                    cn.NAME,
                    user_name.AVATAR
                ORDER BY
                    total_capacity DESC
            ) zu
                JOIN sys_user user_name ON user_name.ACCOUNT = zu.NAME
                JOIN sys_position position ON user_name.POSITION_ID = position.ID;
    </select>
</mapper>