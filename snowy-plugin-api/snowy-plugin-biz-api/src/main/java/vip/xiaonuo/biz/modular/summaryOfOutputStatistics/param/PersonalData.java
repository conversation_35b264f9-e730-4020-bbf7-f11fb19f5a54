package vip.xiaonuo.biz.modular.summaryOfOutputStatistics.param;

import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
public class PersonalData {

    /** 折合总产能 */
    private Double productionCapacity;

    /** 打螺丝 */
    private Double screwing;

    /** 压帽 */
    private Double pressureCap;

    /** 装透镜 */
    private Double installingLenses;

    /** 调焦 */
    private Double focusing;

    /** 装柱镜 */
    private Double columnMountedMirror;

    /** 焊锡焊线 */
    private Double solderingWire;

    /** 补胶 */
    private Double glueReplenishment;

    /** 调一字线 */
    private Double adjustingACharacterLine;

    /** 老化扎线 */
    private Double agingCableTie;

    /** 成品扎线 */
    private Double finishedCableTie;

    /** 补螺丝胶 */
    private Double screwAdhesiveRepair;

    /** 老化 */
    private Double aging;

    /** 半成品验光 */
    private Double semiFinishedOptometry;
}
