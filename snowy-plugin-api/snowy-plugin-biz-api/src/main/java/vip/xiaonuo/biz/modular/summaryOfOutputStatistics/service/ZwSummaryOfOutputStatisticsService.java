
package vip.xiaonuo.biz.modular.summaryOfOutputStatistics.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.servlet.http.HttpServletResponse;
import vip.xiaonuo.biz.modular.summaryOfOutputStatistics.entity.ZwSummaryOfOutputStatistics;
import vip.xiaonuo.biz.modular.summaryOfOutputStatistics.param.ZwSummaryOfOutputStatisticsAddParam;
import vip.xiaonuo.biz.modular.summaryOfOutputStatistics.param.ZwSummaryOfOutputStatisticsEditParam;
import vip.xiaonuo.biz.modular.summaryOfOutputStatistics.param.ZwSummaryOfOutputStatisticsIdParam;
import vip.xiaonuo.biz.modular.summaryOfOutputStatistics.param.ZwSummaryOfOutputStatisticsPageParam;

import java.io.IOException;
import java.util.List;

/**
 * 汇总Service接口
 *
 * <AUTHOR>
 * @date  2024/06/21 11:14
 **/
public interface ZwSummaryOfOutputStatisticsService extends IService<ZwSummaryOfOutputStatistics> {

    /**
     * 获取汇总分页
     *
     * <AUTHOR>
     * @date  2024/06/21 11:14
     */
    Page<ZwSummaryOfOutputStatistics> page(ZwSummaryOfOutputStatisticsPageParam zwSummaryOfOutputStatisticsPageParam);

    /**
     * 添加汇总
     *
     * <AUTHOR>
     * @date  2024/06/21 11:14
     */
    void add(ZwSummaryOfOutputStatisticsAddParam zwSummaryOfOutputStatisticsAddParam);

    /**
     * 编辑汇总
     *
     * <AUTHOR>
     * @date  2024/06/21 11:14
     */
    void edit(ZwSummaryOfOutputStatisticsEditParam zwSummaryOfOutputStatisticsEditParam);

    /**
     * 删除汇总
     *
     * <AUTHOR>
     * @date  2024/06/21 11:14
     */
    void delete(List<ZwSummaryOfOutputStatisticsIdParam> zwSummaryOfOutputStatisticsIdParamList);

    /**
     * 获取汇总详情
     *
     * <AUTHOR>
     * @date  2024/06/21 11:14
     */
    ZwSummaryOfOutputStatistics detail(ZwSummaryOfOutputStatisticsIdParam zwSummaryOfOutputStatisticsIdParam);

    /**
     * 获取汇总详情
     *
     * <AUTHOR>
     * @date  2024/06/21 11:14
     **/
    ZwSummaryOfOutputStatistics queryEntity(String id);

    /**
     * 计算指定工号和日期的产能
     * @param name_id 用户号
     * @param yearMonth 日期 2024-06
     */
    void  CalculateTheSpecifiedJobNumberProductionCapacity(String name_id,String yearMonth);

    /**
     * 重新计算所有工号的产能
     * @param yearMonth 日期
     * @return 计算的次数
     */
    int  RecalculateAllJobNumbersCapacity(String yearMonth);

    /**
     * 导出汇总
     * @param summaryOfOutputStatisticsIdParam 汇总id列表
     * @param response 响应
     */
    void exportFoldNumber(List<ZwSummaryOfOutputStatisticsIdParam> summaryOfOutputStatisticsIdParam, HttpServletResponse response)throws IOException;
}
