
package vip.xiaonuo.biz.modular.conversionCoefficient.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * 折合系数Id参数
 *
 * <AUTHOR>
 * @date  2024/06/17 15:44
 **/
@Getter
@Setter
public class ConversionCoefficientIdParam {

    /** 唯一主键 */
    @Schema(description = "唯一主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "objectRrn不能为空")
    private String objectRrn;
}
