package vip.xiaonuo.choiceway.modular.batch.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * MES批次查询参数
 *
 * <AUTHOR>
 * @date 2025/07/23
 **/
@Getter
@Setter
public class MesBatchPageParam {

    /** 当前页 */
    @Schema(description = "当前页码")
    private Integer current;

    /** 每页条数 */
    @Schema(description = "每页条数")
    private Integer size;

    /** 排序字段 */
    @Schema(description = "排序字段，字段驼峰名称，如：userName")
    private String sortField;

    /** 排序方式 */
    @Schema(description = "排序方式，升序：ASCEND；降序：DESCEND")
    private String sortOrder;

    /** 关键词 */
    @Schema(description = "关键词")
    private String searchKey;

    /** 批次ID */
    @Schema(description = "批次ID")
    private String lotId;

    /** 产品名称 */
    @Schema(description = "产品名称")
    private String productName;

    /** 当前站点ID */
    @Schema(description = "当前站点ID")
    private String currentStationId;

    /** 批次状态 */
    @Schema(description = "批次状态")
    private String status;

    /** 流程ID */
    @Schema(description = "流程ID")
    private String processId;
}
