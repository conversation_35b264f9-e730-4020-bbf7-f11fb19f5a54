package vip.xiaonuo.choiceway.modular.workorder.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * MES工单ID参数
 *
 * <AUTHOR>
 * @date 2025/07/23
 **/
@Getter
@Setter
public class MesWorkOrderIdParam {

    /** 工单ID */
    @Schema(description = "工单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "工单ID不能为空")
    private String id;
}
