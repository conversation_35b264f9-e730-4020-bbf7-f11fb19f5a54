package vip.xiaonuo.choiceway.modular.scrap.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import vip.xiaonuo.common.pojo.CommonEntity;

/**
 * MES批次报废记录实体
 *
 * <AUTHOR>
 * @date 2025/07/25
 **/
@Getter
@Setter
@TableName("mes_scrap_record")
public class MesScrapRecord extends CommonEntity {

    /** 主键ID */
    @TableId
    @Schema(description = "主键ID")
    private String id;

    /** 批次ID（数据库主键） */
    @Schema(description = "批次ID（数据库主键）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String batchId;

    /** 批次LOT ID（业务批次ID） */
    @Schema(description = "批次LOT ID（业务批次ID）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String lotId;

    /** 操作类型：ENTER-进站，EXIT-出站 */
    @Schema(description = "操作类型：ENTER-进站，EXIT-出站", requiredMode = Schema.RequiredMode.REQUIRED)
    private String operationType;

    /** 站点ID */
    @Schema(description = "站点ID")
    private String stationId;

    /** 站点名称 */
    @Schema(description = "站点名称")
    private String stationName;

    /** 原始颗粒数（数据库中的数量） */
    @Schema(description = "原始颗粒数（数据库中的数量）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer originalCount;

    /** 输入颗粒数（用户传入的数量） */
    @Schema(description = "输入颗粒数（用户传入的数量）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer inputCount;

    /** 变化数量（输入数量-原始数量，正数为增加，负数为减少） */
    @Schema(description = "变化数量（输入数量-原始数量，正数为增加，负数为减少）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer changeCount;

    /** 报废原因/变化原因 */
    @Schema(description = "报废原因/变化原因")
    private String scrapReason;

    /** 操作人ID */
    @Schema(description = "操作人ID")
    private String operatorId;

    /** 操作人姓名 */
    @Schema(description = "操作人姓名")
    private String operatorName;

    /** 操作时间 */
    @Schema(description = "操作时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Date operationTime;

    /** 备注 */
    @Schema(description = "备注")
    private String remarks;

    /** 删除标志 */
    @Schema(description = "删除标志")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String deleteFlag;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 创建人 */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 更新时间 */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /** 更新人 */
    @Schema(description = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
}
