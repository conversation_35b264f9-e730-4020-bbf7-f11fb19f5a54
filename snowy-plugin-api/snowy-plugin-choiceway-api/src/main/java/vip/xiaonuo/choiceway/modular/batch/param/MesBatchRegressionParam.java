package vip.xiaonuo.choiceway.modular.batch.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * MES批次退步参数
 *
 * <AUTHOR>
 * @date 2025/08/05
 **/
@Getter
@Setter
public class MesBatchRegressionParam {

    /** 批次LOT ID列表 */
    @Schema(description = "批次LOT ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "批次LOT ID列表不能为空")
    @JsonProperty("LOT_IDS")
    private List<String> LOT_IDS;

    /** 备注 */
    @Schema(description = "备注")
    @JsonProperty("REMARKS")
    private String REMARKS;
}
