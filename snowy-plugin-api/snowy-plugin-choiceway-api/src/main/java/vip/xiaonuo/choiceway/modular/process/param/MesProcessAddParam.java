package vip.xiaonuo.choiceway.modular.process.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * MES流程添加参数
 *
 * <AUTHOR>
 * @date 2025/07/23
 **/
@Getter
@Setter
public class MesProcessAddParam {

    /** 流程名称 */
    @Schema(description = "流程名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "流程名称不能为空")
    private String processName;

    /** 流程描述 */
    @Schema(description = "流程描述")
    private String description;

    /** 流程配置JSON */
    @Schema(description = "流程配置JSON", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "流程配置不能为空")
    private String processConfig;

    /** 是否启用 */
    @Schema(description = "是否启用")
    private String enabled;

    /** 状态 */
    @Schema(description = "状态")
    private String status;

    /** 排序码 */
    @Schema(description = "排序码")
    private Integer sortCode;
}
