package vip.xiaonuo.choiceway.modular.batch.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * MES批次出站参数
 *
 * <AUTHOR>
 * @date 2025/07/23
 **/
@Getter
@Setter
public class MesBatchExitParam {

    /** 批次ID */
    @Schema(description = "批次ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "批次ID不能为空")
    private String batchId;

    /** 站点ID */
    @Schema(description = "站点ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "站点ID不能为空")
    private String stationId;

    /** 站点名称 */
    @Schema(description = "站点名称")
    private String stationName;

    /** 备注 */
    @Schema(description = "备注")
    private String remark;
}
