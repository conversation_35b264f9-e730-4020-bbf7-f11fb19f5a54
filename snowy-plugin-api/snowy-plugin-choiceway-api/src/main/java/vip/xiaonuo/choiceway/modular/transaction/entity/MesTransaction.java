package vip.xiaonuo.choiceway.modular.transaction.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import vip.xiaonuo.common.pojo.CommonEntity;

/**
 * MES事务记录实体
 *
 * <AUTHOR>
 * @date 2025/07/23
 **/
@Getter
@Setter
@TableName("mes_transaction")
public class MesTransaction extends CommonEntity {

    /** 事务ID */
    @TableId
    @Schema(description = "事务ID")
    private String id;

    /** 批次ID */
    @Schema(description = "批次ID")
    private String batchId;

    /** 批次LOT ID */
    @Schema(description = "批次LOT ID")
    private String lotId;

    /** 颗粒数 */
    @Schema(description = "颗粒数")
    private Integer particleCount;

    /** 工单ID */
    @Schema(description = "工单ID")
    private String workOrderId;

    /** 操作类型：FEED-投料，ENTER-进站，EXIT-出站，WAREHOUSE-入库 */
    @Schema(description = "操作类型")
    private String operationType;

    /** 操作描述 */
    @Schema(description = "操作描述")
    private String operationDesc;

    /** 站点ID */
    @Schema(description = "站点ID")
    private String stationId;

    /** 站点名称 */
    @Schema(description = "站点名称")
    private String stationName;

    /** 操作前状态 */
    @Schema(description = "操作前状态")
    private String beforeStatus;

    /** 操作后状态 */
    @Schema(description = "操作后状态")
    private String afterStatus;

    /** 操作时间 */
    @Schema(description = "操作时间")
    private String operationTime;

    /** 操作人员 */
    @Schema(description = "操作人员")
    private String operator;

    /** 备注 */
    @Schema(description = "备注")
    private String remark;

    /** 排序码 */
    @Schema(description = "排序码")
    private Integer sortCode;
}
