package vip.xiaonuo.choiceway.modular.batch.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.choiceway.modular.batch.entity.MesBatch;
import vip.xiaonuo.choiceway.modular.batch.param.*;

import java.util.List;

/**
 * MES批次Service接口
 *
 * <AUTHOR>
 * @date 2025/07/23
 **/
public interface MesBatchService extends IService<MesBatch> {

    /**
     * 获取批次分页
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    Page<MesBatch> page(MesBatchPageParam mesBatchPageParam);

    /**
     * 添加批次
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void add(MesBatchAddParam mesBatchAddParam);

    /**
     * 编辑批次（只能编辑颗粒数）
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void edit(MesBatchEditParam mesBatchEditParam);

    /**
     * 删除批次
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void delete(List<MesBatchIdParam> mesBatchIdParamList);

    /**
     * 投料生成批次
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    String feedMaterial(MesBatchFeedParam mesBatchFeedParam);

    /**
     * 批次进站
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void enterStation(MesBatchEnterParam mesBatchEnterParam);

    /**
     * 批次出站
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void exitStation(MesBatchExitParam mesBatchExitParam);

    /**
     * 批次进站（根据LotId）
     *
     * <AUTHOR>
     * @date 2025/07/25
     */
    void enterStationByLotId(MesBatchEnterLotIdParam mesBatchEnterLotIdParam);

    /**
     * 批次出站（根据LotId）
     *
     * <AUTHOR>
     * @date 2025/07/25
     */
    void exitStationByLotId(MesBatchExitLotIdParam mesBatchExitLotIdParam);

    /**
     * 批次入库
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void warehouse(MesBatchWarehouseParam mesBatchWarehouseParam);

    /**
     * 根据批次ID获取批次信息
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    MesBatch getBatchById(String batchId);

    /**
     * 根据批次ID列表获取批次信息
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    List<MesBatch> getBatchListByIds(List<String> batchIds);

    /**
     * 获取批次详情
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    MesBatch detail(MesBatchIdParam mesBatchIdParam);

    /**
     * 获取批次详情
     *
     * <AUTHOR>
     * @date 2025/07/23
     **/
    MesBatch queryEntity(String id);

    /**
     * 获取当前站点的待进站批次
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    List<MesBatch> getWaitingBatchesByStation(String stationId);

    /**
     * 获取当前站点的运行中批次
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    List<MesBatch> getRunningBatchesByStation(String stationId);

    /**
     * 批次退步
     *
     * <AUTHOR>
     * @date 2025/08/05
     */
    void regression(MesBatchRegressionParam mesBatchRegressionParam);
}
