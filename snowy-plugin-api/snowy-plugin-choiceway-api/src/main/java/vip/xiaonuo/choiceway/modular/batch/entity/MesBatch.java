package vip.xiaonuo.choiceway.modular.batch.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import vip.xiaonuo.common.pojo.CommonEntity;

/**
 * MES批次实体
 *
 * <AUTHOR>
 * @date 2025/07/23
 **/
@Getter
@Setter
@TableName("mes_batch")
public class MesBatch extends CommonEntity {

    /** 主键ID */
    @TableId
    @Schema(description = "主键ID")
    private String id;

    /** 批次ID */
    @Schema(description = "批次ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String lotId;

    /** 产品名称 */
    @Schema(description = "产品名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productName;

    /** 当前站点ID */
    @Schema(description = "当前站点ID")
    private String currentStationId;

    /** 当前站点名称 */
    @Schema(description = "当前站点名称")
    private String currentStationName;

    /** 流程ID */
    @Schema(description = "流程ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String processId;

    /** 流程名称 */
    @Schema(description = "流程名称")
    private String processName;

    /** 批次状态：WAIT-等待，RUN-运行中 */
    @Schema(description = "批次状态")
    private String status;

    /** 颗粒数 */
    @Schema(description = "颗粒数")
    private Integer particleCount;

    /** 备注 */
    @Schema(description = "备注")
    private String remarks;

    /** 删除标志 */
    @Schema(description = "删除标志")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String deleteFlag;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 创建人 */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 更新时间 */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /** 更新人 */
    @Schema(description = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
}
