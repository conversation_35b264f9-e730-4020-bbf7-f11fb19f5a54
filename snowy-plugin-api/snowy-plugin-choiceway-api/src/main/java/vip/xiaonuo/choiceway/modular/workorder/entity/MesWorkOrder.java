package vip.xiaonuo.choiceway.modular.workorder.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import vip.xiaonuo.common.pojo.CommonEntity;

/**
 * MES工单实体
 *
 * <AUTHOR>
 * @date 2025/07/23
 **/
@Getter
@Setter
@TableName("mes_work_order")
public class MesWorkOrder extends CommonEntity {

    /** 工单ID */
    @TableId
    @Schema(description = "工单ID")
    private String id;

    /** SN码 */
    @Schema(description = "SN码")
    private String snCode;

    /** 产品名称 */
    @Schema(description = "产品名称")
    private String productName;

    /** 工单备注 */
    @Schema(description = "工单备注")
    private String remark;

    /** 流程ID */
    @Schema(description = "流程ID")
    private String processId;

    /** 流程名称 */
    @Schema(description = "流程名称")
    private String processName;

    /** 流程版本 */
    @Schema(description = "流程版本")
    private String processVersion;

    /** 工单状态 */
    @Schema(description = "工单状态")
    private String status;

    /** 排序码 */
    @Schema(description = "排序码")
    private Integer sortCode;
}
