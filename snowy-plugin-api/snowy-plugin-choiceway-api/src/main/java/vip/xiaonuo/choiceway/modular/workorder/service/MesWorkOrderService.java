package vip.xiaonuo.choiceway.modular.workorder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.choiceway.modular.workorder.entity.MesWorkOrder;
import vip.xiaonuo.choiceway.modular.workorder.param.*;

import java.util.List;

/**
 * MES工单Service接口
 *
 * <AUTHOR>
 * @date 2025/07/23
 **/
public interface MesWorkOrderService extends IService<MesWorkOrder> {

    /**
     * 获取工单分页
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    Page<MesWorkOrder> page(MesWorkOrderPageParam mesWorkOrderPageParam);

    /**
     * 添加工单
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void add(MesWorkOrderAddParam mesWorkOrderAddParam);

    /**
     * 编辑工单
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void edit(MesWorkOrderEditParam mesWorkOrderEditParam);

    /**
     * 删除工单
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void delete(List<MesWorkOrderIdParam> mesWorkOrderIdParamList);

    /**
     * 获取工单详情
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    MesWorkOrder detail(MesWorkOrderIdParam mesWorkOrderIdParam);

    /**
     * 获取工单详情
     *
     * <AUTHOR>
     * @date 2025/07/23
     **/
    MesWorkOrder queryEntity(String id);

    /**
     * 绑定物料
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void bindMaterial(MesWorkOrderBindMaterialParam mesWorkOrderBindMaterialParam);

    /**
     * 解绑物料
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void unbindMaterial(MesWorkOrderUnbindMaterialParam mesWorkOrderUnbindMaterialParam);

    /**
     * 获取工单绑定的物料列表
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    List<Object> getWorkOrderMaterials(MesWorkOrderIdParam mesWorkOrderIdParam);
}
