package vip.xiaonuo.choiceway.modular.process.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.choiceway.modular.process.entity.MesProcessList;
import vip.xiaonuo.choiceway.modular.process.param.*;

import java.util.List;

/**
 * MES流程节点Service接口
 *
 * <AUTHOR>
 * @date 2025/07/23
 **/
public interface MesProcessListService extends IService<MesProcessList> {

    /**
     * 获取流程节点分页
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    Page<MesProcessList> page(MesProcessListPageParam mesProcessListPageParam);

    /**
     * 添加流程节点
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void add(MesProcessListAddParam mesProcessListAddParam);

    /**
     * 编辑流程节点
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void edit(MesProcessListEditParam mesProcessListEditParam);

    /**
     * 删除流程节点
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void delete(List<MesProcessListIdParam> mesProcessListIdParamList);

    /**
     * 获取流程节点详情
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    MesProcessList detail(MesProcessListIdParam mesProcessListIdParam);

    /**
     * 获取流程节点详情
     *
     * <AUTHOR>
     * @date 2025/07/23
     **/
    MesProcessList queryEntity(String id);

    /**
     * 根据流程ID获取节点列表
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    List<MesProcessList> getNodesByProcessId(String processId);

    /**
     * 批量保存流程节点
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void batchSaveNodes(String processId, String processName, String processVersion, List<MesProcessListAddParam> nodeList);

    /**
     * 删除流程的所有节点
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void deleteNodesByProcessId(String processId);
}
