<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>vip.xiaonuo</groupId>
        <artifactId>snowy-plugin-api</artifactId>
        <version>3.0.0</version>
    </parent>

    <artifactId>snowy-plugin-dev-api</artifactId>
    <packaging>jar</packaging>
    <description>开发工具插件api接口</description>

    <dependencies>
        <!-- 每个插件接口都要引入common -->
        <dependency>
            <groupId>vip.xiaonuo</groupId>
            <artifactId>snowy-common</artifactId>
        </dependency>

        <!--x-file-storage文件sdk-->
        <dependency>
            <groupId>org.dromara.x-file-storage</groupId>
            <artifactId>x-file-storage-spring</artifactId>
        </dependency>

        <!--腾讯云文件sdk-->
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
        </dependency>

        <!--阿里云文件sdk-->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>

        <!--minio文件sdk-->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
        </dependency>

        <!--java邮件sdk-->
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
        </dependency>

        <!--阿里云邮件sdk-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dm20151123</artifactId>
        </dependency>

        <!-- 腾讯云邮件sdk -->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-ses</artifactId>
        </dependency>

        <!--阿里云短信sdk-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dysmsapi20170525</artifactId>
        </dependency>

        <!--腾讯云短信sdk-->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-sms</artifactId>
        </dependency>

        <!-- sms4j短信sdk -->
        <dependency>
            <groupId>org.dromara.sms4j</groupId>
            <artifactId>sms4j-javase-plugin</artifactId>
        </dependency>

        <!--系统硬件信息-->
        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
        </dependency>
    </dependencies>
</project>