package vip.xiaonuo.choiceway.modular.batch.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.choiceway.modular.batch.entity.MesBatch;
import vip.xiaonuo.choiceway.modular.batch.param.*;
import vip.xiaonuo.choiceway.modular.batch.service.MesBatchService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * MES批次控制器
 *
 * <AUTHOR>
 * @date 2025/07/23
 */
@Tag(name = "MES批次控制器")
@RestController
@Validated
public class MesBatchController {

    @Resource
    private MesBatchService mesBatchService;

    /**
     * 获取批次分页
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "获取批次分页")
    @SaCheckPermission("/choiceway/batch/page")
    @GetMapping("/choiceway/batch/page")
    public CommonResult<Page<MesBatch>> page(MesBatchPageParam mesBatchPageParam) {
        return CommonResult.data(mesBatchService.page(mesBatchPageParam));
    }

    /**
     * 添加批次
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "添加批次")
    @CommonLog("添加批次")
    @SaCheckPermission("/choiceway/batch/add")
    @PostMapping("/choiceway/batch/add")
    public CommonResult<String> add(@RequestBody @Valid MesBatchAddParam mesBatchAddParam) {
        mesBatchService.add(mesBatchAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑批次
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "编辑批次")
    @CommonLog("编辑批次")
    @SaCheckPermission("/choiceway/batch/edit")
    @PostMapping("/choiceway/batch/edit")
    public CommonResult<String> edit(@RequestBody @Valid MesBatchEditParam mesBatchEditParam) {
        mesBatchService.edit(mesBatchEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除批次
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "删除批次")
    @CommonLog("删除批次")
    @SaCheckPermission("/choiceway/batch/delete")
    @PostMapping("/choiceway/batch/delete")
    public CommonResult<String> delete(@RequestBody @Valid List<MesBatchIdParam> mesBatchIdParamList) {
        mesBatchService.delete(mesBatchIdParamList);
        return CommonResult.ok();
    }

    /**
     * 投料生成批次
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "投料生成批次")
    @CommonLog("投料生成批次")
    @SaCheckPermission("/choiceway/batch/feed")
    @PostMapping("/choiceway/batch/feed")
    public CommonResult<String> feedMaterial(@RequestBody @Valid MesBatchFeedParam mesBatchFeedParam) {
        String batchId = mesBatchService.feedMaterial(mesBatchFeedParam);
        return CommonResult.data(batchId);
    }

    /**
     * 批次进站
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "批次进站")
    @CommonLog("批次进站")
    @SaCheckPermission("/choiceway/batch/enter")
    @PostMapping("/choiceway/batch/enter")
    public CommonResult<String> enterStation(@RequestBody @Valid MesBatchEnterParam mesBatchEnterParam) {
        mesBatchService.enterStation(mesBatchEnterParam);
        return CommonResult.ok();
    }

    /**
     * 批次出站
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "批次出站")
    @CommonLog("批次出站")
    @SaCheckPermission("/choiceway/batch/exit")
    @PostMapping("/choiceway/batch/exit")
    public CommonResult<String> exitStation(@RequestBody @Valid MesBatchExitParam mesBatchExitParam) {
        mesBatchService.exitStation(mesBatchExitParam);
        return CommonResult.ok();
    }

    /**
     * 批次进站（根据LotId）
     *
     * <AUTHOR>
     * @date 2025/07/25
     */
    @Operation(summary = "批次进站（根据LotId）")
    @CommonLog("批次进站（根据LotId）")
    @SaCheckPermission("/choiceway/batch/enterLotID")
    @PostMapping("/choiceway/batch/enterLotID")
    public CommonResult<String> enterStationByLotId(@RequestBody @Valid MesBatchEnterLotIdParam mesBatchEnterLotIdParam) {
        mesBatchService.enterStationByLotId(mesBatchEnterLotIdParam);
        return CommonResult.ok();
    }

    /**
     * 批次出站（根据LotId）
     *
     * <AUTHOR>
     * @date 2025/07/25
     */
    @Operation(summary = "批次出站（根据LotId）")
    @CommonLog("批次出站（根据LotId）")
    @SaCheckPermission("/choiceway/batch/exitLotID")
    @PostMapping("/choiceway/batch/exitLotID")
    public CommonResult<String> exitStationByLotId(@RequestBody @Valid MesBatchExitLotIdParam mesBatchExitLotIdParam) {
        mesBatchService.exitStationByLotId(mesBatchExitLotIdParam);
        return CommonResult.ok();
    }

    /**
     * 批次入库
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "批次入库")
    @CommonLog("批次入库")
    @SaCheckPermission("/choiceway/batch/warehouse")
    @PostMapping("/choiceway/batch/warehouse")
    public CommonResult<String> warehouse(@RequestBody @Valid MesBatchWarehouseParam mesBatchWarehouseParam) {
        mesBatchService.warehouse(mesBatchWarehouseParam);
        return CommonResult.ok();
    }

    /**
     * 根据批次ID获取批次信息
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "根据批次ID获取批次信息")
    @SaCheckPermission("/choiceway/batch/getBatchById")
    @GetMapping("/choiceway/batch/getBatchById")
    public CommonResult<MesBatch> getBatchById(@RequestParam String batchId) {
        return CommonResult.data(mesBatchService.getBatchById(batchId));
    }

    /**
     * 根据批次ID列表获取批次信息
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "根据批次ID列表获取批次信息")
    @SaCheckPermission("/choiceway/batch/getBatchListByIds")
    @PostMapping("/choiceway/batch/getBatchListByIds")
    public CommonResult<List<MesBatch>> getBatchListByIds(@RequestBody List<String> batchIds) {
        return CommonResult.data(mesBatchService.getBatchListByIds(batchIds));
    }

    /**
     * 获取批次详情
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "获取批次详情")
    @SaCheckPermission("/choiceway/batch/detail")
    @GetMapping("/choiceway/batch/detail")
    public CommonResult<MesBatch> detail(@Valid MesBatchIdParam mesBatchIdParam) {
        return CommonResult.data(mesBatchService.detail(mesBatchIdParam));
    }

    /**
     * 获取当前站点的待进站批次
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "获取当前站点的待进站批次")
    @SaCheckPermission("/choiceway/batch/waitingBatches")
    @GetMapping("/choiceway/batch/waitingBatches")
    public CommonResult<List<MesBatch>> getWaitingBatchesByStation(@RequestParam String stationId) {
        return CommonResult.data(mesBatchService.getWaitingBatchesByStation(stationId));
    }

    /**
     * 获取当前站点的运行中批次
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "获取当前站点的运行中批次")
    @SaCheckPermission("/choiceway/batch/runningBatches")
    @GetMapping("/choiceway/batch/runningBatches")
    public CommonResult<List<MesBatch>> getRunningBatchesByStation(@RequestParam String stationId) {
        return CommonResult.data(mesBatchService.getRunningBatchesByStation(stationId));
    }

    /**
     * 批次退步
     *
     * <AUTHOR>
     * @date 2025/08/05
     */
    @Operation(summary = "批次退步")
    @CommonLog("批次退步")
    @SaCheckPermission("/choiceway/batch/regression")
    @PostMapping("/choiceway/batch/regression")
    public CommonResult<String> regression(@RequestBody @Valid MesBatchRegressionParam mesBatchRegressionParam) {
        System.out.println("=== 控制器接收到退步请求 ===");
        System.out.println("参数对象: " + mesBatchRegressionParam);
        System.out.println("LOT_IDS: " + (mesBatchRegressionParam != null ? mesBatchRegressionParam.getLOT_IDS() : "参数对象为null"));
        System.out.println("REMARKS: " + (mesBatchRegressionParam != null ? mesBatchRegressionParam.getREMARKS() : "参数对象为null"));

        mesBatchService.regression(mesBatchRegressionParam);
        return CommonResult.ok();
    }

}
