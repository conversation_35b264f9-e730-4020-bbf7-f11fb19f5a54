package vip.xiaonuo.choiceway.modular.workorder.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.choiceway.modular.workorder.entity.MesWorkOrder;
import vip.xiaonuo.choiceway.modular.workorder.param.*;
import vip.xiaonuo.choiceway.modular.workorder.service.MesWorkOrderService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * MES工单控制器
 *
 * <AUTHOR>
 * @date 2025/07/23
 */
@Tag(name = "MES工单控制器")
@RestController
@Validated
public class MesWorkOrderController {

    @Resource
    private MesWorkOrderService mesWorkOrderService;

    /**
     * 获取工单分页
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "获取工单分页")
    @SaCheckPermission("/choiceway/workOrder/page")
    @GetMapping("/choiceway/workOrder/page")
    public CommonResult<Page<MesWorkOrder>> page(MesWorkOrderPageParam mesWorkOrderPageParam) {
        return CommonResult.data(mesWorkOrderService.page(mesWorkOrderPageParam));
    }

    /**
     * 添加工单
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "添加工单")
    @CommonLog("添加工单")
    @SaCheckPermission("/choiceway/workOrder/add")
    @PostMapping("/choiceway/workOrder/add")
    public CommonResult<String> add(@RequestBody @Valid MesWorkOrderAddParam mesWorkOrderAddParam) {
        mesWorkOrderService.add(mesWorkOrderAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑工单
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "编辑工单")
    @CommonLog("编辑工单")
    @SaCheckPermission("/choiceway/workOrder/edit")
    @PostMapping("/choiceway/workOrder/edit")
    public CommonResult<String> edit(@RequestBody @Valid MesWorkOrderEditParam mesWorkOrderEditParam) {
        mesWorkOrderService.edit(mesWorkOrderEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除工单
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "删除工单")
    @CommonLog("删除工单")
    @SaCheckPermission("/choiceway/workOrder/delete")
    @PostMapping("/choiceway/workOrder/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                List<MesWorkOrderIdParam> mesWorkOrderIdParamList) {
        mesWorkOrderService.delete(mesWorkOrderIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取工单详情
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "获取工单详情")
    @SaCheckPermission("/choiceway/workOrder/detail")
    @GetMapping("/choiceway/workOrder/detail")
    public CommonResult<MesWorkOrder> detail(@Valid MesWorkOrderIdParam mesWorkOrderIdParam) {
        return CommonResult.data(mesWorkOrderService.detail(mesWorkOrderIdParam));
    }

    /**
     * 绑定物料
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "绑定物料")
    @CommonLog("绑定物料")
    @SaCheckPermission("/choiceway/workOrder/bindMaterial")
    @PostMapping("/choiceway/workOrder/bindMaterial")
    public CommonResult<String> bindMaterial(@RequestBody @Valid MesWorkOrderBindMaterialParam mesWorkOrderBindMaterialParam) {
        mesWorkOrderService.bindMaterial(mesWorkOrderBindMaterialParam);
        return CommonResult.ok();
    }

    /**
     * 解绑物料
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "解绑物料")
    @CommonLog("解绑物料")
    @SaCheckPermission("/choiceway/workOrder/unbindMaterial")
    @PostMapping("/choiceway/workOrder/unbindMaterial")
    public CommonResult<String> unbindMaterial(@RequestBody @Valid MesWorkOrderUnbindMaterialParam mesWorkOrderUnbindMaterialParam) {
        mesWorkOrderService.unbindMaterial(mesWorkOrderUnbindMaterialParam);
        return CommonResult.ok();
    }

    /**
     * 获取工单绑定的物料列表
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "获取工单绑定的物料列表")
    @SaCheckPermission("/choiceway/workOrder/materials")
    @GetMapping("/choiceway/workOrder/materials")
    public CommonResult<List<Object>> getWorkOrderMaterials(@Valid MesWorkOrderIdParam mesWorkOrderIdParam) {
        return CommonResult.data(mesWorkOrderService.getWorkOrderMaterials(mesWorkOrderIdParam));
    }
}
