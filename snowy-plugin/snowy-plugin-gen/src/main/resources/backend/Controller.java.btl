
package ${packageName}.${moduleName}.modular.${busName}.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import ${packageName}.common.annotation.CommonLog;
import ${packageName}.common.pojo.CommonResult;
import ${packageName}.${moduleName}.modular.${busName}.entity.${className};
import ${packageName}.${moduleName}.modular.${busName}.param.${className}AddParam;
import ${packageName}.${moduleName}.modular.${busName}.param.${className}EditParam;
import ${packageName}.${moduleName}.modular.${busName}.param.${className}IdParam;
import ${packageName}.${moduleName}.modular.${busName}.param.${className}PageParam;
import ${packageName}.${moduleName}.modular.${busName}.service.${className}Service;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * ${functionName}控制器
 *
 * <AUTHOR>
 * @date ${genTime}
 */
@Tag(name = "${functionName}控制器")
@RestController
@Validated
public class ${className}Controller {

    @Resource
    private ${className}Service ${classNameFirstLower}Service;

    /**
     * 获取${functionName}分页
     *
     * <AUTHOR>
     * @date ${genTime}
     */
    @Operation(summary = "获取${functionName}分页")
    @SaCheckPermission("/${moduleName}/${busName}/page")
    @GetMapping("/${moduleName}/${busName}/page")
    public CommonResult<Page<${className}>> page(${className}PageParam ${classNameFirstLower}PageParam) {
        return CommonResult.data(${classNameFirstLower}Service.page(${classNameFirstLower}PageParam));
    }

    /**
     * 添加${functionName}
     *
     * <AUTHOR>
     * @date ${genTime}
     */
    @Operation(summary = "添加${functionName}")
    @CommonLog("添加${functionName}")
    @SaCheckPermission("/${moduleName}/${busName}/add")
    @PostMapping("/${moduleName}/${busName}/add")
    public CommonResult<String> add(@RequestBody @Valid ${className}AddParam ${classNameFirstLower}AddParam) {
        ${classNameFirstLower}Service.add(${classNameFirstLower}AddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑${functionName}
     *
     * <AUTHOR>
     * @date ${genTime}
     */
    @Operation(summary = "编辑${functionName}")
    @CommonLog("编辑${functionName}")
    @SaCheckPermission("/${moduleName}/${busName}/edit")
    @PostMapping("/${moduleName}/${busName}/edit")
    public CommonResult<String> edit(@RequestBody @Valid ${className}EditParam ${classNameFirstLower}EditParam) {
        ${classNameFirstLower}Service.edit(${classNameFirstLower}EditParam);
        return CommonResult.ok();
    }

    /**
     * 删除${functionName}
     *
     * <AUTHOR>
     * @date ${genTime}
     */
    @Operation(summary = "删除${functionName}")
    @CommonLog("删除${functionName}")
    @SaCheckPermission("/${moduleName}/${busName}/delete")
    @PostMapping("/${moduleName}/${busName}/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<${className}IdParam> ${classNameFirstLower}IdParamList) {
        ${classNameFirstLower}Service.delete(${classNameFirstLower}IdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取${functionName}详情
     *
     * <AUTHOR>
     * @date ${genTime}
     */
    @Operation(summary = "获取${functionName}详情")
    @SaCheckPermission("/${moduleName}/${busName}/detail")
    @GetMapping("/${moduleName}/${busName}/detail")
    public CommonResult<${className}> detail(@Valid ${className}IdParam ${classNameFirstLower}IdParam) {
        return CommonResult.data(${classNameFirstLower}Service.detail(${classNameFirstLower}IdParam));
    }
}
