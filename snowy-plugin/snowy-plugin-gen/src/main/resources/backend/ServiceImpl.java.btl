
package ${packageName}.${moduleName}.modular.${busName}.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ${packageName}.common.enums.CommonSortOrderEnum;
import ${packageName}.common.exception.CommonException;
import ${packageName}.common.page.CommonPageRequest;
import ${packageName}.${moduleName}.modular.${busName}.entity.${className};
import ${packageName}.${moduleName}.modular.${busName}.mapper.${className}Mapper;
import ${packageName}.${moduleName}.modular.${busName}.param.${className}AddParam;
import ${packageName}.${moduleName}.modular.${busName}.param.${className}EditParam;
import ${packageName}.${moduleName}.modular.${busName}.param.${className}IdParam;
import ${packageName}.${moduleName}.modular.${busName}.param.${className}PageParam;
import ${packageName}.${moduleName}.modular.${busName}.service.${className}Service;

import java.util.List;

/**
 * ${functionName}Service接口实现类
 *
 * <AUTHOR>
 * @date ${genTime}
 **/
@Service
public class ${className}ServiceImpl extends ServiceImpl<${className}Mapper, ${className}> implements ${className}Service {

    @Override
    public Page<${className}> page(${className}PageParam ${classNameFirstLower}PageParam) {
        QueryWrapper<${className}> queryWrapper = new QueryWrapper<${className}>().checkSqlInjection();
        <% for(var i = 0; i < configList.~size; i++) { %>
        <% if(configList[i].needPage) { %>
        <% if(configList[i].effectType == 'datepicker') { %>
        if(ObjectUtil.isNotEmpty(${classNameFirstLower}PageParam.getStart${configList[i].fieldNameCamelCaseFirstUpper}()) && ObjectUtil.isNotEmpty(${classNameFirstLower}PageParam.getEnd${configList[i].fieldNameCamelCaseFirstUpper}())) {
            queryWrapper.lambda().between(${className}::get${configList[i].fieldNameCamelCaseFirstUpper}, ${classNameFirstLower}PageParam.getStart${configList[i].fieldNameCamelCaseFirstUpper}(), ${classNameFirstLower}PageParam.getEnd${configList[i].fieldNameCamelCaseFirstUpper}());
        }
        <% } else { %>
        if(ObjectUtil.isNotEmpty(${classNameFirstLower}PageParam.get${configList[i].fieldNameCamelCaseFirstUpper}())) {
            queryWrapper.lambda().${configList[i].needPageType}(${className}::get${configList[i].fieldNameCamelCaseFirstUpper}, ${classNameFirstLower}PageParam.get${configList[i].fieldNameCamelCaseFirstUpper}());
        }
        <% } %>
        <% } %>
        <% } %>
        if(ObjectUtil.isAllNotEmpty(${classNameFirstLower}PageParam.getSortField(), ${classNameFirstLower}PageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(${classNameFirstLower}PageParam.getSortOrder());
            queryWrapper.orderBy(true, ${classNameFirstLower}PageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(${classNameFirstLower}PageParam.getSortField()));
        } else {
            <% if(hasSortCodeField) { %>
            queryWrapper.lambda().orderByAsc(${className}::getSortCode);
            <% } else { %>
            queryWrapper.lambda().orderByAsc(${className}::get${dbTableKeyFirstUpper});
            <% } %>
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(${className}AddParam ${classNameFirstLower}AddParam) {
        ${className} ${classNameFirstLower} = BeanUtil.toBean(${classNameFirstLower}AddParam, ${className}.class);
        this.save(${classNameFirstLower});
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(${className}EditParam ${classNameFirstLower}EditParam) {
        <% for(var i = 0; i < configList.~size; i++) { %>
        <% if(configList[i].needTableId) { %>
        ${className} ${classNameFirstLower} = this.queryEntity(${classNameFirstLower}EditParam.get${configList[i].fieldNameCamelCaseFirstUpper}());
        <% } %>
        <% } %>
        BeanUtil.copyProperties(${classNameFirstLower}EditParam, ${classNameFirstLower});
        this.updateById(${classNameFirstLower});
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<${className}IdParam> ${classNameFirstLower}IdParamList) {
        // 执行删除
        <% for(var i = 0; i < configList.~size; i++) { %>
        <% if(configList[i].needTableId) { %>
        this.removeByIds(CollStreamUtil.toList(${classNameFirstLower}IdParamList, ${className}IdParam::get${configList[i].fieldNameCamelCaseFirstUpper}));
        <% } %>
        <% } %>
    }

    @Override
    public ${className} detail(${className}IdParam ${classNameFirstLower}IdParam) {
        <% for(var i = 0; i < configList.~size; i++) { %>
        <% if(configList[i].needTableId) { %>
        return this.queryEntity(${classNameFirstLower}IdParam.get${configList[i].fieldNameCamelCaseFirstUpper}());
        <% } %>
        <% } %>
    }

    @Override
    public ${className} queryEntity(String id) {
        ${className} ${classNameFirstLower} = this.getById(id);
        if(ObjectUtil.isEmpty(${classNameFirstLower})) {
            throw new CommonException("${functionName}不存在，id值为：{}", id);
        }
        return ${classNameFirstLower};
    }
}
